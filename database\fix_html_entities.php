<?php
/**
 * Fix HTML entities in database
 * This script will decode HTML entities that were incorrectly stored in the database
 */

require_once __DIR__ . '/../config/app.php';

echo "Starting HTML entities fix...\n";

try {
    // Fix services table
    echo "Fixing services table...\n";
    $services = $database->fetchAll("SELECT id, name, description FROM services WHERE name LIKE '%&amp;%' OR name LIKE '%&lt;%' OR name LIKE '%&gt;%' OR description LIKE '%&amp;%' OR description LIKE '%&lt;%' OR description LIKE '%&gt;%'");
    
    foreach ($services as $service) {
        $fixedName = html_entity_decode($service['name'], ENT_QUOTES, 'UTF-8');
        $fixedDescription = html_entity_decode($service['description'], ENT_QUOTES, 'UTF-8');
        
        if ($fixedName !== $service['name'] || $fixedDescription !== $service['description']) {
            $database->query(
                "UPDATE services SET name = ?, description = ? WHERE id = ?",
                [$fixedName, $fixedDescription, $service['id']]
            );
            echo "Fixed service: {$service['name']} -> {$fixedName}\n";
        }
    }
    
    // Fix faqs table
    echo "Fixing faqs table...\n";
    $faqs = $database->fetchAll("SELECT id, question, answer, service_link_text FROM faqs WHERE question LIKE '%&amp;%' OR question LIKE '%&lt;%' OR question LIKE '%&gt;%' OR answer LIKE '%&amp;%' OR answer LIKE '%&lt;%' OR answer LIKE '%&gt;%' OR service_link_text LIKE '%&amp;%' OR service_link_text LIKE '%&lt;%' OR service_link_text LIKE '%&gt;%'");
    
    foreach ($faqs as $faq) {
        $fixedQuestion = html_entity_decode($faq['question'], ENT_QUOTES, 'UTF-8');
        $fixedAnswer = html_entity_decode($faq['answer'], ENT_QUOTES, 'UTF-8');
        $fixedServiceLinkText = html_entity_decode($faq['service_link_text'] ?? '', ENT_QUOTES, 'UTF-8');
        
        if ($fixedQuestion !== $faq['question'] || $fixedAnswer !== $faq['answer'] || $fixedServiceLinkText !== ($faq['service_link_text'] ?? '')) {
            $database->query(
                "UPDATE faqs SET question = ?, answer = ?, service_link_text = ? WHERE id = ?",
                [$fixedQuestion, $fixedAnswer, $fixedServiceLinkText, $faq['id']]
            );
            echo "Fixed FAQ: {$faq['question']}\n";
        }
    }
    
    // Fix users table (names)
    echo "Fixing users table...\n";
    $users = $database->fetchAll("SELECT id, name FROM users WHERE name LIKE '%&amp;%' OR name LIKE '%&lt;%' OR name LIKE '%&gt;%'");
    
    foreach ($users as $user) {
        $fixedName = html_entity_decode($user['name'], ENT_QUOTES, 'UTF-8');
        
        if ($fixedName !== $user['name']) {
            $database->query(
                "UPDATE users SET name = ? WHERE id = ?",
                [$fixedName, $user['id']]
            );
            echo "Fixed user: {$user['name']} -> {$fixedName}\n";
        }
    }
    
    // Fix staff_schedules table (bio and role)
    echo "Fixing staff_schedules table...\n";
    $staffSchedules = $database->fetchAll("SELECT user_id, bio, role FROM staff_schedules WHERE bio LIKE '%&amp;%' OR bio LIKE '%&lt;%' OR bio LIKE '%&gt;%' OR role LIKE '%&amp;%' OR role LIKE '%&lt;%' OR role LIKE '%&gt;%'");
    
    foreach ($staffSchedules as $schedule) {
        $fixedBio = html_entity_decode($schedule['bio'] ?? '', ENT_QUOTES, 'UTF-8');
        $fixedRole = html_entity_decode($schedule['role'] ?? '', ENT_QUOTES, 'UTF-8');
        
        if ($fixedBio !== ($schedule['bio'] ?? '') || $fixedRole !== ($schedule['role'] ?? '')) {
            $database->query(
                "UPDATE staff_schedules SET bio = ?, role = ? WHERE user_id = ?",
                [$fixedBio, $fixedRole, $schedule['user_id']]
            );
            echo "Fixed staff schedule for user: {$schedule['user_id']}\n";
        }
    }
    
    // Fix bookings table (notes)
    echo "Fixing bookings table...\n";
    $bookings = $database->fetchAll("SELECT id, notes FROM bookings WHERE notes LIKE '%&amp;%' OR notes LIKE '%&lt;%' OR notes LIKE '%&gt;%'");
    
    foreach ($bookings as $booking) {
        $fixedNotes = html_entity_decode($booking['notes'] ?? '', ENT_QUOTES, 'UTF-8');
        
        if ($fixedNotes !== ($booking['notes'] ?? '')) {
            $database->query(
                "UPDATE bookings SET notes = ? WHERE id = ?",
                [$fixedNotes, $booking['id']]
            );
            echo "Fixed booking notes for booking: {$booking['id']}\n";
        }
    }
    
    // Fix progressive_report_entries table
    echo "Fixing progressive_report_entries table...\n";
    try {
        $entries = $database->fetchAll("SELECT id, treatment, description, notes FROM progressive_report_entries WHERE treatment LIKE '%&amp;%' OR treatment LIKE '%&lt;%' OR treatment LIKE '%&gt;%' OR description LIKE '%&amp;%' OR description LIKE '%&lt;%' OR description LIKE '%&gt;%' OR notes LIKE '%&amp;%' OR notes LIKE '%&lt;%' OR notes LIKE '%&gt;%'");
        
        foreach ($entries as $entry) {
            $fixedTreatment = html_entity_decode($entry['treatment'] ?? '', ENT_QUOTES, 'UTF-8');
            $fixedDescription = html_entity_decode($entry['description'] ?? '', ENT_QUOTES, 'UTF-8');
            $fixedNotes = html_entity_decode($entry['notes'] ?? '', ENT_QUOTES, 'UTF-8');
            
            if ($fixedTreatment !== ($entry['treatment'] ?? '') || $fixedDescription !== ($entry['description'] ?? '') || $fixedNotes !== ($entry['notes'] ?? '')) {
                $database->query(
                    "UPDATE progressive_report_entries SET treatment = ?, description = ?, notes = ? WHERE id = ?",
                    [$fixedTreatment, $fixedDescription, $fixedNotes, $entry['id']]
                );
                echo "Fixed progressive report entry: {$entry['id']}\n";
            }
        }
    } catch (Exception $e) {
        echo "Progressive report entries table not found or error: " . $e->getMessage() . "\n";
    }
    
    // Fix notifications table
    echo "Fixing notifications table...\n";
    try {
        $notifications = $database->fetchAll("SELECT id, title, message FROM notifications WHERE title LIKE '%&amp;%' OR title LIKE '%&lt;%' OR title LIKE '%&gt;%' OR message LIKE '%&amp;%' OR message LIKE '%&lt;%' OR message LIKE '%&gt;%'");
        
        foreach ($notifications as $notification) {
            $fixedTitle = html_entity_decode($notification['title'], ENT_QUOTES, 'UTF-8');
            $fixedMessage = html_entity_decode($notification['message'], ENT_QUOTES, 'UTF-8');
            
            if ($fixedTitle !== $notification['title'] || $fixedMessage !== $notification['message']) {
                $database->query(
                    "UPDATE notifications SET title = ?, message = ? WHERE id = ?",
                    [$fixedTitle, $fixedMessage, $notification['id']]
                );
                echo "Fixed notification: {$notification['title']}\n";
            }
        }
    } catch (Exception $e) {
        echo "Notifications table not found or error: " . $e->getMessage() . "\n";
    }
    
    echo "HTML entities fix completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
