<?php
/**
 * Privacy Policy Page
 * Redolence Medi Aesthetics - Professional Medical Aesthetics
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Privacy Policy - Redolence Medi Aesthetics";
$pageDescription = "Learn how Redolence Medi Aesthetics protects your personal information and medical data with our comprehensive privacy policy.";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Modern Privacy Policy Styles - Redolence Medical Aesthetics */
:root {
    --redolence-green: #49a75c;
    --redolence-blue: #5894d2;
    --redolence-navy: #1a2332;
}

.privacy-page {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    min-height: 100vh;
}

.privacy-hero {
    background: linear-gradient(135deg, var(--redolence-green) 0%, var(--redolence-blue) 100%);
    position: relative;
    overflow: hidden;
}

.privacy-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.05) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.content-section {
    background: white;
    border-radius: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(73, 167, 92, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    overflow: hidden;
    position: relative;
}

.content-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--redolence-green), var(--redolence-blue));
}

.content-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.section-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--redolence-green), var(--redolence-blue));
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

.privacy-content h3 {
    color: var(--redolence-navy);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--redolence-green);
}

.privacy-content h4 {
    color: var(--redolence-green);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem 0;
}

.privacy-content ul {
    list-style: none;
    padding-left: 0;
    margin: 1rem 0;
}

.privacy-content li {
    padding: 0.5rem 0;
    padding-left: 2rem;
    position: relative;
}

.privacy-content li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--redolence-green);
    font-weight: bold;
    font-size: 1.2rem;
}

.privacy-content p {
    margin: 1rem 0;
    line-height: 1.8;
    color: #374151;
}

.privacy-content strong {
    color: var(--redolence-navy);
    font-weight: 600;
}

.contact-card {
    background: linear-gradient(135deg, var(--redolence-green)/5 0%, var(--redolence-blue)/5 100%);
    border: 2px solid var(--redolence-green)/20;
    border-radius: 1.5rem;
    padding: 2rem;
    text-align: center;
}

.contact-button {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, var(--redolence-green), var(--redolence-blue));
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.contact-button:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
    text-decoration: none;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-section {
        margin-bottom: 1.5rem;
    }

    .privacy-hero {
        padding: 3rem 0;
    }
}
</style>

</style>

<!-- Modern Privacy Page -->
<div class="privacy-page">

<!-- Hero Section -->
<section class="privacy-hero py-20">
    <div class="max-w-7xl mx-auto px-6 text-center relative z-10">
        <!-- Medical Badge -->
        <div class="inline-flex items-center bg-white/20 text-white px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-white/30 backdrop-blur-sm animate-on-scroll">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            HIPAA Compliant • Medical Privacy
        </div>

        <h1 class="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight animate-on-scroll">
            Privacy Policy
        </h1>

        <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed animate-on-scroll">
            Your privacy and medical information security are our highest priorities at Redolence Medi Aesthetics
        </p>

        <div class="flex items-center justify-center space-x-8 text-white/80 animate-on-scroll">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                <span>Secure & Encrypted</span>
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>HIPAA Compliant</span>
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Transparent Practices</span>
            </div>
        </div>
    </div>
</section>

<!-- Content Section -->
<section class="py-16">
    <div class="max-w-6xl mx-auto px-6">

        <!-- Last Updated -->
        <div class="text-center mb-12">
            <p class="text-gray-600">Last updated: <span class="font-semibold text-redolence-green"><?= date('F d, Y') ?></span></p>
        </div>

        <!-- Introduction Section -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Introduction</h2>

                <div class="privacy-content">
                    <p>
                        At <strong>Redolence Medi Aesthetics</strong>, we respect your privacy and are committed to protecting your personal and medical information. This privacy policy explains how we collect, use, and safeguard your data when you visit our website or receive our medical aesthetic services.
                    </p>
                    <p>
                        As a medical aesthetics practice, we adhere to strict <strong>HIPAA (Health Insurance Portability and Accountability Act)</strong> guidelines to ensure your protected health information (PHI) remains confidential and secure.
                    </p>
                    <p>
                        This policy applies to all personal and medical information collected through our website, during consultations, treatments, and any other interactions with our practice.
                    </p>
                </div>
            </div>
        </div>

        <!-- Information We Collect -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Information We Collect</h2>

                <div class="privacy-content">
                    <h3>Personal Information</h3>
                    <ul>
                        <li>Name, email address, phone number, and contact details</li>
                        <li>Date of birth and demographic information</li>
                        <li>Account credentials for our patient portal</li>
                        <li>Emergency contact information</li>
                        <li>Insurance information (if applicable)</li>
                    </ul>

                    <h3>Medical Information (Protected Health Information - PHI)</h3>
                    <ul>
                        <li>Medical history and current health conditions</li>
                        <li>Medications and allergies</li>
                        <li>Treatment records and consultation notes</li>
                        <li>Before and after photographs (with consent)</li>
                        <li>Treatment outcomes and follow-up information</li>
                    </ul>

                    <h3>Technical Information</h3>
                    <ul>
                        <li>IP address and device information</li>
                        <li>Browser type and version</li>
                        <li>Website usage patterns and preferences</li>
                        <li>Cookies and similar tracking technologies</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- How We Use Your Information -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">How We Use Your Information</h2>

                <div class="privacy-content">
                    <h3>Medical Treatment Purposes</h3>
                    <ul>
                        <li>Providing medical aesthetic consultations and treatments</li>
                        <li>Developing personalized treatment plans</li>
                        <li>Monitoring treatment progress and outcomes</li>
                        <li>Coordinating care with other healthcare providers</li>
                        <li>Maintaining accurate medical records</li>
                    </ul>

                    <h3>Administrative Purposes</h3>
                    <ul>
                        <li>Scheduling appointments and sending reminders</li>
                        <li>Processing payments and insurance claims</li>
                        <li>Communicating about your treatments</li>
                        <li>Responding to your inquiries and requests</li>
                        <li>Improving our services and patient experience</li>
                    </ul>

                    <h3>Legal and Compliance</h3>
                    <ul>
                        <li>Complying with medical regulations and standards</li>
                        <li>Maintaining records as required by law</li>
                        <li>Protecting against fraud and unauthorized access</li>
                        <li>Responding to legal requests and court orders</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Data Security -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Data Security & Protection</h2>

                <div class="privacy-content">
                    <p>
                        We implement comprehensive security measures to protect your personal and medical information:
                    </p>

                    <h3>Technical Safeguards</h3>
                    <ul>
                        <li>SSL encryption for all data transmission</li>
                        <li>Secure, encrypted databases with access controls</li>
                        <li>Regular security audits and vulnerability assessments</li>
                        <li>Firewall protection and intrusion detection systems</li>
                        <li>Secure backup and disaster recovery procedures</li>
                    </ul>

                    <h3>Administrative Safeguards</h3>
                    <ul>
                        <li>HIPAA-compliant policies and procedures</li>
                        <li>Staff training on privacy and security protocols</li>
                        <li>Role-based access controls for medical records</li>
                        <li>Regular privacy and security training updates</li>
                        <li>Incident response and breach notification procedures</li>
                    </ul>

                    <h3>Physical Safeguards</h3>
                    <ul>
                        <li>Secure facility access controls</li>
                        <li>Locked storage for physical records</li>
                        <li>Secure disposal of confidential information</li>
                        <li>Workstation security and screen locks</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Your Rights -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Your Privacy Rights</h2>

                <div class="privacy-content">
                    <p>
                        Under HIPAA and applicable privacy laws, you have the following rights regarding your personal and medical information:
                    </p>

                    <h3>Access Rights</h3>
                    <ul>
                        <li>Request access to your medical records</li>
                        <li>Obtain copies of your treatment information</li>
                        <li>Review how your information is used and shared</li>
                    </ul>

                    <h3>Amendment Rights</h3>
                    <ul>
                        <li>Request corrections to inaccurate information</li>
                        <li>Add clarifications to your medical records</li>
                        <li>Update your contact and personal information</li>
                    </ul>

                    <h3>Restriction Rights</h3>
                    <ul>
                        <li>Request restrictions on how we use your information</li>
                        <li>Limit who can access your medical records</li>
                        <li>Opt-out of certain communications</li>
                    </ul>

                    <h3>Portability Rights</h3>
                    <ul>
                        <li>Request transfer of your records to another provider</li>
                        <li>Receive your information in a portable format</li>
                        <li>Direct us to share information with third parties</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="contact-card animate-on-scroll">
            <div class="section-icon mx-auto">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
            </div>

            <h2 class="text-3xl font-bold text-redolence-navy mb-4">Privacy Questions?</h2>
            <p class="text-gray-600 mb-6 text-lg">
                If you have any questions about this privacy policy or how we handle your information, please don't hesitate to contact us.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="text-center">
                    <h4 class="font-semibold text-redolence-navy mb-2">Privacy Officer</h4>
                    <p class="text-gray-600"><EMAIL></p>
                </div>
                <div class="text-center">
                    <h4 class="font-semibold text-redolence-navy mb-2">Phone</h4>
                    <p class="text-gray-600">+255 123 456 789</p>
                </div>
            </div>

            <a href="contact.php" class="contact-button">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Contact Us
            </a>
        </div>
    </div>
</section>

</div>
<!-- End Privacy Page -->


<!-- Scroll Animation Script -->

<script>
// Handle scroll animations
function handleScrollAnimations() {
    const elements = document.querySelectorAll('.animate-on-scroll:not(.visible)');

    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;

        if (elementTop < window.innerHeight - elementVisible) {
            element.classList.add('visible');
        }
    });
}

// Listen for scroll events
window.addEventListener('scroll', handleScrollAnimations);

// Check for elements in view on page load
document.addEventListener('DOMContentLoaded', () => {
    handleScrollAnimations();
});
</script>



<script>
// Handle scroll animations
function handleScrollAnimations() {
    const elements = document.querySelectorAll('.animate-on-scroll:not(.animate)');
    
    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementBottom = element.getBoundingClientRect().bottom;
        const windowHeight = window.innerHeight;
        
        if (elementTop < windowHeight * 0.9 && elementBottom > 0) {
            const delay = element.style.getPropertyValue('--delay') || '0s';
            setTimeout(() => {
                element.classList.add('animate');
            }, parseFloat(delay) * 1000);
        }
    });
}

// Set up Intersection Observer for scroll animations
if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const delay = entry.target.style.getPropertyValue('--delay') || '0s';
                setTimeout(() => {
                    entry.target.classList.add('animate');
                }, parseFloat(delay) * 1000);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    document.querySelectorAll('.animate-on-scroll').forEach(element => {
        observer.observe(element);
    });
} else {
    // Fallback for browsers that don't support Intersection Observer
    window.addEventListener('scroll', handleScrollAnimations);
    handleScrollAnimations();
}

// Check for elements in view on page load
document.addEventListener('DOMContentLoaded', () => {
    handleScrollAnimations();
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>