<?php
/**
 * Registration Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config/app.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'ADMIN') {
        redirect('/admin');
    } elseif ($user['role'] === 'STAFF') {
        redirect('/staff');
    } else {
        redirect('/customer');
    }
}

$errors = [];
$success = '';

// Get redirect parameter
$redirectUrl = $_GET['redirect'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'name' => sanitize($_POST['name'] ?? ''),
        'email' => sanitize($_POST['email'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'confirm_password' => $_POST['confirm_password'] ?? '',
        'phone' => sanitize($_POST['phone'] ?? ''),
        'referral_code' => sanitize($_POST['referral_code'] ?? '')
    ];
    $redirectUrl = sanitize($_POST['redirect'] ?? '');

    // Validate password confirmation
    if ($data['password'] !== $data['confirm_password']) {
        $errors['confirm_password'] = 'Passwords do not match';
    }

    if (empty($errors)) {
        $result = $auth->register($data);

        if ($result['success']) {
            // Auto-login the user after successful registration
            $loginResult = $auth->login($data['email'], $data['password']);

            if ($loginResult['success'] && !empty($redirectUrl)) {
                // Validate redirect URL to prevent open redirect attacks
                $allowedPaths = ['/customer/', '/services.php'];
                $isValidRedirect = false;

                foreach ($allowedPaths as $allowedPath) {
                    if (strpos($redirectUrl, $allowedPath) !== false) {
                        $isValidRedirect = true;
                        break;
                    }
                }

                if ($isValidRedirect) {
                    redirect($redirectUrl);
                } else {
                    redirect('/customer');
                }
            } else {
                $success = 'Account created successfully! You can now login.';
                // Clear form data
                $data = [];
            }
        } else {
            $errors = $result['errors'];
        }
    }
}

$pageTitle = "Create your account";
$pageDescription = "Sign up to manage appointments, rewards and more.";

// Include header
include __DIR__ . '/../includes/header.php';
?>

<style>
  /* Background */
  .reg-bg {
    min-height: calc(100vh - 140px);
    background: radial-gradient(1000px 500px at 0% 0%, rgba(73,167,92,0.12), transparent 60%),
                radial-gradient(800px 400px at 100% 100%, rgba(88,148,210,0.12), transparent 60%),
                linear-gradient(180deg, #0b1220 0%, #0f172a 100%);
  }
  /* Glass shell */
  .glass {
    background: rgba(255,255,255,0.06);
    border: 1px solid rgba(255,255,255,0.08);
    box-shadow: 0 20px 60px rgba(0,0,0,0.35), inset 0 1px 0 rgba(255,255,255,0.05);
    backdrop-filter: blur(16px);
  }
  /* Accent ring */
  .accent-ring {
    box-shadow: 0 0 0 1px rgba(73,167,92,0.35), 0 10px 30px rgba(73,167,92,0.25);
  }
  /* Stepper */
  .step-dot { transition: all .3s ease; }
  .step-dot.active { background: #49a75c; box-shadow: 0 0 0 6px rgba(73,167,92,0.18); }
  .step-line { height: 2px; background: linear-gradient(90deg, rgba(73,167,92,.35), rgba(88,148,210,.35)); }

  /* Inputs */
  .field { transition: all .2s ease; }
  .field:focus { outline: none; box-shadow: 0 0 0 3px rgba(73,167,92,0.25); border-color: rgba(73,167,92,0.65) !important; }

  /* Buttons */
  .btn-primary {
    background: linear-gradient(135deg, #49a75c 0%, #5894d2 100%);
    color: #0b1220;
    transition: transform .15s ease, box-shadow .2s ease;
  }
  .btn-primary:hover { transform: translateY(-1px); box-shadow: 0 12px 28px rgba(73,167,92,0.35); }
  .btn-muted { background: rgba(255,255,255,0.06); border: 1px solid rgba(255,255,255,0.12); }

  /* Alert */
  .alert { animation: slideIn .35s ease; }
  @keyframes slideIn { from { opacity: 0; transform: translateY(-6px);} to { opacity: 1; transform: translateY(0);} }

  /* Strength bar */
  .strength-bar { height: 8px; background: rgba(148,163,184,0.22); border-radius: 9999px; overflow: hidden; }
  .strength-fill { height: 100%; width: 0%; transition: width .4s ease, background .3s ease; }

  /* Marketing shapes */
  .orb { filter: blur(30px); opacity: .6; }
</style>

<div class="reg-bg relative">
  <!-- Decorative orbs -->
  <div class="pointer-events-none absolute -top-10 -left-10 w-64 h-64 rounded-full bg-emerald-500/20 orb"></div>
  <div class="pointer-events-none absolute -bottom-10 -right-10 w-72 h-72 rounded-full bg-sky-500/20 orb"></div>

  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Brand/Marketing Panel -->
      <section class="glass rounded-2xl p-8 lg:p-10 relative overflow-hidden">
        <div class="absolute -right-10 -top-10 w-48 h-48 rounded-full bg-emerald-400/10"></div>
        <div class="relative">
          <div class="inline-flex items-center space-x-2 px-3 py-1 rounded-full bg-emerald-500/15 border border-emerald-500/30 text-emerald-300 text-xs font-semibold mb-6">
            <span class="w-2 h-2 rounded-full bg-emerald-400"></span>
            <span>New to Redolence Medi Aesthetics</span>
          </div>
          <h1 class="text-3xl sm:text-4xl font-extrabold tracking-tight text-white">
            Join the experience built around you
          </h1>
          <p class="mt-3 text-slate-300/90 leading-relaxed">
            Create your account to book faster, track rewards, and get personalized recommendations.
          </p>

          <ul class="mt-8 space-y-4">
            <li class="flex items-start">
              <div class="mt-1 mr-3 w-6 h-6 rounded-lg grid place-content-center bg-emerald-500/20 text-emerald-300 accent-ring">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-7.25 7.25a1 1 0 01-1.414 0l-3-3a1 1 0 111.414-1.414L8.5 11.086l6.543-6.543a1 1 0 011.414 0z" clip-rule="evenodd"/></svg>
              </div>
              <div>
                <p class="text-slate-200 font-semibold">Priority booking</p>
                <p class="text-slate-300/80 text-sm">Reserve your favorite stylist in just a few taps.</p>
              </div>
            </li>
            <li class="flex items-start">
              <div class="mt-1 mr-3 w-6 h-6 rounded-lg grid place-content-center bg-emerald-500/20 text-emerald-300 accent-ring">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor"><path d="M10 2a1 1 0 011 1v5h5a1 1 0 010 2h-5v5a1 1 0 01-2 0v-5H4a1 1 0 110-2h5V3a1 1 0 011-1z"/></svg>
              </div>
              <div>
                <p class="text-slate-200 font-semibold">Rewards & referrals</p>
                <p class="text-slate-300/80 text-sm">Earn points and unlock perks. Use a referral code for bonuses.</p>
              </div>
            </li>
            <li class="flex items-start">
              <div class="mt-1 mr-3 w-6 h-6 rounded-lg grid place-content-center bg-emerald-500/20 text-emerald-300 accent-ring">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M2.003 5.884l8-3.2a1 1 0 01.747 0l8 3.2a1 1 0 01.25 1.686l-8 7a1 1 0 01-1.29 0l-8-7a1 1 0 01.293-1.686z" clip-rule="evenodd"/></svg>
              </div>
              <div>
                <p class="text-slate-200 font-semibold">Personalized tips</p>
                <p class="text-slate-300/80 text-sm">Get care recommendations matched to your preferences.</p>
              </div>
            </li>
          </ul>

          <div class="mt-10 p-4 rounded-xl bg-white/5 border border-white/10">
            <div class="flex items-center gap-3">
              <img src="https://images.unsplash.com/photo-1544717305-2782549b5136?q=80&w=256&auto=format&fit=crop" alt="avatar" class="w-10 h-10 rounded-full object-cover" />
              <div>
                <p class="text-slate-200 text-sm">“The new portal is slick and fast. Booking takes seconds.”</p>
                <p class="text-slate-400 text-xs mt-1">— Regular client</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Form Panel -->
      <section class="glass rounded-2xl p-6 sm:p-8">
        <!-- Alerts -->
        <?php if (!empty($errors) && isset($errors['general'])): ?>
          <div class="alert mb-4 p-3 rounded-md border border-red-500/30 bg-red-500/10 text-red-300 text-sm">
            <?= htmlspecialchars($errors['general']) ?>
          </div>
        <?php endif; ?>
        <?php if ($success): ?>
          <div class="alert mb-4 p-3 rounded-md border border-emerald-500/30 bg-emerald-500/10 text-emerald-300 text-sm">
            <?= htmlspecialchars($success) ?>
          </div>
        <?php endif; ?>

        <!-- Stepper -->
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center gap-3">
            <div id="dot-1" class="step-dot w-8 h-8 rounded-full grid place-content-center bg-white/10 text-slate-200">1</div>
            <div class="step-line w-14 sm:w-24 opacity-60"></div>
            <div id="dot-2" class="step-dot w-8 h-8 rounded-full grid place-content-center bg-white/10 text-slate-200">2</div>
          </div>
          <p class="text-xs text-slate-400"><span id="step-label">Your details</span></p>
        </div>

        <form id="registrationForm" method="POST" autocomplete="off" class="space-y-6">
          <?php if (!empty($redirectUrl)): ?>
            <input type="hidden" name="redirect" value="<?= htmlspecialchars($redirectUrl) ?>">
          <?php endif; ?>

          <!-- Step 1: Basic Info -->
          <div id="step-1" class="space-y-5">
            <div>
              <label for="name" class="block text-sm font-medium text-slate-200 mb-2">Full name</label>
              <input id="name" name="name" type="text" required
                     value="<?= htmlspecialchars($data['name'] ?? '') ?>"
                     class="field w-full px-4 py-3 rounded-xl bg-white/5 border border-white/10 text-slate-100 placeholder-slate-400" placeholder="Alex Johnson">
              <?php if (isset($errors['name'])): ?>
                <p class="mt-2 text-xs text-red-300"><?= htmlspecialchars($errors['name']) ?></p>
              <?php endif; ?>
            </div>

            <div>
              <label for="email" class="block text-sm font-medium text-slate-200 mb-2">Email address</label>
              <input id="email" name="email" type="email" required
                     value="<?= htmlspecialchars($data['email'] ?? '') ?>"
                     class="field w-full px-4 py-3 rounded-xl bg-white/5 border border-white/10 text-slate-100 placeholder-slate-400" placeholder="<EMAIL>">
              <?php if (isset($errors['email'])): ?>
                <p class="mt-2 text-xs text-red-300"><?= htmlspecialchars($errors['email']) ?></p>
              <?php endif; ?>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label for="phone" class="block text-sm font-medium text-slate-200 mb-2">Phone (optional)</label>
                <input id="phone" name="phone" type="tel"
                       value="<?= htmlspecialchars($data['phone'] ?? '') ?>"
                       class="field w-full px-4 py-3 rounded-xl bg-white/5 border border-white/10 text-slate-100 placeholder-slate-400" placeholder="****** 000 1234">
              </div>
              <div>
                <label for="referral_code" class="block text-sm font-medium text-slate-200 mb-2">Referral code (optional)</label>
                <input id="referral_code" name="referral_code" type="text"
                       value="<?= htmlspecialchars($data['referral_code'] ?? '') ?>"
                       class="field w-full px-4 py-3 rounded-xl bg-white/5 border border-white/10 text-slate-100 placeholder-slate-400" placeholder="FRIEND10">
                <p class="mt-2 text-xs text-slate-400">Use a referral to unlock welcome points.</p>
              </div>
            </div>

            <div class="flex items-center justify-end pt-2">
              <button id="to-step-2" type="button" class="btn-primary px-5 py-2 rounded-lg font-semibold">Continue</button>
            </div>
          </div>

          <!-- Step 2: Security -->
          <div id="step-2" class="space-y-5 hidden">
            <div>
              <label for="password" class="block text-sm font-medium text-slate-200 mb-2">Create password</label>
              <div class="relative">
                <input id="password" name="password" type="password" required
                       class="field w-full pr-12 px-4 py-3 rounded-xl bg-white/5 border border-white/10 text-slate-100 placeholder-slate-400" placeholder="At least 8 characters">
                <button type="button" class="absolute inset-y-0 right-0 px-3 text-slate-400 hover:text-slate-200" onclick="togglePassword('password','eye-password')">
                  <svg id="eye-password" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/></svg>
                </button>
              </div>
              <?php if (isset($errors['password'])): ?>
                <p class="mt-2 text-xs text-red-300"><?= htmlspecialchars($errors['password']) ?></p>
              <?php endif; ?>

              <!-- Generator controls -->
              <div class="mt-3 flex flex-wrap items-center gap-2">
                <button id="btn-gen" type="button" class="btn-muted px-3 py-2 rounded-lg text-slate-200">Generate strong password</button>
                <button id="btn-copy" type="button" class="btn-muted px-3 py-2 rounded-lg text-slate-200">Copy</button>
                <button id="btn-toggle-gen" type="button" class="btn-muted px-3 py-2 rounded-lg text-slate-200">Options</button>
              </div>
              <div id="gen-panel" class="mt-3 hidden rounded-xl border border-white/10 bg-white/5 p-3">
                <div class="flex items-center justify-between gap-3">
                  <label class="text-xs text-slate-400">Length: <span id="gen-length-label" class="font-semibold text-slate-200">16</span></label>
                  <input id="gen-length" type="range" min="12" max="32" value="16" class="w-40 accent-emerald-400">
                </div>
                <div class="grid grid-cols-2 gap-3 mt-3 text-sm">
                  <label class="inline-flex items-center gap-2 text-slate-300"><input id="opt-lower" type="checkbox" checked class="h-4 w-4 rounded bg-white/5 border-white/20">Lowercase</label>
                  <label class="inline-flex items-center gap-2 text-slate-300"><input id="opt-upper" type="checkbox" checked class="h-4 w-4 rounded bg-white/5 border-white/20">Uppercase</label>
                  <label class="inline-flex items-center gap-2 text-slate-300"><input id="opt-number" type="checkbox" checked class="h-4 w-4 rounded bg-white/5 border-white/20">Numbers</label>
                  <label class="inline-flex items-center gap-2 text-slate-300"><input id="opt-symbol" type="checkbox" checked class="h-4 w-4 rounded bg-white/5 border-white/20">Symbols</label>
                </div>
              </div>

              <div class="mt-3">
                <div class="flex items-center justify-between text-xs text-slate-400">
                  <span>Password strength</span>
                  <span id="strength-text" class="font-medium">Weak</span>
                </div>
                <div class="strength-bar mt-2">
                  <div id="strength-fill" class="strength-fill"></div>
                </div>
                <ul class="mt-3 grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs text-slate-300/90">
                  <li id="req-length" class="flex items-center gap-2"><span class="w-1.5 h-1.5 rounded-full bg-red-400" data-dot></span> 8+ characters</li>
                  <li id="req-lowercase" class="flex items-center gap-2"><span class="w-1.5 h-1.5 rounded-full bg-red-400" data-dot></span> Lowercase letter</li>
                  <li id="req-uppercase" class="flex items-center gap-2"><span class="w-1.5 h-1.5 rounded-full bg-red-400" data-dot></span> Uppercase letter</li>
                  <li id="req-number" class="flex items-center gap-2"><span class="w-1.5 h-1.5 rounded-full bg-red-400" data-dot></span> Number</li>
                  <li id="req-special" class="flex items-center gap-2"><span class="w-1.5 h-1.5 rounded-full bg-red-400" data-dot></span> Special character (!@#$%)</li>
                </ul>
              </div>
            </div>

            <div>
              <label for="confirm_password" class="block text-sm font-medium text-slate-200 mb-2">Confirm password</label>
              <div class="relative">
                <input id="confirm_password" name="confirm_password" type="password" required
                       class="field w-full pr-12 px-4 py-3 rounded-xl bg-white/5 border border-white/10 text-slate-100 placeholder-slate-400" placeholder="Re-enter password">
                <button type="button" class="absolute inset-y-0 right-0 px-3 text-slate-400 hover:text-slate-200" onclick="togglePassword('confirm_password','eye-confirm')">
                  <svg id="eye-confirm" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/></svg>
                </button>
              </div>
              <?php if (isset($errors['confirm_password'])): ?>
                <p class="mt-2 text-xs text-red-300"><?= htmlspecialchars($errors['confirm_password']) ?></p>
              <?php endif; ?>
              <p id="match-hint" class="mt-2 text-xs hidden"></p>
            </div>

            <div class="flex items-start gap-3 pt-1">
              <input id="terms" name="terms" type="checkbox" required class="mt-1 h-4 w-4 rounded border-white/20 bg-white/5">
              <label for="terms" class="text-sm text-slate-300">I agree to the <a href="<?= getBasePath() ?>/terms" class="text-emerald-300 hover:text-emerald-200 font-medium">Terms of Service</a> and <a href="<?= getBasePath() ?>/privacy" class="text-emerald-300 hover:text-emerald-200 font-medium">Privacy Policy</a>.</label>
            </div>

            <div class="flex items-center justify-between pt-2">
              <button id="back-to-1" type="button" class="btn-muted px-4 py-2 rounded-lg text-slate-200">Back</button>
              <button id="submitBtn" type="submit" disabled class="btn-primary px-5 py-2 rounded-lg font-semibold inline-flex items-center">
                <span id="submitText">Create account</span>
                <svg id="loadingSpinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </button>
            </div>

            <p class="text-center text-sm text-slate-400 pt-4">Already have an account?
              <a href="<?= getBasePath() ?>/auth/login.php<?= !empty($redirectUrl) ? '?redirect=' . urlencode($redirectUrl) : '' ?>" class="text-emerald-300 hover:text-emerald-200 font-semibold">Sign in</a>
            </p>
          </div>
        </form>
      </section>
    </div>
  </div>
</div>

<script>
  // Step handling
  let currentStep = 1;
  const step1 = document.getElementById('step-1');
  const step2 = document.getElementById('step-2');
  const dot1 = document.getElementById('dot-1');
  const dot2 = document.getElementById('dot-2');
  const stepLabel = document.getElementById('step-label');

  function setStep(step) {
    currentStep = step;
    if (step === 1) {
      step1.classList.remove('hidden');
      step2.classList.add('hidden');
      dot1.classList.add('active');
      dot2.classList.remove('active');
      stepLabel.textContent = 'Your details';
    } else {
      step1.classList.add('hidden');
      step2.classList.remove('hidden');
      dot2.classList.add('active');
      dot1.classList.remove('active');
      stepLabel.textContent = 'Security';
    }
  }

  document.getElementById('to-step-2').addEventListener('click', () => {
    // Basic client-side check for step 1
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    const emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    if (name && emailValid) {
      setStep(2);
      document.getElementById('password').focus();
    } else {
      // Lightweight nudge
      if (!name) document.getElementById('name').focus();
      if (name && !emailValid) document.getElementById('email').focus();
    }
  });
  document.getElementById('back-to-1').addEventListener('click', () => setStep(1));

  // Password toggle
  function togglePassword(fieldId, iconId) {
    const input = document.getElementById(fieldId);
    const icon = document.getElementById(iconId);
    if (input.type === 'password') {
      input.type = 'text';
      icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />';
    } else {
      input.type = 'password';
      icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />';
    }
  }
  window.togglePassword = togglePassword;

  // Validation state
  const validationState = {
    password: { length: false, lowercase: false, uppercase: false, number: false, special: false },
    passwordMatch: false,
    termsAccepted: false
  };

  function updateDot(el, ok) {
    const dot = el.querySelector('[data-dot]');
    if (!dot) return;
    dot.classList.toggle('bg-red-400', !ok);
    dot.classList.toggle('bg-emerald-400', ok);
  }

  function updateStrengthBar(score) {
    const fill = document.getElementById('strength-fill');
    const text = document.getElementById('strength-text');
    const labels = ['Very weak','Weak','Fair','Good','Strong','Excellent'];
    const widths = ['5%','20%','40%','60%','80%','100%'];
    const colors = ['#ef4444','#ef4444','#f59e0b','#eab308','#22c55e','#10b981'];
    const idx = Math.min(Math.max(score, 0), 5);
    fill.style.width = widths[idx];
    fill.style.background = colors[idx];
    text.textContent = labels[idx];
  }

  function validateForm() {
    const allMet = Object.values(validationState.password).every(Boolean);
    const valid = allMet && validationState.passwordMatch && validationState.termsAccepted;
    const btn = document.getElementById('submitBtn');
    btn.disabled = !valid;
  }

  // Password checks
  const pwd = document.getElementById('password');
  const cpwd = document.getElementById('confirm_password');
  const matchHint = document.getElementById('match-hint');

  // Generator elements
  const genPanel = document.getElementById('gen-panel');
  const genLength = document.getElementById('gen-length');
  const genLengthLabel = document.getElementById('gen-length-label');
  const btnGen = document.getElementById('btn-gen');
  const btnCopy = document.getElementById('btn-copy');
  const btnToggleGen = document.getElementById('btn-toggle-gen');
  const optLower = document.getElementById('opt-lower');
  const optUpper = document.getElementById('opt-upper');
  const optNumber = document.getElementById('opt-number');
  const optSymbol = document.getElementById('opt-symbol');

  function randIndex(max) {
    if (window.crypto && window.crypto.getRandomValues) {
      const arr = new Uint32Array(1);
      window.crypto.getRandomValues(arr);
      return arr[0] % max;
    }
    return Math.floor(Math.random() * max);
  }

  function shuffle(arr) {
    for (let i = arr.length - 1; i > 0; i--) {
      const j = randIndex(i + 1);
      [arr[i], arr[j]] = [arr[j], arr[i]];
    }
    return arr;
  }

  function generateStrongPassword(length, sets) {
    const pools = [];
    if (sets.lower) pools.push('abcdefghijklmnopqrstuvwxyz');
    if (sets.upper) pools.push('ABCDEFGHIJKLMNOPQRSTUVWXYZ');
    if (sets.number) pools.push('0123456789');
    if (sets.symbol) pools.push('!@#$%^&*()_+-=[]{}|;:,.<>?');
    if (pools.length === 0) pools.push('abcdefghijklmnopqrstuvwxyz0123456789');

    const all = pools.join('');
    const chars = [];

    // Ensure at least one from each selected pool
    pools.forEach(pool => { chars.push(pool[randIndex(pool.length)]); });

    for (let i = chars.length; i < length; i++) {
      chars.push(all[randIndex(all.length)]);
    }
    shuffle(chars);
    return chars.join('');
  }

  if (btnToggleGen) {
    btnToggleGen.addEventListener('click', () => {
      genPanel.classList.toggle('hidden');
    });
  }
  if (genLength && genLengthLabel) {
    genLength.addEventListener('input', () => {
      genLengthLabel.textContent = genLength.value;
    });
  }
  if (btnGen) {
    btnGen.addEventListener('click', () => {
      const pass = generateStrongPassword(parseInt(genLength.value || '16', 10), {
        lower: !optLower || optLower.checked,
        upper: !optUpper || optUpper.checked,
        number: !optNumber || optNumber.checked,
        symbol: !optSymbol || optSymbol.checked,
      });
      pwd.value = pass;
      cpwd.value = pass;
      runPasswordChecks();
      btnGen.textContent = 'Generated';
      setTimeout(() => { btnGen.textContent = 'Generate strong password'; }, 1200);
    });
  }
  if (btnCopy) {
    btnCopy.addEventListener('click', async () => {
      const text = pwd.value || '';
      if (!text) return;
      try {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          await navigator.clipboard.writeText(text);
        } else {
          throw new Error('Clipboard API not available');
        }
        btnCopy.textContent = 'Copied!';
      } catch (e) {
        const ta = document.createElement('textarea');
        ta.value = text;
        ta.style.position = 'fixed';
        ta.style.opacity = '0';
        document.body.appendChild(ta);
        ta.focus();
        ta.select();
        try { document.execCommand('copy'); } catch (_) {}
        document.body.removeChild(ta);
        btnCopy.textContent = 'Copied!';
      }
      setTimeout(() => { btnCopy.textContent = 'Copy'; }, 1000);
    });
  }

  function runPasswordChecks() {
    const v = pwd.value || '';
    validationState.password.length = v.length >= 8;
    validationState.password.lowercase = /[a-z]/.test(v);
    validationState.password.uppercase = /[A-Z]/.test(v);
    validationState.password.number = /[0-9]/.test(v);
    validationState.password.special = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(v);

    updateDot(document.getElementById('req-length'), validationState.password.length);
    updateDot(document.getElementById('req-lowercase'), validationState.password.lowercase);
    updateDot(document.getElementById('req-uppercase'), validationState.password.uppercase);
    updateDot(document.getElementById('req-number'), validationState.password.number);
    updateDot(document.getElementById('req-special'), validationState.password.special);

    const score = Object.values(validationState.password).filter(Boolean).length;
    updateStrengthBar(score);

    // match check
    const m = v.length > 0 && v === (cpwd.value || '');
    validationState.passwordMatch = m;
    if (cpwd.value.length) {
      matchHint.classList.remove('hidden');
      matchHint.textContent = m ? 'Passwords match' : 'Passwords do not match';
      matchHint.className = 'mt-2 text-xs ' + (m ? 'text-emerald-300' : 'text-red-300');
    } else {
      matchHint.classList.add('hidden');
    }

    validateForm();
  }

  pwd.addEventListener('input', runPasswordChecks);
  cpwd.addEventListener('input', runPasswordChecks);

  // Terms checkbox
  document.getElementById('terms').addEventListener('change', function() {
    validationState.termsAccepted = this.checked;
    validateForm();
  });

  // Submit UX
  document.getElementById('registrationForm').addEventListener('submit', function(e) {
    const btn = document.getElementById('submitBtn');
    const text = document.getElementById('submitText');
    const spinner = document.getElementById('loadingSpinner');
    if (btn.disabled) {
      e.preventDefault();
      return;
    }
    btn.disabled = true;
    text.textContent = 'Creating your account…';
    spinner.classList.remove('hidden');
  });

  // Initialize
  document.addEventListener('DOMContentLoaded', function() {
    // If server returned password related errors, jump to step 2
    const startStep = <?php
      $initialStep = 1;
      if (!empty($errors) && (isset($errors['password']) || isset($errors['confirm_password']))) { $initialStep = 2; }
      echo (int)$initialStep;
    ?>;
    setStep(startStep);
    // Update generator label
    const gl = document.getElementById('gen-length');
    const gll = document.getElementById('gen-length-label');
    if (gl && gll) gll.textContent = gl.value;
    runPasswordChecks();
  });
</script>

<?php
// Include footer
include __DIR__ . '/../includes/footer.php';
?>
