<?php
/**
 * API for Blog Posts - Admin
 * Flix Salonce - PHP Version
 */

header('Content-Type: application/json');
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/blog_functions.php';

// Require admin authentication for all API actions
$auth->requireRole('ADMIN');

$action = $_GET['action'] ?? $_POST['action'] ?? null;
$response = ['success' => false, 'error' => 'Invalid action.'];

switch ($action) {
    case 'get':
        if (isset($_GET['id'])) {
            $postId = sanitize($_GET['id']);
            $post = getBlogPostById($postId);
            if ($post) {
                // Decode HTML entities for admin editing
                $post['title'] = html_entity_decode($post['title'] ?? '', ENT_QUOTES, 'UTF-8');
                $post['content'] = html_entity_decode($post['content'] ?? '', ENT_QUOTES, 'UTF-8');
                $post['excerpt'] = html_entity_decode($post['excerpt'] ?? '', ENT_QUOTES, 'UTF-8');
                $post['meta_description'] = html_entity_decode($post['meta_description'] ?? '', ENT_QUOTES, 'UTF-8');

                // Format dates for consistency if needed, e.g., for datetime-local input
                if ($post['publish_date']) {
                    // Convert to 'YYYY-MM-DDTHH:MM' format for datetime-local input
                    $dt = new DateTime($post['publish_date']);
                    $post['publish_date'] = $dt->format('Y-m-d\TH:i');
                }
                $response = ['success' => true, 'post' => $post];
            } else {
                $response['error'] = 'Blog post not found.';
            }
        } else {
            $response['error'] = 'Post ID not provided for get action.';
        }
        break;

    // Future actions like 'list', 'create_api', 'update_api', 'delete_api' can be added here
    // For now, the main page handles create, update, delete via form submissions.
    // This API is primarily for AJAX-based fetching or more complex interactions if needed later.

    default:
        $response['error'] = 'Unknown API action for blog posts.';
        break;
}

echo json_encode($response);
exit;
?>