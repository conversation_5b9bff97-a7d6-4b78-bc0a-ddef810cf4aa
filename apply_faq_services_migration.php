<?php
/**
 * Apply FAQ Services Migration
 * Redolence Medi Aesthetics - FAQ Enhancement System
 */

require_once __DIR__ . '/config/app.php';

// Check if user is admin
if (!$auth->isAuthenticated() || !$auth->hasRole('ADMIN')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQ Services Migration - Redolence Medi Aesthetics</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #10b981; background: #ecfdf5; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #ef4444; background: #fef2f2; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #f59e0b; background: #fffbeb; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #3b82f6; background: #eff6ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f3f4f6; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .btn { background: #49a75c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #2d6a3e; }
    </style>
</head>
<body>
    <h1>FAQ Services Migration</h1>
    <p>This migration will add support for linking FAQ items to services.</p>

    <?php
    if (isset($_POST['apply_migration'])) {
        echo "<h2>Applying Migration...</h2>";
        
        try {
            // Read the migration file
            $migrationFile = __DIR__ . '/database/faq_services_migration.sql';
            
            if (!file_exists($migrationFile)) {
                throw new Exception("Migration file not found: $migrationFile");
            }
            
            $sql = file_get_contents($migrationFile);
            
            if ($sql === false) {
                throw new Exception("Could not read migration file");
            }
            
            echo "<div class='info'>📖 Migration file loaded successfully</div>";
            
            // Split SQL into individual statements, handling comments and special cases
            $statements = array_filter(
                array_map('trim', explode(';', $sql)),
                function($stmt) {
                    return !empty($stmt) &&
                           !preg_match('/^\s*--/', $stmt) &&
                           !preg_match('/^\s*\/\*/', $stmt) &&
                           !preg_match('/^\s*USE\s+/', $stmt) &&
                           !preg_match('/^\s*DELIMITER\s+/', $stmt) &&
                           $stmt !== 'DELIMITER';
                }
            );
            
            echo "<div class='info'>📝 Found " . count($statements) . " SQL statements to execute</div>";
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement)) continue;
                
                try {
                    // Skip empty statements and comments
                    if (preg_match('/^\s*(--|\/\*|$)/', $statement)) {
                        continue;
                    }

                    // Skip DELIMITER statements (already filtered but double-check)
                    if (preg_match('/^\s*DELIMITER\s+/', $statement) || $statement === 'DELIMITER') {
                        echo "<div class='info'>🔧 Skipping DELIMITER statement</div>";
                        continue;
                    }

                    $database->query($statement);
                    $successCount++;

                    // Show what was executed (truncated for readability)
                    $shortStatement = strlen($statement) > 100 ? substr($statement, 0, 100) . '...' : $statement;
                    echo "<div class='success'>✅ Executed: " . htmlspecialchars($shortStatement) . "</div>";

                } catch (Exception $e) {
                    $errorCount++;
                    $shortStatement = strlen($statement) > 100 ? substr($statement, 0, 100) . '...' : $statement;
                    echo "<div class='error'>❌ Error executing: " . htmlspecialchars($shortStatement) . "<br>Error: " . htmlspecialchars($e->getMessage()) . "</div>";

                    // For critical errors, stop execution
                    if (strpos($e->getMessage(), 'syntax error') !== false) {
                        echo "<div class='error'>🛑 Syntax error detected. Stopping migration to prevent further issues.</div>";
                        break;
                    }
                }
            }
            
            echo "<h3>Migration Summary</h3>";
            echo "<div class='info'>📊 Successfully executed: $successCount statements</div>";
            if ($errorCount > 0) {
                echo "<div class='warning'>⚠️ Errors encountered: $errorCount statements</div>";
            }
            
            // Verify the migration
            echo "<h3>Verification</h3>";
            
            try {
                // Check if faq_services table was created
                $result = $database->fetch("SHOW TABLES LIKE 'faq_services'");
                if ($result) {
                    echo "<div class='success'>✅ faq_services table created successfully</div>";
                } else {
                    echo "<div class='error'>❌ faq_services table was not created</div>";
                }
                
                // Check if service_link_text column was added
                $result = $database->fetch("SHOW COLUMNS FROM faqs LIKE 'service_link_text'");
                if ($result) {
                    echo "<div class='success'>✅ service_link_text column added to faqs table</div>";
                } else {
                    echo "<div class='error'>❌ service_link_text column was not added</div>";
                }
                
                // Check if view was created
                $result = $database->fetch("SHOW TABLES LIKE 'faq_with_services'");
                if ($result) {
                    echo "<div class='success'>✅ faq_with_services view created successfully</div>";
                } else {
                    echo "<div class='error'>❌ faq_with_services view was not created</div>";
                }
                
                // Show current counts
                $faqCount = $database->fetch("SELECT COUNT(*) as count FROM faqs")['count'];
                $serviceCount = $database->fetch("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'];
                
                echo "<div class='info'>📈 Current FAQ count: $faqCount</div>";
                echo "<div class='info'>📈 Current active service count: $serviceCount</div>";
                
                if ($errorCount === 0) {
                    echo "<div class='success'><h3>🎉 Migration completed successfully!</h3></div>";
                    echo "<div class='info'>You can now proceed to enhance the admin FAQ management interface.</div>";
                } else {
                    echo "<div class='warning'><h3>⚠️ Migration completed with some errors</h3></div>";
                    echo "<div class='info'>Please review the errors above and fix them before proceeding.</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ Verification failed: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Migration failed: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        
        echo "<br><a href='admin/faq/' class='btn'>Go to FAQ Management</a>";
        echo " <a href='faq.php' class='btn' style='background: #3b82f6;'>View Public FAQ Page</a>";
        
    } else {
        ?>
        <div class="warning">
            <h3>⚠️ Important Notes:</h3>
            <ul>
                <li>This migration will modify your database structure</li>
                <li>It will add a new table: <code>faq_services</code></li>
                <li>It will add a new column: <code>service_link_text</code> to the <code>faqs</code> table</li>
                <li>It will create a view: <code>faq_with_services</code></li>
                <li>It will create stored procedures for FAQ-Service operations</li>
                <li>Make sure you have a database backup before proceeding</li>
            </ul>
        </div>
        
        <form method="post">
            <button type="submit" name="apply_migration" class="btn">Apply Migration</button>
        </form>
        <?php
    }
    ?>
</body>
</html>
