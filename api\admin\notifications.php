<?php
/**
 * Admin Notifications API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetNotifications();
            break;
        case 'POST':
            handleCreateNotification();
            break;
        case 'PUT':
            handleUpdateNotification();
            break;
        case 'DELETE':
            handleDeleteNotification();
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetNotifications() {
    global $database;

    try {
        // Check if notifications table exists
        $database->fetch("SELECT 1 FROM notifications LIMIT 1");
    } catch (Exception $e) {
        // Table doesn't exist
        http_response_code(500);
        echo json_encode([
            'error' => 'Notifications table does not exist. Please run the migration first.',
            'migration_url' => '/admin/notifications/migrate.php'
        ]);
        return;
    }

    $userId = $_SESSION['user_id'];

    // Check if requesting a single notification by ID
    if (isset($_GET['id'])) {
        handleGetSingleNotification($_GET['id'], $userId);
        return;
    }

    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $category = $_GET['category'] ?? null;
    $unreadOnly = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
    $offset = ($page - 1) * $limit;
    
    // Build WHERE clause
    $whereConditions = ['user_id = ?'];
    $params = [$userId];
    
    if ($category) {
        $whereConditions[] = 'category = ?';
        $params[] = $category;
    }
    
    if ($unreadOnly) {
        $whereConditions[] = 'is_read = FALSE';
    }
    
    // Add expiration check
    $whereConditions[] = '(expires_at IS NULL OR expires_at > NOW())';
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Get notifications
    $notifications = $database->fetchAll(
        "SELECT * FROM notifications 
         WHERE {$whereClause}
         ORDER BY 
            CASE priority 
                WHEN 'URGENT' THEN 1 
                WHEN 'HIGH' THEN 2 
                WHEN 'MEDIUM' THEN 3 
                WHEN 'LOW' THEN 4 
            END,
            created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, [$limit, $offset])
    );
    
    // Get total count
    $totalCount = $database->fetch(
        "SELECT COUNT(*) as count FROM notifications WHERE {$whereClause}",
        $params
    )['count'];
    
    // Get unread count
    $unreadCount = $database->fetch(
        "SELECT COUNT(*) as count FROM notifications 
         WHERE user_id = ? AND is_read = FALSE AND (expires_at IS NULL OR expires_at > NOW())",
        [$userId]
    )['count'];
    
    // Get category counts
    $categoryCounts = $database->fetchAll(
        "SELECT category, COUNT(*) as count 
         FROM notifications 
         WHERE user_id = ? AND is_read = FALSE AND (expires_at IS NULL OR expires_at > NOW())
         GROUP BY category",
        [$userId]
    );
    
    $categoryCountsMap = [];
    foreach ($categoryCounts as $cat) {
        $categoryCountsMap[$cat['category']] = (int)$cat['count'];
    }
    
    // Format notifications
    $formattedNotifications = [];
    foreach ($notifications as $notification) {
        $formattedNotifications[] = formatNotification($notification);
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'notifications' => $formattedNotifications,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($totalCount / $limit),
                'total_count' => $totalCount,
                'per_page' => $limit
            ],
            'counts' => [
                'unread' => $unreadCount,
                'categories' => $categoryCountsMap
            ]
        ]
    ]);
}

function handleGetSingleNotification($notificationId, $userId) {
    global $database;

    // Get single notification
    $notification = $database->fetch(
        "SELECT * FROM notifications
         WHERE id = ? AND user_id = ? AND (expires_at IS NULL OR expires_at > NOW())",
        [$notificationId, $userId]
    );

    if (!$notification) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Notification not found'
        ]);
        return;
    }

    echo json_encode([
        'success' => true,
        'data' => formatNotification($notification)
    ]);
}

function handleCreateNotification() {
    global $database;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    $required = ['title', 'message', 'type'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '{$field}' is required"]);
            return;
        }
    }
    
    $userId = $input['user_id'] ?? $_SESSION['user_id'];
    $title = $input['title'];
    $message = $input['message'];
    $type = $input['type'];
    $priority = $input['priority'] ?? 'MEDIUM';
    $actionUrl = $input['action_url'] ?? null;
    $metadata = $input['metadata'] ?? null;
    $expiresAt = $input['expires_at'] ?? null;
    
    $options = [
        'priority' => $priority,
        'action_url' => $actionUrl,
        'metadata' => $metadata,
        'expires_at' => $expiresAt
    ];
    
    $notificationId = createNotification($userId, $title, $message, $type, $options);
    
    if ($notificationId) {
        echo json_encode([
            'success' => true,
            'data' => ['notification_id' => $notificationId]
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create notification']);
    }
}

function handleUpdateNotification() {
    global $database;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $notificationId = $_GET['id'] ?? $input['id'] ?? null;
    
    if (!$notificationId) {
        http_response_code(400);
        echo json_encode(['error' => 'Notification ID is required']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    
    // Check if notification exists and belongs to user
    $notification = $database->fetch(
        "SELECT * FROM notifications WHERE id = ? AND user_id = ?",
        [$notificationId, $userId]
    );
    
    if (!$notification) {
        http_response_code(404);
        echo json_encode(['error' => 'Notification not found']);
        return;
    }
    
    // Update notification (typically just marking as read)
    if (isset($input['is_read'])) {
        $database->query(
            "UPDATE notifications SET is_read = ?, updated_at = NOW() WHERE id = ?",
            [$input['is_read'] ? 1 : 0, $notificationId]
        );
    }
    
    echo json_encode(['success' => true]);
}

function handleDeleteNotification() {
    global $database;
    
    $notificationId = $_GET['id'] ?? null;
    
    if (!$notificationId) {
        http_response_code(400);
        echo json_encode(['error' => 'Notification ID is required']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    
    // Check if notification exists and belongs to user
    $notification = $database->fetch(
        "SELECT * FROM notifications WHERE id = ? AND user_id = ?",
        [$notificationId, $userId]
    );
    
    if (!$notification) {
        http_response_code(404);
        echo json_encode(['error' => 'Notification not found']);
        return;
    }
    
    // Delete notification
    $database->query("DELETE FROM notifications WHERE id = ?", [$notificationId]);
    
    echo json_encode(['success' => true]);
}

function formatNotification($notification) {
    $metadata = null;
    if ($notification['metadata']) {
        $metadata = json_decode($notification['metadata'], true);
    }

    return [
        'id' => $notification['id'],
        'title' => html_entity_decode($notification['title'], ENT_QUOTES, 'UTF-8'),
        'message' => html_entity_decode($notification['message'], ENT_QUOTES, 'UTF-8'),
        'type' => $notification['type'],
        'category' => $notification['category'],
        'priority' => $notification['priority'],
        'is_read' => (bool)$notification['is_read'],
        'action_url' => $notification['action_url'],
        'metadata' => $metadata,
        'expires_at' => $notification['expires_at'],
        'created_at' => $notification['created_at'],
        'updated_at' => $notification['updated_at'],
        'time_ago' => timeAgo($notification['created_at'])
    ];
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}
?>
