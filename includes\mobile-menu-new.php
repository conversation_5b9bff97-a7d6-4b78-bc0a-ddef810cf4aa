<?php
/**
 * Enhanced Mobile Menu Component
 * Modern slide-out navigation menu for mobile devices with medical aesthetics styling
 */

// Get current path for active state
$currentPath = $_SERVER['REQUEST_URI'];
$basePath = getBasePath();
?>

<!-- Enhanced Mobile Menu Overlay -->
<div class="lg:hidden fixed inset-0 z-50 hidden" id="mobile-menu-overlay">
    <!-- Background overlay with blur effect -->
    <div class="fixed inset-0 bg-black/60 backdrop-blur-sm transition-all duration-300" id="mobile-menu-backdrop"></div>
    
    <!-- Enhanced Menu Panel -->
    <div class="mobile-menu fixed inset-y-0 left-0 z-50 w-full max-w-sm glass-effect shadow-2xl">
        <div class="flex h-full flex-col">
            <!-- Enhanced Header -->
            <div class="flex items-center justify-between px-6 py-6 border-b border-white/20">
                <div class="flex items-center space-x-4">
                    <div class="logo-icon w-12 h-12 rounded-2xl flex items-center justify-center shadow-xl">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-xl font-heading font-bold gradient-text">Redolence</h2>
                        <p class="text-sm text-gray-600 font-medical -mt-1 tracking-wide">Medi Aesthetics</p>
                    </div>
                </div>
                <button class="hamburger p-3 rounded-xl hover:bg-white/10 transition-all duration-300" id="mobile-menu-close">
                    <div class="w-6 h-6 flex flex-col justify-center items-center space-y-1">
                        <span class="hamburger-line w-6 h-0.5 bg-gray-700 block transform rotate-45 translate-y-1.5"></span>
                        <span class="hamburger-line w-6 h-0.5 bg-gray-700 block opacity-0"></span>
                        <span class="hamburger-line w-6 h-0.5 bg-gray-700 block transform -rotate-45 -translate-y-1.5"></span>
                    </div>
                </button>
            </div>

            <!-- Enhanced Navigation -->
            <nav class="flex-1 px-6 py-6 space-y-3 overflow-y-auto">
                <!-- Home -->
                <?php $isActive = $currentPath === $basePath . '/'; ?>
                <a href="<?= $basePath ?>/" 
                   class="mobile-menu-item block px-6 py-4 text-base font-medium rounded-2xl transition-all duration-300 <?= $isActive ? 'bg-gradient-to-r from-redolence-green/20 to-redolence-blue/20 text-redolence-green border-l-4 border-redolence-green' : 'text-gray-700 hover:bg-white/10' ?>">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold">Home</div>
                            <div class="text-xs text-gray-500">Welcome page</div>
                        </div>
                    </div>
                </a>

                <!-- Services -->
                <?php $isServicesActive = $currentPath === $basePath . '/services' || strpos($currentPath, $basePath . '/services') === 0; ?>
                <a href="<?= $basePath ?>/services" 
                   class="mobile-menu-item block px-6 py-4 text-base font-medium rounded-2xl transition-all duration-300 <?= $isServicesActive ? 'bg-gradient-to-r from-redolence-green/20 to-redolence-blue/20 text-redolence-green border-l-4 border-redolence-green' : 'text-gray-700 hover:bg-white/10' ?>">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold">Services</div>
                            <div class="text-xs text-gray-500">Medical treatments</div>
                        </div>
                    </div>
                </a>

                <!-- Enhanced Navigation Items -->
                <?php
                $enhancedNavigation = [
                    [
                        'name' => 'Offers', 
                        'href' => $basePath . '/offers', 
                        'icon' => 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
                        'description' => 'Special deals'
                    ],
                    [
                        'name' => 'Gallery', 
                        'href' => $basePath . '/gallery', 
                        'icon' => 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z',
                        'description' => 'Before & after'
                    ],
                    [
                        'name' => 'About', 
                        'href' => $basePath . '/about', 
                        'icon' => 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
                        'description' => 'Our story'
                    ],
                    [
                        'name' => 'Contact', 
                        'href' => $basePath . '/contact', 
                        'icon' => 'M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z',
                        'description' => 'Get in touch'
                    ],
                ];

                foreach ($enhancedNavigation as $item):
                    $isActive = $currentPath === $item['href'] || ($item['href'] !== $basePath . '/' && strpos($currentPath, $item['href']) === 0);
                ?>
                    <a href="<?= $item['href'] ?>" 
                       class="mobile-menu-item block px-6 py-4 text-base font-medium rounded-2xl transition-all duration-300 <?= $isActive ? 'bg-gradient-to-r from-redolence-green/20 to-redolence-blue/20 text-redolence-green border-l-4 border-redolence-green' : 'text-gray-700 hover:bg-white/10' ?>">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 flex items-center justify-center mr-4">
                                <svg class="w-5 h-5 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?= $item['icon'] ?>"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold"><?= $item['name'] ?></div>
                                <div class="text-xs text-gray-500"><?= $item['description'] ?></div>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>

                <!-- Divider -->
                <div class="border-t border-white/20 my-6"></div>

                <!-- Enhanced User Account Section -->
                <?php if (isLoggedIn()): ?>
                    <?php $user = getCurrentUser(); ?>
                    <div class="bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50">
                        <!-- User Info Header -->
                        <div class="px-6 py-4 border-b border-gray-100">
                            <div class="flex items-center space-x-3">
                                <?php if ($user['role'] === 'ADMIN'): ?>
                                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076-.124a6.57 6.57 0 01-.22-.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                <?php elseif ($user['role'] === 'STAFF'): ?>
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 717.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                        </svg>
                                    </div>
                                <?php else: ?>
                                    <div class="w-12 h-12 bg-gradient-to-br from-redolence-green to-green-600 rounded-full flex items-center justify-center shadow-lg">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                <div>
                                    <h3 class="font-bold text-gray-900">
                                        <?php if ($user['role'] === 'ADMIN'): ?>
                                            Administrator
                                        <?php elseif ($user['role'] === 'STAFF'): ?>
                                            Staff Member
                                        <?php else: ?>
                                            <?= htmlspecialchars($user['name']) ?>
                                        <?php endif; ?>
                                    </h3>
                                    <p class="text-sm text-gray-500 capitalize"><?= strtolower($user['role']) ?> Account</p>
                                </div>
                            </div>
                        </div>

                        <!-- Menu Items -->
                        <div class="py-2">
                            <!-- Dashboard -->
                            <a href="<?= $basePath . ($user['role'] === 'ADMIN' ? '/admin' : ($user['role'] === 'STAFF' ? '/staff' : '/customer')) ?>"
                               class="flex items-center px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-redolence-green/10 hover:to-redolence-blue/10 hover:text-redolence-green transition-all duration-200 group">
                                <div class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center mr-3 group-hover:bg-redolence-green/10 group-hover:scale-110 transition-all duration-200">
                                    <svg class="w-5 h-5 text-gray-600 group-hover:text-redolence-green" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold">Dashboard</div>
                                    <div class="text-xs text-gray-500">Manage your account</div>
                                </div>
                            </a>

                            <?php if ($user['role'] === 'CUSTOMER'): ?>
                                <!-- My Bookings -->
                                <a href="<?= $basePath ?>/customer/bookings"
                                   class="flex items-center px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-redolence-green/10 hover:to-redolence-blue/10 hover:text-redolence-green transition-all duration-200 group">
                                    <div class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center mr-3 group-hover:bg-redolence-green/10 group-hover:scale-110 transition-all duration-200">
                                        <svg class="w-5 h-5 text-gray-600 group-hover:text-redolence-green" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 715.25 9h13.5a2.25 2.25 0 712.25 2.25v7.5" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-semibold">My Bookings</div>
                                        <div class="text-xs text-gray-500">View appointments</div>
                                    </div>
                                </a>

                                <!-- Profile -->
                                <a href="<?= $basePath ?>/customer/profile"
                                   class="flex items-center px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-redolence-green/10 hover:to-redolence-blue/10 hover:text-redolence-green transition-all duration-200 group">
                                    <div class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center mr-3 group-hover:bg-redolence-green/10 group-hover:scale-110 transition-all duration-200">
                                        <svg class="w-5 h-5 text-gray-600 group-hover:text-redolence-green" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-semibold">Profile Settings</div>
                                        <div class="text-xs text-gray-500">Update your info</div>
                                    </div>
                                </a>
                            <?php endif; ?>

                            <!-- Divider -->
                            <div class="border-t border-gray-100 my-2 mx-6"></div>

                            <!-- Logout -->
                            <a href="<?= $basePath ?>/auth/logout.php"
                               class="flex items-center px-6 py-3 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 transition-all duration-200 group rounded-b-2xl">
                                <div class="w-10 h-10 bg-red-50 rounded-xl flex items-center justify-center mr-3 group-hover:bg-red-100 group-hover:scale-110 transition-all duration-200">
                                    <svg class="w-5 h-5 text-red-500 group-hover:text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold">Sign Out</div>
                                    <div class="text-xs text-red-400">End your session</div>
                                </div>
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 p-6">
                        <div class="text-center">
                            <div class="text-sm text-gray-600 mb-4">Join Redolence today</div>
                            <div class="space-y-3">
                                <a href="<?= $basePath ?>/auth/login.php" class="block w-full bg-redolence-green hover:bg-green-600 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 text-center shadow-lg hover:shadow-xl">Sign In</a>
                                <a href="<?= $basePath ?>/auth/register.php" class="block w-full border-2 border-redolence-green text-redolence-green hover:bg-redolence-green hover:text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 text-center">Register</a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </nav>

            <!-- Enhanced Footer -->
            <div class="px-6 py-6 border-t border-white/20">
                <a href="<?= getBasePath() ?>/customer/book" 
                   class="cta-button w-full text-white py-4 px-6 rounded-2xl font-semibold transition-all duration-300 shadow-lg text-center inline-block relative overflow-hidden">
                    <svg class="w-5 h-5 mr-2 inline relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                    </svg>
                    <span class="relative z-10">Book Consultation</span>
                </a>
                
                <!-- Contact Info -->
                <div class="mt-4 text-center">
                    <div class="text-xs text-gray-500 mb-2">Need help? Call us</div>
                    <a href="tel:+255123456789" class="text-sm font-semibold text-redolence-green hover:text-redolence-blue transition-colors">
                        +255 123 456 789
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Mobile Menu Styles -->
<style>
/* Mobile Menu Enhancements */
.mobile-menu {
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.open {
    transform: translateX(0);
}

.mobile-menu-item {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.mobile-menu-item:hover {
    background: rgba(73, 167, 92, 0.05);
    border-left-color: #49a75c;
    transform: translateX(8px);
}

/* Hamburger Animation for Close Button */
.hamburger {
    transition: all 0.3s ease;
}

.hamburger-line {
    transition: all 0.3s ease;
    transform-origin: center;
}

/* Smooth Scrolling for Mobile Menu */
.mobile-menu nav {
    scroll-behavior: smooth;
}

/* Enhanced Glass Effect for Mobile */
@supports (backdrop-filter: blur(20px)) {
    .mobile-menu {
        backdrop-filter: blur(20px) saturate(180%);
        background: rgba(255, 255, 255, 0.9);
        border-right: 1px solid rgba(255, 255, 255, 0.3);
    }
}

/* Fallback for browsers without backdrop-filter */
@supports not (backdrop-filter: blur(20px)) {
    .mobile-menu {
        background: rgba(255, 255, 255, 0.95);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
    const mobileMenuClose = document.getElementById('mobile-menu-close');
    const mobileMenuBackdrop = document.getElementById('mobile-menu-backdrop');
    const mobileMenu = document.querySelector('.mobile-menu');
    const hamburgerButton = document.querySelector('.hamburger');

    function openMobileMenu() {
        mobileMenuOverlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
        if (hamburgerButton) {
            hamburgerButton.classList.add('active');
        }
        setTimeout(() => {
            mobileMenu.classList.add('open');
        }, 10);
    }

    function closeMobileMenu() {
        mobileMenu.classList.remove('open');
        document.body.style.overflow = ''; // Restore scrolling
        if (hamburgerButton) {
            hamburgerButton.classList.remove('active');
        }
        setTimeout(() => {
            mobileMenuOverlay.classList.add('hidden');
        }, 400);
    }

    if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', openMobileMenu);
    }

    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', closeMobileMenu);
    }

    if (mobileMenuBackdrop) {
        mobileMenuBackdrop.addEventListener('click', closeMobileMenu);
    }

    // Close menu when clicking on navigation links
    const mobileNavLinks = mobileMenuOverlay.querySelectorAll('a');
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', closeMobileMenu);
    });

    // Handle escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !mobileMenuOverlay.classList.contains('hidden')) {
            closeMobileMenu();
        }
    });

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';
});
</script>
