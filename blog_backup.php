<?php
/**
 * Blog Page - Revolutionary Medical Aesthetics Blog
 * Displays all blog posts or a single blog post with revolutionary design
 * Redolence Medi Aesthetics - Advanced Medical Beauty
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/blog_functions.php';

// Check if we're viewing a single blog post
$slug = $_GET['slug'] ?? null;
$search = $_GET['search'] ?? '';
$singlePost = null;

if ($slug) {
    // Get the single blog post by slug
    $singlePost = $database->fetch(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.slug = ? AND bp.status = 'published'",
        [$slug]
    );

    // If post not found, redirect to blog index
    if (!$singlePost) {
        redirect('/blog');
    }

    $pageTitle = $singlePost['title'] . ' - Redolence Medi Aesthetics Blog';
} else {
    // Get search and pagination parameters
    $page = (int)($_GET['page'] ?? 1);
    $limit = 9; // Increased for better grid layout
    $offset = ($page - 1) * $limit;

    // Build search query
    $searchCondition = '';
    $searchConditionCount = '';
    $searchParams = [];
    if (!empty($search)) {
        $searchCondition = " AND (bp.title LIKE ? OR bp.summary LIKE ? OR bp.full_content LIKE ?)";
        $searchConditionCount = " AND (title LIKE ? OR summary LIKE ? OR full_content LIKE ?)";
        $searchTerm = '%' . $search . '%';
        $searchParams = [$searchTerm, $searchTerm, $searchTerm];
    }

    // Get total posts count
    $totalPosts = $database->fetch(
        "SELECT COUNT(*) as count FROM blog_posts WHERE status = 'published'" . $searchConditionCount,
        $searchParams
    )['count'];

    // Get featured posts (latest 3 posts)
    $featuredPosts = $database->fetchAll(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.status = 'published'
         ORDER BY bp.publish_date DESC, bp.created_at DESC
         LIMIT 3"
    );

    // Get regular blog posts (excluding featured ones if on first page)
    $excludeFeatured = ($page === 1 && empty($search)) ? " AND bp.id NOT IN ('" . implode("','", array_column($featuredPosts, 'id')) . "')" : '';

    $blogPosts = $database->fetchAll(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.status = 'published'" . $searchCondition . $excludeFeatured . "
         ORDER BY bp.publish_date DESC, bp.created_at DESC
         LIMIT $limit OFFSET $offset",
        $searchParams
    );

    $totalPages = ceil($totalPosts / $limit);
    $pageTitle = !empty($search) ? "Search: $search - Medical Aesthetics Blog" : 'Medical Aesthetics Blog - Redolence';
}

// Include header
include __DIR__ . '/includes/header.php';
?>

<!-- Critical CSS for Revolutionary Medical Aesthetics Blog -->
<style>
/* Revolutionary Medical Aesthetics Design System */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --accent-gold: #f4d03f;
    --deep-navy: #1a2332;
    --soft-gray: #f8fafc;
    --medical-white: #ffffff;
    --shadow-primary: rgba(73, 167, 92, 0.15);
    --shadow-blue: rgba(88, 148, 210, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
}

/* Advanced Animation Framework */
@keyframes morphingGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes floatingElement {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(5deg); }
    66% { transform: translateY(-10px) rotate(-3deg); }
}

@keyframes pulseGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(73, 167, 92, 0.3); }
    50% { box-shadow: 0 0 40px rgba(88, 148, 210, 0.5); }
}

@keyframes slideInFromLeft {
    0% { opacity: 0; transform: translateX(-100px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes slideInFromRight {
    0% { opacity: 0; transform: translateX(100px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

/* Revolutionary Card System for Blog */
.medical-blog-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
    backdrop-filter: blur(20px);
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    border-radius: 24px;
}

.medical-blog-card::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: var(--gradient-primary);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    opacity: 0;
    transition: opacity 0.6s ease;
}

.medical-blog-card:hover::before {
    opacity: 1;
}

.medical-blog-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(73, 167, 92, 0.2);
}

/* Featured Blog Card */
.featured-blog-card {
    background: linear-gradient(145deg, rgba(73, 167, 92, 0.1), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    border-radius: 24px;
}

.featured-blog-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 35px 70px rgba(73, 167, 92, 0.3);
}

/* Morphing Background System */
.morphing-bg {
    background: linear-gradient(-45deg, #49a75c, #5894d2, #6ba3d6, #4db86d);
    background-size: 400% 400%;
    animation: morphingGradient 8s ease infinite;
}

/* Advanced Typography */
.text-revolutionary {
    background: var(--gradient-primary);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: morphingGradient 6s ease infinite;
}

/* Floating Elements System */
.floating-medical {
    animation: floatingElement 6s ease-in-out infinite;
}

.floating-medical:nth-child(2) { animation-delay: 2s; }
.floating-medical:nth-child(3) { animation-delay: 4s; }

/* Interactive Hover States */
.interactive-element {
    transition: all 0.3s ease;
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-3px);
}

/* Advanced Grid System */
.medical-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* Scroll-triggered Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Revolutionary Button System */
.btn-revolutionary {
    background: var(--gradient-primary);
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.btn-revolutionary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.btn-revolutionary:hover::before {
    left: 100%;
}

.btn-revolutionary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(73, 167, 92, 0.4);
}

/* Text Utilities */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Enhanced Search Bar */
.search-revolutionary {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 50px;
    transition: all 0.3s ease;
}

.search-revolutionary:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 20px rgba(73, 167, 92, 0.3);
}

/* Pagination Revolutionary Style */
.pagination-revolutionary {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 50px;
    transition: all 0.3s ease;
}

.pagination-revolutionary:hover {
    border-color: var(--primary-green);
    background: var(--gradient-primary);
    color: white;
}

.pagination-revolutionary.active {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-green);
}

/* Medical Professional Blog Styling */
.blog-meta-revolutionary {
    background: rgba(73, 167, 92, 0.1);
    border-radius: 50px;
    padding: 0.5rem 1rem;
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--primary-green);
    font-weight: 600;
}

/* Enhanced Typography for Blog Content */
.prose-revolutionary {
    color: #374151;
    line-height: 1.8;
    font-size: 1.125rem;
}

.prose-revolutionary h1, .prose-revolutionary h2, .prose-revolutionary h3, .prose-revolutionary h4 {
    color: var(--deep-navy);
    font-weight: 700;
    margin-top: 2em;
    margin-bottom: 1em;
    position: relative;
}

.prose-revolutionary h2 {
    font-size: 1.875em;
    line-height: 1.3;
    border-bottom: 2px solid rgba(73, 167, 92, 0.2);
    padding-bottom: 0.5em;
}

.prose-revolutionary h2::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 0.5em;
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--primary-green);
    border-radius: 50%;
}

.prose-revolutionary a {
    color: var(--primary-green);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.3s ease;
}

.prose-revolutionary a:hover {
    border-bottom-color: var(--primary-green);
    background-color: rgba(73, 167, 92, 0.1);
    border-radius: 0.125rem;
    padding: 0 0.25rem;
}

.prose-revolutionary blockquote {
    border-left: 4px solid var(--primary-green);
    padding-left: 1.5em;
    margin: 2em 0;
    font-style: italic;
    background: rgba(73, 167, 92, 0.05);
    padding: 1.5em 2em;
    border-radius: 0.5rem;
    position: relative;
}

.prose-revolutionary code {
    background: rgba(73, 167, 92, 0.1);
    color: var(--primary-green);
    padding: 0.25em 0.5em;
    border-radius: 0.25rem;
    font-size: 0.9em;
}

/* Responsive Design */
@media (max-width: 768px) {
    .medical-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .medical-blog-card {
        margin: 0;
        padding: 1rem;
    }
    
    /* Mobile Hero Section */
    .relative.min-h-screen h1 {
        font-size: 3rem !important;
        line-height: 1.1;
    }
    
    .relative.min-h-screen h1 span {
        font-size: 2.5rem !important;
    }
    
    .relative.min-h-screen p {
        font-size: 1.1rem !important;
    }
    
    /* Mobile Section Headings */
    h2 {
        font-size: 2.5rem !important;
    }
    
    .text-6xl, .text-7xl {
        font-size: 2.5rem !important;
    }
    
    /* Mobile Cards */
    .medical-blog-card {
        padding: 1.5rem;
    }
    
    /* Mobile Sections Padding */
    .py-32 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .py-16 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    .py-24 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(73, 167, 92, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(73, 167, 92, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(73, 167, 92, 0.7);
}

/* Focus States */
a:focus, button:focus, input:focus {
    outline: 2px solid rgba(73, 167, 92, 0.5);
    outline-offset: 2px;
}
</style>

<!-- Revolutionary Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Dynamic Background -->
    <div class="absolute inset-0 morphing-bg"></div>
    
    <!-- Floating Medical Elements -->
    <div class="absolute inset-0 pointer-events-none">
        <div class="floating-medical absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full backdrop-blur-sm"></div>
        <div class="floating-medical absolute top-40 right-20 w-24 h-24 bg-white/15 rounded-full backdrop-blur-sm"></div>
        <div class="floating-medical absolute bottom-32 left-1/4 w-40 h-40 bg-white/8 rounded-full backdrop-blur-sm"></div>
        <div class="floating-medical absolute bottom-20 right-10 w-28 h-28 bg-white/12 rounded-full backdrop-blur-sm"></div>
    </div>
    
    <!-- Hero Content -->
    <div class="relative z-10 text-center text-white px-6 max-w-6xl mx-auto">
        <?php if ($singlePost): ?>
            <!-- Single Post Hero -->
            <div class="scroll-reveal">
                <div class="inline-flex items-center bg-white/20 backdrop-blur-sm px-8 py-4 rounded-full mb-8 border border-white/30">
                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                    <span class="font-semibold text-lg">MEDICAL AESTHETICS INSIGHTS</span>
                </div>
            </div>
            
            <h1 class="text-5xl md:text-7xl font-black mb-8 leading-none scroll-reveal" style="animation-delay: 0.2s;">
                <?= htmlspecialchars($singlePost['title']) ?>
            </h1>
            
            <div class="flex flex-wrap items-center justify-center gap-6 text-white/80 mb-8 scroll-reveal" style="animation-delay: 0.4s;">
                <div class="flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <?= htmlspecialchars($singlePost['author_name'] ?? 'Redolence Medical Team') ?>
                </div>
                <div class="flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <?= $singlePost['publish_date'] ? date('F j, Y', strtotime($singlePost['publish_date'])) : date('F j, Y', strtotime($singlePost['created_at'])) ?>
                </div>
                <div class="flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <?php
                    $wordCount = str_word_count(strip_tags($singlePost['full_content']));
                    $readTime = max(1, ceil($wordCount / 200));
                    echo "$readTime min read";
                    ?>
                </div>
            </div>
        <?php else: ?>
            <!-- Blog Index Hero -->
            <div class="scroll-reveal">
                <div class="inline-flex items-center bg-white/20 backdrop-blur-sm px-8 py-4 rounded-full mb-8 border border-white/30">
                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="font-semibold text-lg">CERTIFIED MEDICAL AESTHETICS INSIGHTS</span>
                </div>
            </div>
            
            <h1 class="text-7xl md:text-9xl font-black mb-8 leading-none scroll-reveal" style="animation-delay: 0.2s;">
                MEDICAL
                <span class="block text-6xl md:text-8xl font-light opacity-90">Aesthetics Blog</span>
            </h1>
            
            <p class="text-2xl md:text-4xl font-light mb-12 leading-relaxed scroll-reveal" style="animation-delay: 0.4s;">
                Where <strong>Medical Science</strong> meets <strong>Beauty Innovation</strong>
                <span class="block mt-4 text-xl md:text-2xl opacity-80">Expert insights from certified medical professionals</span>
            </p>
            
            <!-- Revolutionary Search Bar -->
            <div class="max-w-2xl mx-auto relative z-10 scroll-reveal" style="animation-delay: 0.6s;">
                <form method="GET" action="<?= getBasePath() ?>/blog" class="relative group">
                    <div class="search-revolutionary p-2 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-primary-green/20">
                        <input
                            type="text"
                            name="search"
                            value="<?= htmlspecialchars($search) ?>"
                            placeholder="Search medical aesthetics insights..."
                            class="w-full px-6 py-4 pl-14 bg-transparent border-none text-gray-800 placeholder-gray-500 focus:outline-none text-lg"
                        >
                        <svg class="absolute left-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-primary-green transition-all duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <button type="submit" class="btn-revolutionary absolute right-2 top-1/2 transform -translate-y-1/2 px-6 py-2 text-sm">
                            Search
                        </button>
                    </div>
                </form>
            </div>
        <?php endif; ?>
        
        <!-- Enhanced Breadcrumb -->
        <div class="flex items-center justify-center space-x-2 text-white/70 mt-12 bg-white/10 backdrop-blur-sm py-3 px-6 rounded-full inline-flex mx-auto scroll-reveal" style="animation-delay: 0.8s;">
            <a href="<?= getBasePath() ?>/" class="hover:text-white transition-colors flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                Home
            </a>
            <svg class="w-4 h-4 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <?php if ($singlePost): ?>
                <a href="<?= getBasePath() ?>/blog" class="hover:text-white transition-colors">Medical Blog</a>
                <svg class="w-4 h-4 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-white truncate max-w-[150px] sm:max-w-xs"><?= htmlspecialchars($singlePost['title']) ?></span>
            <?php else: ?>
                <span class="text-white">Medical Aesthetics Blog</span>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>

<!-- Revolutionary Main Content Section -->
<section class="py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 75% 75%, var(--primary-blue) 2px, transparent 2px); background-size: 80px 80px;"></div>
    </div>
    
    <div class="max-w-7xl mx-auto px-6 relative"<?php if ($singlePost): ?>
            <!-- Revolutionary Single Blog Post View -->
            <article class="max-w-4xl mx-auto scroll-reveal">
                <!-- Featured Image with Medical Aesthetics Design -->
                <?php if ($singlePost['image_url']): ?>
                <div class="medical-card-revolutionary rounded-3xl overflow-hidden mb-12 group">
                    <div class="aspect-w-16 aspect-h-9 overflow-hidden">
                        <img
                            src="<?= htmlspecialchars(getBlogImageUrl($singlePost['image_url'])) ?>"
                            alt="<?= htmlspecialchars($singlePost['title']) ?>"
                            class="w-full h-[400px] object-cover transition-transform duration-700 group-hover:scale-105"
                        >
                    </div>
                    <!-- Medical Aesthetics Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-t from-primary-green/20 via-transparent to-transparent opacity-60"></div>
                </div>
                <?php endif; ?>

                <!-- Revolutionary Article Content -->
                <div class="medical-card-revolutionary rounded-3xl p-8 md:p-12">
                    <!-- Article Summary with Medical Design -->
                    <?php if (!empty($singlePost['summary'])): ?>
                    <div class="bg-gradient-to-r from-primary-green/10 to-primary-blue/10 border-l-4 border-primary-green p-6 rounded-r-2xl mb-8 relative overflow-hidden">
                        <!-- Medical Quote Icon -->
                        <div class="absolute right-4 top-4 opacity-20">
                            <svg class="w-12 h-12 text-primary-green" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
                            </svg>
                        </div>
                        <p class="text-lg text-gray-700 italic leading-relaxed relative z-10 font-medium">
                            <?= htmlspecialchars($singlePost['summary']) ?>
                        </p>
                    </div>
                    <?php endif; ?>

                    <!-- Revolutionary Article Body -->
                    <div class="prose-revolutionary max-w-none">
                        <?= $singlePost['full_content'] ?>
                    </div>

                    <!-- Revolutionary Article Footer -->
                    <div class="mt-12 pt-8 border-t border-gray-200">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
                            <!-- Revolutionary Back Button -->
                            <a href="<?= getBasePath() ?>/blog" class="btn-revolutionary inline-flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                </svg>
                                Back to Medical Blog
                            </a>

                            <!-- Revolutionary Share Buttons -->
                            <div class="flex items-center space-x-4">
                                <span class="text-gray-600 font-medium">Share Article:</span>
                                <div class="flex space-x-3">
                                    <!-- Twitter -->
                                    <a href="https://twitter.com/intent/tweet?text=<?= urlencode($singlePost['title']) ?>&url=<?= urlencode(getBasePath() . '/blog?slug=' . $singlePost['slug']) ?>"
                                       target="_blank"
                                       class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white hover:scale-110 transition-all duration-300">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                        </svg>
                                    </a>
                                    
                                    <!-- Facebook -->
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode(getBasePath() . '/blog?slug=' . $singlePost['slug']) ?>"
                                       target="_blank"
                                       class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center text-white hover:scale-110 transition-all duration-300">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                        </svg>
                                    </a>
                                    
                                    <!-- LinkedIn -->
                                    <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?= urlencode(getBasePath() . '/blog?slug=' . $singlePost['slug']) ?>"
                                       target="_blank"
                                       class="w-10 h-10 bg-gradient-to-br from-blue-700 to-blue-800 rounded-full flex items-center justify-center text-white hover:scale-110 transition-all duration-300">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                        </svg>
                                    </a>
                                    
                                    <!-- Copy Link -->
                                    <button onclick="copyToClipboard('<?= getBasePath() . '/blog?slug=' . $singlePost['slug'] ?>')"
                                           class="w-10 h-10 bg-gradient-to-br from-primary-green to-primary-blue rounded-full flex items-center justify-center text-white hover:scale-110 transition-all duration-300">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        <?php else: ?>
            <!-- Search Results Header -->
            <?php if (!empty($search)): ?>
            <div class="mb-12 text-center">
                <h2 class="text-3xl font-bold text-white mb-4">
                    Search Results for "<span class="text-salon-gold"><?= htmlspecialchars($search) ?></span>"
                </h2>
                <p class="text-gray-300">
                    <?= $totalPosts ?> <?= $totalPosts === 1 ? 'post' : 'posts' ?> found
                </p>
            </div>
            <?php endif; ?>

            <!-- Enhanced Featured Posts Section (only on first page without search) -->
            <?php if ($page === 1 && empty($search) && !empty($featuredPosts)): ?>
            <section class="mb-20 relative">
                <!-- Decorative elements -->
                <div class="absolute -top-20 -right-20 w-64 h-64 bg-salon-gold/5 rounded-full blur-3xl opacity-70 pointer-events-none"></div>
                <div class="absolute -bottom-20 -left-20 w-64 h-64 bg-salon-gold/5 rounded-full blur-3xl opacity-70 pointer-events-none"></div>
                
                <div class="flex flex-col md:flex-row md:items-end justify-between mb-12 relative">
                    <div class="relative">
                        <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-2 relative inline-block">
                            Featured <span class="text-salon-gold">Stories</span>
                            <span class="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-salon-gold to-transparent rounded-full"></span>
                        </h2>
                        <p class="text-gray-300 max-w-md">Discover our latest beauty insights and trends</p>
                    </div>
                    <a href="<?= getBasePath() ?>/blog" class="mt-4 md:mt-0 text-sm text-salon-gold hover:text-white transition-colors duration-300 flex items-center group self-start md:self-auto">
                        View all stories
                        <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Enhanced Main Featured Post -->
                    <?php $mainPost = $featuredPosts[0]; ?>
                    <div class="lg:col-span-2">
                        <article class="featured-card rounded-2xl overflow-hidden h-full group relative bg-black/80 backdrop-blur-sm border border-gray-800/50 hover:border-salon-gold/50 transition-all duration-500 shadow-xl hover:shadow-salon-gold/10">
                            <!-- Decorative elements -->
                            <div class="absolute -top-10 -right-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl opacity-0 group-hover:opacity-70 transition-opacity duration-700 pointer-events-none"></div>
                            <div class="absolute -bottom-10 -left-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl opacity-0 group-hover:opacity-70 transition-opacity duration-700 delay-100 pointer-events-none"></div>
                            
                            <div class="relative h-80 lg:h-96 overflow-hidden">
                                <?php if ($mainPost['image_url']): ?>
                                    <img
                                        src="<?= htmlspecialchars(getBlogImageUrl($mainPost['image_url'])) ?>"
                                        alt="<?= htmlspecialchars($mainPost['title']) ?>"
                                        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                                        loading="lazy"
                                    >
                                    <!-- Enhanced gradient overlay -->
                                    <div class="absolute inset-0 bg-gradient-to-t from-salon-black via-salon-black/50 to-transparent opacity-80 group-hover:opacity-70 transition-opacity duration-500"></div>
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-salon-gold/20 to-salon-black flex items-center justify-center">
                                        <svg class="w-20 h-20 text-salon-gold/60 animate-pulse-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Enhanced featured badge -->
                                <div class="absolute top-4 left-4 z-10">
                                    <span class="bg-salon-gold/90 text-black px-4 py-1.5 rounded-full text-sm font-bold shadow-lg transform group-hover:scale-105 transition-transform duration-300 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        Featured
                                    </span>
                                </div>
                            </div>
                            
                            <div class="p-8 relative z-10">
                                <!-- Enhanced metadata with icons -->
                                <div class="flex flex-wrap items-center text-gray-400 text-sm mb-4 space-x-4">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-salon-gold/70" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?= $mainPost['publish_date'] ? date('F j, Y', strtotime($mainPost['publish_date'])) : date('F j, Y', strtotime($mainPost['created_at'])) ?>
                                    </span>
                                    <span class="text-salon-gold/50">•</span>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-salon-gold/70" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?= max(1, ceil(str_word_count(strip_tags($mainPost['full_content'])) / 200)) ?> min read
                                    </span>
                                </div>
                                
                                <!-- Enhanced title with hover effect -->
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($mainPost['slug']) ?>" class="block">
                                <h3 class="text-2xl font-bold text-white mb-3 line-clamp-2 group-hover:text-salon-gold transition-colors duration-300">
                                    <?= htmlspecialchars($mainPost['title']) ?>
                                </h3>
                            </a>
                                
                                <!-- Enhanced summary with better typography -->
                                <p class="text-gray-300 mb-6 line-clamp-3 leading-relaxed">
                                    <?= !empty($mainPost['summary']) ? htmlspecialchars($mainPost['summary']) : htmlspecialchars(substr(strip_tags($mainPost['full_content']), 0, 200)) . '...' ?>
                                </p>
                                
                                <!-- Enhanced call-to-action button -->
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($mainPost['slug']) ?>"
                                   class="inline-flex items-center text-salon-gold hover:text-white font-semibold transition-all duration-300 group-hover:translate-x-2 transition-transform ease-in-out bg-black/80 hover:bg-salon-gold/20 px-5 py-2 rounded-full">
                                    Read Full Story
                                    <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </article>
                    </div>

                    <!-- Enhanced Side Featured Posts -->
                    <div class="space-y-6">
                        <?php for ($i = 1; $i < min(3, count($featuredPosts)); $i++): ?>
                            <?php $sidePost = $featuredPosts[$i]; ?>
                            <article class="blog-card rounded-xl overflow-hidden group relative bg-black/80 backdrop-blur-sm border border-gray-800/50 hover:border-salon-gold/50 transition-all duration-500 shadow-lg hover:shadow-salon-gold/10">
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($sidePost['slug']) ?>" class="absolute inset-0 z-10" aria-label="Read more about <?= htmlspecialchars($sidePost['title']) ?>"></a>
                                <!-- Decorative element -->
                                <div class="absolute -bottom-8 -right-8 w-24 h-24 bg-salon-gold/10 rounded-full blur-2xl opacity-0 group-hover:opacity-70 transition-opacity duration-700 pointer-events-none"></div>
                                
                                <div class="flex flex-col sm:flex-row relative">
                                    <div class="sm:w-1/3 h-32 sm:h-auto relative overflow-hidden">
                                        <?php if ($sidePost['image_url']): ?>
                                            <img
                                                src="<?= htmlspecialchars(getBlogImageUrl($sidePost['image_url'])) ?>"
                                                alt="<?= htmlspecialchars($sidePost['title']) ?>"
                                                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                                                loading="lazy"
                                            >
                                            <!-- Gradient overlay -->
                                            <div class="absolute inset-0 bg-gradient-to-r from-transparent to-salon-black/70 sm:bg-gradient-to-r opacity-0 sm:opacity-70 group-hover:opacity-60 transition-opacity duration-500"></div>
                                        <?php else: ?>
                                            <div class="w-full h-full bg-gradient-to-br from-salon-gold/20 to-gray-900 flex items-center justify-center">
                                                <svg class="w-8 h-8 text-salon-gold/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                                </svg>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-1 p-4">
                                        <div class="text-xs text-gray-400 mb-2">
                                            <?= $sidePost['publish_date'] ? date('M j, Y', strtotime($sidePost['publish_date'])) : date('M j, Y', strtotime($sidePost['created_at'])) ?>
                                        </div>
                                        <h4 class="text-sm font-semibold text-white line-clamp-2 group-hover:text-salon-gold transition-colors">
                                            <?= htmlspecialchars($sidePost['title']) ?>
                                        </h4>
                                        <div class="inline-flex items-center text-salon-gold text-xs font-semibold transition-all duration-300 mt-2">
                                            Read Full Story
                                            <svg class="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </article>
                        <?php endfor; ?>
                    </div>
                </div>
            </section>
            <?php endif; ?>

            <!-- Enhanced Regular Blog Posts Grid -->
            <?php if (!empty($blogPosts)): ?>
            <section>
                <?php if ($page === 1 && empty($search) && !empty($featuredPosts)): ?>
                    <div class="flex items-center justify-between mb-12 relative">
                        <!-- Decorative elements -->
                        <div class="absolute -top-10 -left-10 w-32 h-32 bg-salon-gold/5 rounded-full blur-3xl"></div>
                        
                        <div class="relative z-10">
                            <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-3 relative inline-block">
                                Latest <span class="text-salon-gold relative">Articles
                                    <span class="absolute -bottom-1 left-0 w-full h-0.5 bg-salon-gold/30"></span>
                                </span>
                                <!-- Decorative star -->
                                <span class="absolute -top-4 -right-6 text-salon-gold/70 text-sm animate-pulse-gold">✧</span>
                            </h2>
                            <p class="text-gray-300 max-w-md">Stay updated with our beauty and wellness insights</p>
                        </div>
                        <div class="hidden md:flex items-center space-x-2">
                            <div class="w-2 h-2 rounded-full bg-salon-gold/70"></div>
                            <div class="w-32 h-px bg-gradient-to-r from-salon-gold to-transparent"></div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($blogPosts as $post): ?>
                        <article class="blog-card bg-black/80 backdrop-blur-sm rounded-xl overflow-hidden group h-full flex flex-col relative border border-gray-800/80 hover:border-salon-gold/80 transition-all duration-500 shadow-lg hover:shadow-salon-gold/10">
                            <!-- Decorative elements -->
                            <div class="absolute -bottom-10 -right-10 w-32 h-32 bg-salon-gold/10 rounded-full blur-3xl opacity-0 group-hover:opacity-70 transition-opacity duration-700 pointer-events-none"></div>
                            
                            <!-- Post Image with enhanced styling -->
                            <div class="relative h-52 overflow-hidden">
                                <?php if ($post['image_url']): ?>
                                    <img
                                        src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>"
                                        alt="<?= htmlspecialchars($post['title']) ?>"
                                        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                                        loading="lazy"
                                    >
                                    <!-- Enhanced gradient overlay -->
                                    <div class="absolute inset-0 bg-gradient-to-t from-salon-black/90 via-salon-black/50 to-transparent opacity-70 group-hover:opacity-60 transition-opacity duration-500"></div>
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-salon-gold/20 to-secondary-700 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-salon-gold/60 animate-pulse-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Enhanced Post Content -->
                            <div class="p-6 flex flex-col flex-grow relative z-10">
                                <!-- Enhanced Meta Info with icons -->
                                <div class="flex items-center text-gray-400 text-sm mb-4 space-x-3">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-salon-gold/70" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?= $post['publish_date'] ? date('M j, Y', strtotime($post['publish_date'])) : date('M j, Y', strtotime($post['created_at'])) ?>
                                    </span>
                                    <span class="text-salon-gold/50">•</span>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1 text-salon-gold/70" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?= max(1, ceil(str_word_count(strip_tags($post['full_content'])) / 200)) ?> min read
                                    </span>
                                </div>

                                <!-- Enhanced Title with hover effect - Made entire area clickable -->
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>" class="block">
                                    <h3 class="text-xl font-bold text-white mb-3 line-clamp-2 group-hover:text-salon-gold transition-colors duration-300">
                                        <?= htmlspecialchars($post['title']) ?>
                                    </h3>
                                </a>

                                <!-- Enhanced Excerpt with better typography -->
                                <p class="text-gray-300 mb-6 line-clamp-3 flex-grow leading-relaxed">
                                    <?= !empty($post['summary']) ? htmlspecialchars($post['summary']) : htmlspecialchars(substr(strip_tags($post['full_content']), 0, 150)) . '...' ?>
                                </p>

                                <!-- Enhanced Read More Link with animation -->
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>"
                                   class="inline-flex items-center text-salon-gold hover:text-white font-semibold transition-all duration-300 group-hover:translate-x-2 transition-transform ease-in-out mt-auto bg-secondary-800/80 hover:bg-salon-gold/20 px-4 py-1.5 rounded-full">
                                    Read Full Story
                                    <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php else: ?>
                <!-- Enhanced No Posts Found -->
                <div class="text-center py-20 relative">
                    <!-- Decorative elements -->
                    <div class="absolute top-0 left-1/2 -translate-x-1/2 w-64 h-64 bg-salon-gold/5 rounded-full blur-3xl opacity-70 pointer-events-none"></div>
                    <div class="absolute -bottom-20 left-1/4 w-40 h-40 bg-salon-gold/5 rounded-full blur-3xl opacity-50 pointer-events-none"></div>
                    
                    <div class="max-w-md mx-auto relative z-10 bg-secondary-800/50 backdrop-blur-sm p-10 rounded-2xl border border-secondary-700/50 shadow-xl">
                        <!-- Decorative top border -->
                        <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-salon-gold/30 to-transparent"></div>
                        
                        <svg class="w-24 h-24 text-salon-gold/40 mx-auto mb-6 animate-pulse-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                        </svg>
                        
                        <h3 class="text-2xl font-bold text-white mb-4 relative inline-block">
                            <?= !empty($search) ? 'No posts found' : 'No blog posts available' ?>
                            <!-- Decorative underline -->
                            <span class="absolute -bottom-1 left-0 w-full h-0.5 bg-salon-gold/30"></span>
                        </h3>
                        
                        <p class="text-gray-300 mb-8 leading-relaxed">
                            <?= !empty($search) ? "Try adjusting your search terms or browse all posts." : "We're working on creating amazing content for you. Check back soon!" ?>
                        </p>
                        
                        <?php if (!empty($search)): ?>
                            <a href="<?= getBasePath() ?>/blog" 
                               class="inline-flex items-center bg-salon-gold/90 hover:bg-salon-gold text-black px-6 py-3 rounded-full font-semibold transition-all duration-300 shadow-lg hover:shadow-salon-gold/30 group">
                                <svg class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                </svg>
                                View All Posts
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Enhanced Pagination with Improved Visual Design -->
            <?php if ($totalPages > 1): ?>
            <div class="mt-20 flex flex-col items-center space-y-8 relative">
                <!-- Decorative elements -->
                <div class="absolute -top-10 left-1/2 -translate-x-1/2 w-64 h-64 bg-salon-gold/5 rounded-full blur-3xl opacity-50 pointer-events-none"></div>
                
                <!-- Enhanced Pagination Info -->
                <div class="text-center relative z-10 bg-secondary-800/30 backdrop-blur-sm px-6 py-2 rounded-full border border-secondary-700/50">
                    <p class="text-gray-300">
                        Showing page <span class="text-salon-gold font-semibold"><?= $page ?></span>
                        of <span class="text-salon-gold font-semibold"><?= $totalPages ?></span>
                        <span class="mx-1 text-salon-gold/50">•</span>
                        <span class="text-gray-300"><?= $totalPosts ?> total <?= $totalPosts === 1 ? 'post' : 'posts' ?></span>
                    </p>
                </div>

                <!-- Enhanced Pagination Controls -->
                <nav class="flex flex-wrap items-center justify-center gap-2 relative z-10" aria-label="Pagination">
                    <?php
                    // Build pagination URL with search parameter
                    $baseUrl = getBasePath() . '/blog';
                    $searchParam = !empty($search) ? '&search=' . urlencode($search) : '';

                    // Calculate pagination range
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);

                    // Adjust range if we're near the beginning or end
                    if ($endPage - $startPage < 4) {
                        if ($startPage === 1) {
                            $endPage = min($totalPages, $startPage + 4);
                        } else {
                            $startPage = max(1, $endPage - 4);
                        }
                    }
                    ?>

                    <!-- Previous Button with Enhanced Styling -->
                    <?php if ($page > 1): ?>
                        <a href="<?= $baseUrl ?>?page=<?= $page - 1 ?><?= $searchParam ?>"
                           class="flex items-center px-5 py-2.5 text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-full hover:bg-secondary-600 hover:border-salon-gold/50 hover:text-salon-gold transition-all duration-300 group shadow-md hover:shadow-salon-gold/20">
                            <svg class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Previous
                        </a>
                    <?php else: ?>
                        <span class="flex items-center px-5 py-2.5 text-sm font-medium text-gray-500 bg-secondary-800/30 border border-secondary-700/50 rounded-full cursor-not-allowed opacity-70">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Previous
                        </span>
                    <?php endif; ?>

                    <!-- First Page with Enhanced Styling -->
                    <?php if ($startPage > 1): ?>
                        <a href="<?= $baseUrl ?>?page=1<?= $searchParam ?>"
                           class="w-10 h-10 flex items-center justify-center text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-full hover:bg-secondary-600 hover:border-salon-gold/50 hover:text-salon-gold transition-all duration-300 shadow-md hover:shadow-salon-gold/20">
                            1
                        </a>
                        <?php if ($startPage > 2): ?>
                            <span class="flex items-center justify-center text-salon-gold/70">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Page Numbers with Enhanced Styling -->
                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                        <?php if ($i === $page): ?>
                            <span class="w-10 h-10 flex items-center justify-center text-sm font-bold text-black bg-salon-gold rounded-full shadow-lg transform scale-110 transition-transform duration-300">
                                <?= $i ?>
                            </span>
                        <?php else: ?>
                            <a href="<?= $baseUrl ?>?page=<?= $i ?><?= $searchParam ?>"
                               class="w-10 h-10 flex items-center justify-center text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-full hover:bg-secondary-600 hover:border-salon-gold/50 hover:text-salon-gold transition-all duration-300 shadow-md hover:shadow-salon-gold/20">
                                <?= $i ?>
                            </a>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <!-- Last Page with Enhanced Styling -->
                    <?php if ($endPage < $totalPages): ?>
                        <?php if ($endPage < $totalPages - 1): ?>
                            <span class="flex items-center justify-center text-salon-gold/70">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </span>
                        <?php endif; ?>
                        <a href="<?= $baseUrl ?>?page=<?= $totalPages ?><?= $searchParam ?>"
                           class="w-10 h-10 flex items-center justify-center text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-full hover:bg-secondary-600 hover:border-salon-gold/50 hover:text-salon-gold transition-all duration-300 shadow-md hover:shadow-salon-gold/20">
                            <?= $totalPages ?>
                        </a>
                    <?php endif; ?>

                    <!-- Next Button with Enhanced Styling -->
                    <?php if ($page < $totalPages): ?>
                        <a href="<?= $baseUrl ?>?page=<?= $page + 1 ?><?= $searchParam ?>"
                           class="flex items-center px-5 py-2.5 text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-full hover:bg-secondary-600 hover:border-salon-gold/50 hover:text-salon-gold transition-all duration-300 group shadow-md hover:shadow-salon-gold/20">
                            Next
                            <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    <?php else: ?>
                        <span class="flex items-center px-5 py-2.5 text-sm font-medium text-gray-500 bg-secondary-800/30 border border-secondary-700/50 rounded-full cursor-not-allowed opacity-70">
                            Next
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </span>
                    <?php endif; ?>
                </nav>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Newsletter Subscription Section -->
<section class="py-20 bg-gradient-to-r from-salon-gold/10 via-salon-gold/5 to-transparent">
    <div class="max-w-4xl mx-auto px-6 text-center">
        <div class="bg-secondary-800/50 backdrop-blur-sm rounded-2xl border border-secondary-700/50 p-12">
            <div class="mb-8">
                <svg class="w-16 h-16 text-salon-gold mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-4">
                    Stay <span class="text-salon-gold">Beautiful</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Subscribe to our newsletter and never miss the latest beauty tips, trends, and exclusive offers.
                </p>
            </div>

            <form class="max-w-md mx-auto" action="<?= getBasePath() ?>/api/newsletter.php" method="POST">
                <div class="flex flex-col sm:flex-row gap-4">
                    <input
                        type="email"
                        name="email"
                        placeholder="Enter your email address"
                        class="flex-1 px-6 py-4 bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-salon-gold focus:ring-2 focus:ring-salon-gold/20 transition-all duration-300"
                        required
                    >
                    <button
                        type="submit"
                        class="px-8 py-4 bg-salon-gold hover:bg-gold-light text-black font-semibold rounded-lg transition-all duration-300 hover:scale-105 whitespace-nowrap"
                    >
                        Subscribe
                    </button>
                </div>
                <p class="text-sm text-gray-400 mt-4">
                    We respect your privacy. Unsubscribe at any time.
                </p>
            </form>
        </div>
    </div>
</section>

<!-- Revolutionary JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Advanced Scroll Reveal System
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
                
                // Add staggered animation for grid items
                if (entry.target.classList.contains('medical-grid')) {
                    const children = entry.target.children;
                    Array.from(children).forEach((child, index) => {
                        setTimeout(() => {
                            child.classList.add('revealed');
                        }, index * 100);
                    });
                }
            }
        });
    }, observerOptions);
    
    // Observe all scroll reveal elements
    document.querySelectorAll('.scroll-reveal').forEach(el => {
        observer.observe(el);
    });
    
    // Revolutionary Card Hover Effects
    document.querySelectorAll('.medical-blog-card, .featured-blog-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Interactive Elements
    document.querySelectorAll('.interactive-element').forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
        });
        
        element.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Revolutionary Button Effects
    document.querySelectorAll('.btn-revolutionary').forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Enhanced Search Functionality
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.parentElement.classList.add('search-glow');
        });
        
        searchInput.addEventListener('blur', function() {
            this.parentElement.parentElement.classList.remove('search-glow');
        });
    }
    
    // Smooth Scrolling for Internal Links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Page Load Animation
    setTimeout(() => {
        document.body.style.opacity = '1';
        document.body.style.transform = 'translateY(0)';
    }, 100);
    
    // Parallax Effect for Hero Section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.morphing-bg');
        
        parallaxElements.forEach(element => {
            const speed = 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });
    
    // Revolutionary Pagination Hover Effects
    document.querySelectorAll('.pagination-revolutionary').forEach(element => {
        element.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.borderColor = 'var(--primary-green)';
                this.style.background = 'var(--gradient-primary)';
                this.style.color = 'white';
            }
        });
        
        element.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.borderColor = 'rgba(73, 167, 92, 0.2)';
                this.style.background = 'rgba(255, 255, 255, 0.9)';
                this.style.color = '';
            }
        });
    });
    
    // Blog Card Interactions
    document.querySelectorAll('.blog-card, .featured-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Add subtle glow effect
            this.style.boxShadow = '0 25px 50px rgba(73, 167, 92, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
        });
    });
    
    // Newsletter Form Enhancement
    const newsletterForm = document.querySelector('form[action*="newsletter"]');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            submitBtn.textContent = 'Subscribing...';
            submitBtn.disabled = true;
            
            // Re-enable after 3 seconds (adjust based on actual form handling)
            setTimeout(() => {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });
    }
    
    // Copy to Clipboard Functionality
    window.copyToClipboard = function(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success feedback
            const notification = document.createElement('div');
            notification.textContent = 'Link copied to clipboard!';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--gradient-primary);
                color: white;
                padding: 12px 24px;
                border-radius: 50px;
                font-weight: 600;
                z-index: 10000;
                animation: slideInFromRight 0.3s ease;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideInFromRight 0.3s ease reverse';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 2000);
        });
    };
});

// Add CSS for ripple effect and animations
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    body {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.8s ease;
    }
    
    .search-glow {
        box-shadow: 0 0 30px rgba(73, 167, 92, 0.3) !important;
    }
    
    @keyframes slideInFromRight {
        0% { opacity: 0; transform: translateX(100px); }
        100% { opacity: 1; transform: translateX(0); }
    }
`;
document.head.appendChild(style);
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>