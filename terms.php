<?php
/**
 * Terms of Service Page
 * Redolence Medi Aesthetics - Professional Medical Aesthetics
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Terms of Service - Redolence Medi Aesthetics";
$pageDescription = "Read our comprehensive terms of service for medical aesthetic treatments and services at Redolence Medi Aesthetics.";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Modern Terms of Service Styles - Redolence Medical Aesthetics */
:root {
    --redolence-green: #49a75c;
    --redolence-blue: #5894d2;
    --redolence-navy: #1a2332;
}

.terms-page {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    min-height: 100vh;
}

.terms-hero {
    background: linear-gradient(135deg, var(--redolence-green) 0%, var(--redolence-blue) 100%);
    position: relative;
    overflow: hidden;
}

.terms-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.05) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.content-section {
    background: white;
    border-radius: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(73, 167, 92, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    overflow: hidden;
    position: relative;
}

.content-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--redolence-green), var(--redolence-blue));
}

.content-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.section-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--redolence-green), var(--redolence-blue));
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

.terms-content h3 {
    color: var(--redolence-navy);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--redolence-green);
}

.terms-content h4 {
    color: var(--redolence-green);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem 0;
}

.terms-content ul {
    list-style: none;
    padding-left: 0;
    margin: 1rem 0;
}

.terms-content li {
    padding: 0.5rem 0;
    padding-left: 2rem;
    position: relative;
}

.terms-content li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--redolence-green);
    font-weight: bold;
    font-size: 1.2rem;
}

.terms-content p {
    margin: 1rem 0;
    line-height: 1.8;
    color: #374151;
}

.terms-content strong {
    color: var(--redolence-navy);
    font-weight: 600;
}

.contact-card {
    background: linear-gradient(135deg, var(--redolence-green)/5 0%, var(--redolence-blue)/5 100%);
    border: 2px solid var(--redolence-green)/20;
    border-radius: 1.5rem;
    padding: 2rem;
    text-align: center;
}

.contact-button {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, var(--redolence-green), var(--redolence-blue));
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.contact-button:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
    text-decoration: none;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-section {
        margin-bottom: 1.5rem;
    }

    .terms-hero {
        padding: 3rem 0;
    }
}
</style>

</style>

<!-- Modern Terms of Service Page -->
<div class="terms-page">

<!-- Hero Section -->
<section class="terms-hero py-20">
    <div class="max-w-7xl mx-auto px-6 text-center relative z-10">
        <!-- Legal Badge -->
        <div class="inline-flex items-center bg-white/20 text-white px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-white/30 backdrop-blur-sm animate-on-scroll">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Legal Agreement • Medical Services
        </div>

        <h1 class="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight animate-on-scroll">
            Terms of Service
        </h1>

        <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed animate-on-scroll">
            Please read these terms carefully before using our medical aesthetic services at Redolence Medi Aesthetics
        </p>

        <div class="flex items-center justify-center space-x-8 text-white/80 animate-on-scroll">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Legally Binding</span>
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                <span>Medical Standards</span>
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Clear Guidelines</span>
            </div>
        </div>
    </div>
</section>

<!-- Content Section -->
<section class="py-16">
    <div class="max-w-6xl mx-auto px-6">

        <!-- Last Updated -->
        <div class="text-center mb-12">
            <p class="text-gray-600">Last updated: <span class="font-semibold text-redolence-green"><?= date('F d, Y') ?></span></p>
        </div>

        <!-- Agreement Overview -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Agreement Overview</h2>

                <div class="terms-content">
                    <p>
                        Welcome to <strong>Redolence Medi Aesthetics</strong>. These Terms of Service ("Terms") govern your use of our website, services, and medical aesthetic treatments. By accessing our website or receiving our services, you agree to be bound by these Terms.
                    </p>
                    <p>
                        These Terms constitute a legally binding agreement between you and Redolence Medi Aesthetics. Please read them carefully before using our services or website.
                    </p>
                    <p>
                        If you do not agree with any part of these Terms, you must not use our website or services.
                    </p>
                </div>
            </div>
        </div>

        <!-- Medical Services -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Medical Aesthetic Services</h2>

                <div class="terms-content">
                    <h3>Service Description</h3>
                    <p>
                        Redolence Medi Aesthetics provides professional medical aesthetic treatments including but not limited to:
                    </p>
                    <ul>
                        <li>Non-surgical facial rejuvenation treatments</li>
                        <li>Injectable treatments (Botox, dermal fillers)</li>
                        <li>Laser and light-based therapies</li>
                        <li>Chemical peels and skin resurfacing</li>
                        <li>Body contouring and skin tightening</li>
                        <li>Medical-grade skincare consultations</li>
                    </ul>

                    <h3>Medical Consultation Required</h3>
                    <p>
                        All treatments require a comprehensive medical consultation to assess your suitability for the procedure. Our qualified medical professionals will:
                    </p>
                    <ul>
                        <li>Review your medical history and current medications</li>
                        <li>Assess your skin condition and treatment goals</li>
                        <li>Explain the procedure, benefits, and potential risks</li>
                        <li>Provide detailed aftercare instructions</li>
                        <li>Obtain informed consent before any treatment</li>
                    </ul>

                    <h3>Treatment Limitations</h3>
                    <p>
                        Certain medical conditions, medications, or circumstances may prevent you from receiving specific treatments. We reserve the right to refuse treatment if it is not in your best medical interest.
                    </p>
                </div>
            </div>
        </div>

        <!-- Booking and Appointments -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Booking & Appointments</h2>

                <div class="terms-content">
                    <h3>Appointment Booking</h3>
                    <ul>
                        <li>Appointments can be booked online, by phone, or in person</li>
                        <li>A valid form of identification is required for all appointments</li>
                        <li>Consultation fees may apply and will be disclosed at booking</li>
                        <li>Treatment prices are subject to change without notice</li>
                    </ul>

                    <h3>Cancellation Policy</h3>
                    <ul>
                        <li>Appointments must be cancelled at least 24 hours in advance</li>
                        <li>Late cancellations may incur a cancellation fee</li>
                        <li>No-shows will be charged the full consultation fee</li>
                        <li>Emergency cancellations will be considered on a case-by-case basis</li>
                    </ul>

                    <h3>Rescheduling</h3>
                    <ul>
                        <li>Appointments can be rescheduled subject to availability</li>
                        <li>Multiple rescheduling may result in appointment forfeiture</li>
                        <li>Peak time appointments may have limited rescheduling options</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Payment Terms -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Payment Terms</h2>

                <div class="terms-content">
                    <h3>Payment Methods</h3>
                    <ul>
                        <li>We accept cash, credit cards, and bank transfers</li>
                        <li>Payment is required at the time of service unless otherwise arranged</li>
                        <li>Payment plans may be available for certain treatments</li>
                        <li>All prices are in Tanzanian Shillings (TSH)</li>
                    </ul>

                    <h3>Pricing</h3>
                    <ul>
                        <li>Treatment prices are provided during consultation</li>
                        <li>Prices may vary based on individual treatment requirements</li>
                        <li>Package deals and promotions may be available</li>
                        <li>All prices include applicable taxes</li>
                    </ul>

                    <h3>Refund Policy</h3>
                    <ul>
                        <li>Consultation fees are generally non-refundable</li>
                        <li>Treatment refunds are considered on a case-by-case basis</li>
                        <li>Medical contraindications may qualify for refund consideration</li>
                        <li>Refund requests must be made within 7 days of treatment</li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- Liability and Disclaimers -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Liability & Disclaimers</h2>

                <div class="terms-content">
                    <h3>Treatment Risks</h3>
                    <p>
                        All medical aesthetic treatments carry inherent risks. While we take every precaution to ensure your safety, you acknowledge that:
                    </p>
                    <ul>
                        <li>Results may vary between individuals</li>
                        <li>Some treatments may require multiple sessions</li>
                        <li>Temporary side effects may occur (swelling, bruising, redness)</li>
                        <li>Rare complications, though uncommon, are possible</li>
                        <li>Follow-up treatments may be necessary</li>
                    </ul>

                    <h3>Limitation of Liability</h3>
                    <p>
                        To the fullest extent permitted by law, Redolence Medi Aesthetics shall not be liable for any indirect, incidental, special, or consequential damages arising from your use of our services.
                    </p>

                    <h3>Medical Disclaimer</h3>
                    <p>
                        Our treatments are for aesthetic purposes and are not intended to diagnose, treat, cure, or prevent any disease. Individual results may vary, and we cannot guarantee specific outcomes.
                    </p>
                </div>
            </div>
        </div>

        <!-- Privacy and Confidentiality -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Privacy & Confidentiality</h2>

                <div class="terms-content">
                    <h3>Medical Privacy</h3>
                    <p>
                        We are committed to protecting your medical information in accordance with HIPAA guidelines and applicable privacy laws. Your medical records and treatment information will be kept strictly confidential.
                    </p>

                    <h3>Photography and Documentation</h3>
                    <ul>
                        <li>Before and after photos may be taken for medical records</li>
                        <li>Separate consent is required for marketing use of images</li>
                        <li>You may request that no photos be taken</li>
                        <li>All images are stored securely and confidentially</li>
                    </ul>

                    <h3>Information Sharing</h3>
                    <p>
                        We will only share your information with your explicit consent or as required by law. This may include sharing with other healthcare providers involved in your care.
                    </p>
                </div>
            </div>
        </div>

        <!-- Website Terms -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Website Usage</h2>

                <div class="terms-content">
                    <h3>Acceptable Use</h3>
                    <ul>
                        <li>Use the website only for lawful purposes</li>
                        <li>Do not attempt to gain unauthorized access to our systems</li>
                        <li>Do not use the website to transmit harmful content</li>
                        <li>Respect intellectual property rights</li>
                    </ul>

                    <h3>Account Security</h3>
                    <ul>
                        <li>Keep your login credentials secure and confidential</li>
                        <li>Notify us immediately of any unauthorized access</li>
                        <li>You are responsible for all activities under your account</li>
                        <li>We may suspend accounts for security reasons</li>
                    </ul>

                    <h3>Content and Intellectual Property</h3>
                    <p>
                        All content on our website, including text, images, logos, and designs, is protected by copyright and other intellectual property laws. You may not reproduce, distribute, or modify our content without permission.
                    </p>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="contact-card animate-on-scroll">
            <div class="section-icon mx-auto">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
            </div>

            <h2 class="text-3xl font-bold text-redolence-navy mb-4">Questions About These Terms?</h2>
            <p class="text-gray-600 mb-6 text-lg">
                If you have any questions about these terms of service or need clarification on any aspect of our policies, please contact us.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="text-center">
                    <h4 class="font-semibold text-redolence-navy mb-2">Legal Department</h4>
                    <p class="text-gray-600"><EMAIL></p>
                </div>
                <div class="text-center">
                    <h4 class="font-semibold text-redolence-navy mb-2">Phone</h4>
                    <p class="text-gray-600">+255 123 456 789</p>
                </div>
            </div>

            <a href="contact.php" class="contact-button">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Contact Us
            </a>
        </div>
    </div>
</section>

</div>
<!-- End Terms Page -->

<script>
// Handle scroll animations
function handleScrollAnimations() {
    const elements = document.querySelectorAll('.animate-on-scroll:not(.visible)');

    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;

        if (elementTop < window.innerHeight - elementVisible) {
            element.classList.add('visible');
        }
    });
}

// Listen for scroll events
window.addEventListener('scroll', handleScrollAnimations);

// Check for elements in view on page load
document.addEventListener('DOMContentLoaded', () => {
    handleScrollAnimations();
});
</script>
<script>
// Handle scroll animations
function handleScrollAnimations() {
    const elements = document.querySelectorAll('.animate-on-scroll:not(.animate)');
    
    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementBottom = element.getBoundingClientRect().bottom;
        const windowHeight = window.innerHeight;
        
        if (elementTop < windowHeight * 0.9 && elementBottom > 0) {
            const delay = element.style.getPropertyValue('--delay') || '0s';
            setTimeout(() => {
                element.classList.add('animate');
            }, parseFloat(delay) * 1000);
        }
    });
}

// Set up Intersection Observer for scroll animations
if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const delay = entry.target.style.getPropertyValue('--delay') || '0s';
                setTimeout(() => {
                    entry.target.classList.add('animate');
                }, parseFloat(delay) * 1000);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    document.querySelectorAll('.animate-on-scroll').forEach(element => {
        observer.observe(element);
    });
} else {
    // Fallback for browsers that don't support Intersection Observer
    window.addEventListener('scroll', handleScrollAnimations);
    handleScrollAnimations();
}

// Check for elements in view on page load
document.addEventListener('DOMContentLoaded', () => {
    handleScrollAnimations();
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>