<?php
$currentPath = $_SERVER['REQUEST_URI'];
$basePath = getBasePath();

// Ensure contact functions available for unread counter
require_once __DIR__ . '/contact_functions.php';
$contactStats = function_exists('getContactMessageStats') ? getContactMessageStats() : ['new' => 0];
$unreadContacts = (int)($contactStats['new'] ?? 0);
$contactMessagesHref = $basePath . '/admin/contact-messages';

// Normalize current path for comparison
$normalizedCurrentPath = rtrim(parse_url($currentPath, PHP_URL_PATH), '/');
$adminBasePath = $basePath . '/admin';
$navigation = [
    [
        'name' => 'Medical Dashboard',
        'href' => $basePath . '/admin',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h4" />',
        'description' => 'Overview & Analytics'
    ],
    [
        'name' => 'Patient Appointments',
        'href' => $basePath . '/admin/bookings',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />',
        'description' => 'Scheduling & Calendar'
    ],
    [
        'name' => 'Patient Management',
        'href' => $basePath . '/admin/customers',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />',
        'description' => 'Patient Records'
    ],
    [
        'name' => 'Progressive Reports',
        'href' => $basePath . '/admin/progressive-reports',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />',
        'description' => 'Treatment Progress Tracking'
    ],
    [
        'name' => 'Medical Treatments',
        'href' => $basePath . '/admin/services',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />',
        'description' => 'Aesthetic Procedures'
    ],
    [
        'name' => 'Promotional Offers',
        'href' => $basePath . '/admin/offers',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />',
        'description' => 'Special Discounts & Campaigns'
    ],
    [
        'name' => 'Medical Staff',
        'href' => $basePath . '/admin/staff',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />',
        'description' => 'Medical Professionals'
    ],
    [
        'name' => 'Rooms Management',
        'href' => $basePath . '/admin/rooms',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />',
        'description' => 'Treatment Rooms & Facilities'
    ],
    [
        'name' => 'Patient Inquiries',
        'href' => $contactMessagesHref,
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />',
        'description' => 'Contact Messages',
        'badge' => $unreadContacts
    ],
    [
        'name' => 'Medical FAQ',
        'href' => $basePath . '/admin/faq',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />',
        'description' => 'Patient Questions'
    ],
    [
        'name' => 'Medical Gallery',
        'href' => $basePath . '/admin/gallery',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />',
        'description' => 'Before/After & Clinic Photos'
    ],
    [
        'name' => 'Medical Blog',
        'href' => $basePath . '/admin/blog',
        'icon' => '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />',
        'description' => 'Educational Content'
    ]
];
?>

<!-- Clean Professional Sidebar -->
<div class="clean-sidebar h-full flex flex-col bg-gradient-to-b from-white to-gray-50 border-r border-gray-200/50 shadow-lg">
    <!-- Clean Logo Section -->
    <div class="p-5 border-b border-gray-200/50">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-redolence-blue rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div>
                <h2 class="text-xl font-semibold text-gray-800">Redolence</h2>
                <p class="text-sm text-gray-500">Medical Admin</p>
            </div>
        </div>
    </div>

    <!-- Clean Navigation -->
    <nav class="flex-1 p-3 space-y-1 overflow-y-auto">
    <?php foreach ($navigation as $item):
        // Normalize item href for comparison
        $normalizedItemHref = rtrim($item['href'], '/');

        // Check if this menu item is active
        $isActive = false;

        // Exact match for all pages
        if ($normalizedCurrentPath === $normalizedItemHref) {
            $isActive = true;
        }
        // For non-dashboard items, check sub-pages
        elseif ($normalizedItemHref !== $adminBasePath && strpos($normalizedCurrentPath . '/', $normalizedItemHref . '/') === 0) {
            $isActive = true;
        }
    ?>
        <a href="<?= $item['href'] ?>" class="clean-nav-item flex items-center px-3 py-3 rounded-lg <?= $isActive ? 'bg-redolence-blue text-white' : 'text-gray-700 hover:bg-gray-100' ?>">
            <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <?php
                // Replace stroke with fill in icon if active
                if ($isActive) {
                    $icon = str_replace('fill="none"', 'fill="currentColor"', $item['icon']);
                    $icon = str_replace('stroke="currentColor"', '', $icon);
                } else {
                    $icon = $item['icon'];
                }
                echo $icon;
                ?>
            </svg>
            <div class="flex-1">
                <div class="font-medium text-sm flex items-center gap-2">
                    <span><?= htmlspecialchars($item['name']) ?></span>
                    <?php if (!empty($item['badge'])): ?>
                        <span class="inline-flex items-center justify-center px-2 h-5 rounded-full text-xs font-semibold <?= $isActive ? 'bg-white text-redolence-blue' : 'bg-red-600 text-white' ?>" aria-label="Unread messages">
                            <?= (int)$item['badge'] ?>
                        </span>
                    <?php endif; ?>
                </div>
                <div class="text-xs <?= $isActive ? 'text-white/80' : 'text-gray-500' ?>"><?= htmlspecialchars($item['description']) ?></div>
            </div>
        </a>
    <?php endforeach; ?>
    </nav>

    <!-- Quick Actions -->
    <div class="mt-6 px-3 pb-4">
        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 pl-2">Quick Actions</h3>
        <div class="space-y-2">
            <a href="<?= $basePath ?>/admin/bookings/create" class="clean-btn w-full bg-redolence-blue text-white hover:bg-redolence-blue/90 px-3 py-2.5 rounded-lg flex items-center justify-center text-sm font-medium transition-colors">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"/>
                </svg>
                New Appointment
            </a>
            <a href="<?= $basePath ?>/admin/customers/create" class="clean-btn w-full bg-white border border-redolence-blue text-redolence-blue hover:bg-gray-50 px-3 py-2.5 rounded-lg flex items-center justify-center text-sm font-medium transition-colors">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9c0-1.1.9-2 2-2h10a2 2 0 012 2v1a1 1 0 01-2 0v-1H5v1a1 1 0 11-2 0v-1z" clip-rule="evenodd"/>
                </svg>
                Add Patient
            </a>
        </div>
    </div>
</div> <!-- End of Clean Professional Sidebar -->