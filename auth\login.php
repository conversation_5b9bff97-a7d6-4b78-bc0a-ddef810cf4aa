<?php
/**
 * Login Page - Modern UI/UX (aligned with Register page)
 * Redolence Medi Aesthetics
 */

require_once __DIR__ . '/../config/app.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'ADMIN') {
        redirect('/admin');
    } elseif ($user['role'] === 'STAFF') {
        redirect('/staff');
    } else {
        redirect('/customer');
    }
}

$error = '';
$success = '';

// Check for password reset success message
if (isset($_SESSION['password_reset_success'])) {
    $success = $_SESSION['password_reset_success'];
    unset($_SESSION['password_reset_success']);
}

// Get redirect parameter
$redirectUrl = $_GET['redirect'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $redirectUrl = sanitize($_POST['redirect'] ?? '');

    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields.';
    } else {
        $result = $auth->login($email, $password);

        if ($result['success']) {
            $user = $result['user'];

            if (isset($result['requires_2fa']) && $result['requires_2fa']) {
                redirect('/admin/auth/verify-2fa.php');
            }

            if (!empty($redirectUrl) && $user['role'] === 'CUSTOMER') {
                $allowedPaths = ['/customer/', '/services.php', '/booking-policy.php'];
                $isValidRedirect = false;
                $decodedRedirectUrl = urldecode($redirectUrl);

                foreach ($allowedPaths as $allowedPath) {
                    if (strpos($decodedRedirectUrl, $allowedPath) !== false) {
                        $isValidRedirect = true;
                        break;
                    }
                }
                redirect($isValidRedirect ? $decodedRedirectUrl : '/customer');
            } else {
                if ($user['role'] === 'ADMIN') {
                    redirect('/admin');
                } elseif ($user['role'] === 'STAFF') {
                    redirect('/staff');
                } else {
                    redirect('/customer');
                }
            }
        } else {
            $error = $result['error'];
        }
    }
}

$pageTitle = "Sign in";
$pageDescription = "Access your Redolence account to manage appointments and view your treatment history.";

include __DIR__ . '/../includes/header.php';
?>

<style>
  /* Background and shell (matching register page) */
  .reg-bg {
    min-height: calc(100vh - 140px);
    background: radial-gradient(1000px 500px at 0% 0%, rgba(73,167,92,0.12), transparent 60%),
                radial-gradient(800px 400px at 100% 100%, rgba(88,148,210,0.12), transparent 60%),
                linear-gradient(180deg, #0b1220 0%, #0f172a 100%);
  }
  .glass {
    background: rgba(255,255,255,0.06);
    border: 1px solid rgba(255,255,255,0.08);
    box-shadow: 0 20px 60px rgba(0,0,0,0.35), inset 0 1px 0 rgba(255,255,255,0.05);
    backdrop-filter: blur(16px);
  }
  .accent-ring { box-shadow: 0 0 0 1px rgba(73,167,92,0.35), 0 10px 30px rgba(73,167,92,0.25); }
  .orb { filter: blur(30px); opacity: .6; }

  /* Inputs and buttons */
  .field { transition: all .2s ease; }
  .field:focus { outline: none; box-shadow: 0 0 0 3px rgba(73,167,92,0.25); border-color: rgba(73,167,92,0.65) !important; }
  .btn-primary { background: linear-gradient(135deg, #49a75c 0%, #5894d2 100%); color: #0b1220; transition: transform .15s ease, box-shadow .2s ease; }
  .btn-primary:hover { transform: translateY(-1px); box-shadow: 0 12px 28px rgba(73,167,92,0.35); }
  .btn-muted { background: rgba(255,255,255,0.06); border: 1px solid rgba(255,255,255,0.12); }

  /* Alerts */
  .alert { animation: slideIn .35s ease; }
  @keyframes slideIn { from { opacity: 0; transform: translateY(-6px);} to { opacity: 1; transform: translateY(0);} }
</style>

<div class="reg-bg relative">
  <!-- Decorative orbs -->
  <div class="pointer-events-none absolute -top-10 -left-10 w-64 h-64 rounded-full bg-emerald-500/20 orb"></div>
  <div class="pointer-events-none absolute -bottom-10 -right-10 w-72 h-72 rounded-full bg-sky-500/20 orb"></div>

  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Brand/Marketing Panel -->
      <section class="glass rounded-2xl p-8 lg:p-10 relative overflow-hidden">
        <div class="absolute -right-10 -top-10 w-48 h-48 rounded-full bg-emerald-400/10"></div>
        <div class="relative">
          <div class="inline-flex items-center space-x-2 px-3 py-1 rounded-full bg-emerald-500/15 border border-emerald-500/30 text-emerald-300 text-xs font-semibold mb-6">
            <span class="w-2 h-2 rounded-full bg-emerald-400"></span>
            <span>Welcome back to Redolence</span>
          </div>
          <h1 class="text-3xl sm:text-4xl font-extrabold tracking-tight text-white">Fast, secure sign in</h1>
          <p class="mt-3 text-slate-300/90 leading-relaxed">Manage appointments, view treatment history, and access personalized recommendations.</p>

          <ul class="mt-8 space-y-4">
            <li class="flex items-start">
              <div class="mt-1 mr-3 w-6 h-6 rounded-lg grid place-content-center bg-emerald-500/20 text-emerald-300 accent-ring">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-7.25 7.25a1 1 0 01-1.414 0l-3-3a1 1 0 111.414-1.414L8.5 11.086l6.543-6.543a1 1 0 011.414 0z" clip-rule="evenodd"/></svg>
              </div>
              <div>
                <p class="text-slate-200 font-semibold">One-tap booking</p>
                <p class="text-slate-300/80 text-sm">Securely reserve your spot in seconds.</p>
              </div>
            </li>
            <li class="flex items-start">
              <div class="mt-1 mr-3 w-6 h-6 rounded-lg grid place-content-center bg-emerald-500/20 text-emerald-300 accent-ring">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor"><path d="M10 2a1 1 0 011 1v5h5a1 1 0 010 2h-5v5a1 1 0 01-2 0v-5H4a1 1 0 110-2h5V3a1 1 0 011-1z"/></svg>
              </div>
              <div>
                <p class="text-slate-200 font-semibold">Rewards</p>
                <p class="text-slate-300/80 text-sm">Track and redeem your loyalty points.</p>
              </div>
            </li>
            <li class="flex items-start">
              <div class="mt-1 mr-3 w-6 h-6 rounded-lg grid place-content-center bg-emerald-500/20 text-emerald-300 accent-ring">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M2.003 5.884l8-3.2a1 1 0 01.747 0l8 3.2a1 1 0 01.25 1.686l-8 7a1 1 0 01-1.29 0l-8-7a1 1 0 01.293-1.686z" clip-rule="evenodd"/></svg>
              </div>
              <div>
                <p class="text-slate-200 font-semibold">Personalized care</p>
                <p class="text-slate-300/80 text-sm">Recommendations tailored to your goals.</p>
              </div>
            </li>
          </ul>

          <div class="mt-10 p-4 rounded-xl bg-white/5 border border-white/10">
            <div class="flex items-center gap-3">
              <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=256&auto=format&fit=crop" alt="avatar" class="w-10 h-10 rounded-full object-cover" />
              <div>
                <p class="text-slate-200 text-sm">“Love how quick it is to sign in and book.”</p>
                <p class="text-slate-400 text-xs mt-1">— Returning client</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Form Panel -->
      <section class="glass rounded-2xl p-6 sm:p-8">
        <div class="text-center mb-6">
          <a href="<?= getBasePath() ?>/" class="inline-block mb-4">
            <img class="mx-auto h-10 w-auto" src="<?= getBasePath() ?>/assets/images/LOGO2.png" alt="Redolence Logo">
          </a>
          <h2 class="text-2xl font-extrabold text-white">Welcome back</h2>
          <p class="mt-1 text-sm text-slate-300/90">Sign in to continue your journey with us.</p>
        </div>

        <?php if ($error): ?>
          <div class="alert mb-4 p-3 rounded-md border border-red-500/30 bg-red-500/10 text-red-300 text-sm" role="alert">
            <p><?= htmlspecialchars($error) ?></p>
          </div>
        <?php endif; ?>

        <?php if ($success): ?>
          <div class="alert mb-4 p-3 rounded-md border border-emerald-500/30 bg-emerald-500/10 text-emerald-300 text-sm" role="alert">
            <p><?= htmlspecialchars($success) ?></p>
          </div>
        <?php endif; ?>

        <form class="space-y-6" method="POST" autocomplete="off" novalidate>
          <input type="hidden" name="redirect" value="<?= htmlspecialchars($redirectUrl) ?>">

          <div>
            <label for="email" class="block text-sm font-medium text-slate-200 mb-2">Email address</label>
            <input id="email" name="email" type="email" autocomplete="email" required
                   class="field w-full px-4 py-3 rounded-xl bg-white/5 border border-white/10 text-slate-100 placeholder-slate-400" placeholder="<EMAIL>"
                   value="<?= isset($email) ? htmlspecialchars($email) : '' ?>">
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-slate-200 mb-2">Password</label>
            <div class="relative">
              <input id="password" name="password" type="password" autocomplete="current-password" required
                     class="field w-full pr-12 px-4 py-3 rounded-xl bg-white/5 border border-white/10 text-slate-100 placeholder-slate-400" placeholder="Your password">
              <button type="button" class="absolute inset-y-0 right-0 px-3 text-slate-400 hover:text-slate-200" onclick="togglePassword('password','eye-password')">
                <svg id="eye-password" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/></svg>
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="text-sm">
              <a href="<?= getBasePath() ?>/auth/forgot-password.php" class="font-medium text-emerald-300 hover:text-emerald-200">Forgot your password?</a>
            </div>
          </div>

          <div class="pt-2">
            <button id="submitBtn" type="submit" class="btn-primary w-full px-5 py-3 rounded-xl font-semibold inline-flex items-center justify-center">
              <span id="submitText">Sign in</span>
              <svg id="loadingSpinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </button>
          </div>
        </form>

        <div class="mt-6 text-center">
          <p class="text-sm text-slate-300">
            Don't have an account?
            <a href="<?= getBasePath() ?>/auth/register.php<?= !empty($redirectUrl) ? '?redirect=' . urlencode($redirectUrl) : '' ?>" class="font-semibold text-emerald-300 hover:text-emerald-200">Sign up here</a>
          </p>
        </div>
      </section>
    </div>
  </div>
</div>

<script>
  function togglePassword(fieldId, iconId) {
    const input = document.getElementById(fieldId);
    const icon = document.getElementById(iconId);
    if (input.type === 'password') {
      input.type = 'text';
      icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />';
    } else {
      input.type = 'password';
      icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />';
    }
  }

  // Submit UX: show loading spinner
  document.querySelector('form').addEventListener('submit', function() {
    const btn = document.getElementById('submitBtn');
    const text = document.getElementById('submitText');
    const spinner = document.getElementById('loadingSpinner');
    btn.disabled = true;
    text.textContent = 'Signing in…';
    spinner.classList.remove('hidden');
  });
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>
