<?php
/**
 * Basic FAQ Services Migration
 * Redolence Medi Aesthetics - Ultra-simple migration with step-by-step execution
 */

require_once __DIR__ . '/config/app.php';

// Check if user is admin
if (!$auth->isAuthenticated() || !$auth->hasRole('ADMIN')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic FAQ Migration - Redolence Medi Aesthetics</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #10b981; background: #ecfdf5; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #ef4444; background: #fef2f2; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #f59e0b; background: #fffbeb; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #3b82f6; background: #eff6ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .btn { background: #49a75c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #2d6a3e; }
        pre { background: #f3f4f6; padding: 10px; border-radius: 5px; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Basic FAQ Services Migration</h1>
    <p>This ultra-simple migration executes each SQL statement individually with detailed feedback.</p>

    <?php
    if (isset($_POST['apply_migration'])) {
        echo "<h2>Applying Basic Migration...</h2>";
        
        $steps = [
            [
                'name' => 'Create faq_services table',
                'sql' => "CREATE TABLE faq_services (
                    id VARCHAR(36) PRIMARY KEY,
                    faq_id INT NOT NULL,
                    service_id VARCHAR(36) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )",
                'check' => "SHOW TABLES LIKE 'faq_services'"
            ],
            [
                'name' => 'Add unique constraint to faq_services',
                'sql' => "ALTER TABLE faq_services ADD UNIQUE KEY unique_faq_service (faq_id, service_id)",
                'check' => "SHOW INDEX FROM faq_services WHERE Key_name = 'unique_faq_service'"
            ],
            [
                'name' => 'Add faq_id index to faq_services',
                'sql' => "ALTER TABLE faq_services ADD KEY idx_faq_services_faq_id (faq_id)",
                'check' => "SHOW INDEX FROM faq_services WHERE Key_name = 'idx_faq_services_faq_id'"
            ],
            [
                'name' => 'Add service_id index to faq_services',
                'sql' => "ALTER TABLE faq_services ADD KEY idx_faq_services_service_id (service_id)",
                'check' => "SHOW INDEX FROM faq_services WHERE Key_name = 'idx_faq_services_service_id'"
            ],
            [
                'name' => 'Add service_link_text column to faqs',
                'sql' => "ALTER TABLE faqs ADD COLUMN service_link_text VARCHAR(255) DEFAULT NULL",
                'check' => "SHOW COLUMNS FROM faqs LIKE 'service_link_text'"
            ]
        ];
        
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($steps as $step) {
            echo "<h3>Step: {$step['name']}</h3>";
            
            try {
                // First check if the change is already applied
                $checkResult = $database->fetch($step['check']);
                
                if ($checkResult) {
                    echo "<div class='info'>✅ Already exists: {$step['name']}</div>";
                    $successCount++;
                } else {
                    // Execute the SQL
                    echo "<div class='info'>Executing SQL:</div>";
                    echo "<pre>" . htmlspecialchars($step['sql']) . "</pre>";
                    
                    $database->query($step['sql']);
                    
                    // Verify it worked
                    $verifyResult = $database->fetch($step['check']);
                    if ($verifyResult) {
                        echo "<div class='success'>✅ Successfully completed: {$step['name']}</div>";
                        $successCount++;
                    } else {
                        echo "<div class='error'>❌ Verification failed for: {$step['name']}</div>";
                        $errorCount++;
                    }
                }
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ Error in {$step['name']}: " . htmlspecialchars($e->getMessage()) . "</div>";
                $errorCount++;
                
                // For table creation errors, check if it's because table already exists
                if (strpos($e->getMessage(), 'already exists') !== false) {
                    echo "<div class='info'>ℹ️ This error might be because the item already exists, which is okay.</div>";
                }
            }
        }
        
        echo "<h3>Migration Summary</h3>";
        echo "<div class='info'>✅ Successful steps: $successCount</div>";
        echo "<div class='info'>❌ Failed steps: $errorCount</div>";
        
        // Final verification
        echo "<h3>Final Verification</h3>";
        
        try {
            // Check faq_services table
            $faqServicesExists = $database->fetch("SHOW TABLES LIKE 'faq_services'");
            if ($faqServicesExists) {
                echo "<div class='success'>✅ faq_services table exists</div>";
                
                // Check table structure
                $columns = $database->fetchAll("SHOW COLUMNS FROM faq_services");
                echo "<div class='info'>Table structure:</div>";
                foreach ($columns as $column) {
                    echo "<div class='info'>- {$column['Field']} ({$column['Type']})</div>";
                }
            } else {
                echo "<div class='error'>❌ faq_services table does not exist</div>";
            }
            
            // Check service_link_text column
            $columnExists = $database->fetch("SHOW COLUMNS FROM faqs LIKE 'service_link_text'");
            if ($columnExists) {
                echo "<div class='success'>✅ service_link_text column exists</div>";
            } else {
                echo "<div class='error'>❌ service_link_text column does not exist</div>";
            }
            
            // Show current table counts
            $faqCount = $database->fetch("SELECT COUNT(*) as count FROM faqs")['count'];
            $serviceCount = $database->fetch("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'];
            
            echo "<div class='info'>📊 Current FAQ count: $faqCount</div>";
            echo "<div class='info'>📊 Current active service count: $serviceCount</div>";
            
            if ($faqServicesExists && $columnExists) {
                echo "<div class='success'><h3>🎉 Migration completed successfully!</h3></div>";
                echo "<div class='info'>You can now use the FAQ-to-services linking features.</div>";
                
                echo "<br><a href='create_sample_faq_content.php' class='btn'>Create Sample Content</a>";
                echo " <a href='admin/faq/' class='btn'>Manage FAQs</a>";
                echo " <a href='faq.php' class='btn' style='background: #3b82f6;'>View Public FAQ Page</a>";
            } else {
                echo "<div class='warning'>⚠️ Migration partially completed. Some features may not work correctly.</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Verification error: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        
    } else {
        ?>
        <div class="info">
            <h3>ℹ️ What this migration does:</h3>
            <ul>
                <li>Creates the <code>faq_services</code> table for linking FAQs to services</li>
                <li>Adds necessary indexes for performance</li>
                <li>Adds the <code>service_link_text</code> column to the <code>faqs</code> table</li>
                <li>Executes each step individually with verification</li>
                <li>Provides detailed feedback for each operation</li>
            </ul>
        </div>
        
        <div class="warning">
            <h3>⚠️ Prerequisites:</h3>
            <ul>
                <li>Make sure you have a database backup</li>
                <li>Ensure the <code>faqs</code> table exists</li>
                <li>Ensure the <code>services</code> table exists</li>
                <li>Verify you have proper database permissions</li>
            </ul>
        </div>
        
        <form method="post">
            <button type="submit" name="apply_migration" class="btn">Apply Basic Migration</button>
        </form>
        
        <div class="info">
            <h3>🔍 Current Database Status:</h3>
            <?php
            try {
                // Show current tables
                $tables = $database->fetchAll("SHOW TABLES");
                echo "<p><strong>Tables in database:</strong> " . count($tables) . "</p>";
                
                // Check if required tables exist
                $faqsExists = $database->fetch("SHOW TABLES LIKE 'faqs'");
                $servicesExists = $database->fetch("SHOW TABLES LIKE 'services'");
                $faqServicesExists = $database->fetch("SHOW TABLES LIKE 'faq_services'");
                
                echo "<p>✅ FAQs table: " . ($faqsExists ? "EXISTS" : "MISSING") . "</p>";
                echo "<p>✅ Services table: " . ($servicesExists ? "EXISTS" : "MISSING") . "</p>";
                echo "<p>✅ FAQ-Services table: " . ($faqServicesExists ? "EXISTS" : "NOT YET CREATED") . "</p>";
                
                if ($faqsExists) {
                    $faqCount = $database->fetch("SELECT COUNT(*) as count FROM faqs")['count'];
                    echo "<p>📊 Current FAQ count: $faqCount</p>";
                }
                
                if ($servicesExists) {
                    $serviceCount = $database->fetch("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'];
                    echo "<p>📊 Current active service count: $serviceCount</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>Error checking database: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            ?>
        </div>
        <?php
    }
    ?>
</body>
</html>
