/**
 * WhatsApp Booking Function
 * Handles booking redirects to WhatsApp for service inquiries
 */

// WhatsApp booking function
function bookViaWhatsApp(serviceName) {
    // WhatsApp number without the plus sign and spaces
    const whatsappNumber = '255787574355'; // Tanzanian number format
    
    // Properly encode the service name to handle special characters like &, +, etc.
    const encodedServiceName = encodeURIComponent(serviceName);
    
    // Create the message template with proper encoding
    const message = `Hello, I'd like to book the service: ${encodedServiceName}.%0A%0APlease provide me with the available dates and details.`;
    
    // Create WhatsApp URL
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    
    // Open WhatsApp in a new tab/window
    window.open(whatsappUrl, '_blank');
}

// Alternative function name for backward compatibility
window.bookViaWhatsApp = bookViaWhatsApp;