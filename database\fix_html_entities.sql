-- Fix HTML entities in database
-- This script will decode HTML entities that were incorrectly stored in the database

USE flix_salonce2;

-- Fix services table
UPDATE services SET 
    name = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(name, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    description = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(description, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    session_frequency = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(session_frequency, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    technology_used = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(technology_used, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'")
WHERE name LIKE '%&amp;%' OR name LIKE '%&lt;%' OR name LIKE '%&gt;%' OR name LIKE '%&quot;%' OR name LIKE '%&#039;%'
   OR description LIKE '%&amp;%' OR description LIKE '%&lt;%' OR description LIKE '%&gt;%' OR description LIKE '%&quot;%' OR description LIKE '%&#039;%'
   OR session_frequency LIKE '%&amp;%' OR session_frequency LIKE '%&lt;%' OR session_frequency LIKE '%&gt;%' OR session_frequency LIKE '%&quot;%' OR session_frequency LIKE '%&#039;%'
   OR technology_used LIKE '%&amp;%' OR technology_used LIKE '%&lt;%' OR technology_used LIKE '%&gt;%' OR technology_used LIKE '%&quot;%' OR technology_used LIKE '%&#039;%';

-- Fix faqs table
UPDATE faqs SET 
    question = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(question, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    answer = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(answer, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    service_link_text = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(service_link_text, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'")
WHERE question LIKE '%&amp;%' OR question LIKE '%&lt;%' OR question LIKE '%&gt;%' OR question LIKE '%&quot;%' OR question LIKE '%&#039;%'
   OR answer LIKE '%&amp;%' OR answer LIKE '%&lt;%' OR answer LIKE '%&gt;%' OR answer LIKE '%&quot;%' OR answer LIKE '%&#039;%'
   OR service_link_text LIKE '%&amp;%' OR service_link_text LIKE '%&lt;%' OR service_link_text LIKE '%&gt;%' OR service_link_text LIKE '%&quot;%' OR service_link_text LIKE '%&#039;%';

-- Fix users table (names)
UPDATE users SET 
    name = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(name, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'")
WHERE name LIKE '%&amp;%' OR name LIKE '%&lt;%' OR name LIKE '%&gt;%' OR name LIKE '%&quot;%' OR name LIKE '%&#039;%';

-- Fix staff_schedules table (bio)
UPDATE staff_schedules SET 
    bio = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(bio, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    role = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(role, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'")
WHERE bio LIKE '%&amp;%' OR bio LIKE '%&lt;%' OR bio LIKE '%&gt;%' OR bio LIKE '%&quot;%' OR bio LIKE '%&#039;%'
   OR role LIKE '%&amp;%' OR role LIKE '%&lt;%' OR role LIKE '%&gt;%' OR role LIKE '%&quot;%' OR role LIKE '%&#039;%';

-- Fix bookings table (notes)
UPDATE bookings SET 
    notes = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(notes, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'")
WHERE notes LIKE '%&amp;%' OR notes LIKE '%&lt;%' OR notes LIKE '%&gt;%' OR notes LIKE '%&quot;%' OR notes LIKE '%&#039;%';

-- Fix progressive_report_entries table
UPDATE progressive_report_entries SET 
    treatment = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(treatment, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    description = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(description, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    notes = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(notes, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'")
WHERE treatment LIKE '%&amp;%' OR treatment LIKE '%&lt;%' OR treatment LIKE '%&gt;%' OR treatment LIKE '%&quot;%' OR treatment LIKE '%&#039;%'
   OR description LIKE '%&amp;%' OR description LIKE '%&lt;%' OR description LIKE '%&gt;%' OR description LIKE '%&quot;%' OR description LIKE '%&#039;%'
   OR notes LIKE '%&amp;%' OR notes LIKE '%&lt;%' OR notes LIKE '%&gt;%' OR notes LIKE '%&quot;%' OR notes LIKE '%&#039;%';

-- Fix notifications table
UPDATE notifications SET 
    title = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(title, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    message = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(message, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'")
WHERE title LIKE '%&amp;%' OR title LIKE '%&lt;%' OR title LIKE '%&gt;%' OR title LIKE '%&quot;%' OR title LIKE '%&#039;%'
   OR message LIKE '%&amp;%' OR message LIKE '%&lt;%' OR message LIKE '%&gt;%' OR message LIKE '%&quot;%' OR message LIKE '%&#039;%';

-- Fix blog_posts table if it exists
UPDATE blog_posts SET 
    title = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(title, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    content = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(content, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    excerpt = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(excerpt, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    meta_description = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(meta_description, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'")
WHERE title LIKE '%&amp;%' OR title LIKE '%&lt;%' OR title LIKE '%&gt;%' OR title LIKE '%&quot;%' OR title LIKE '%&#039;%'
   OR content LIKE '%&amp;%' OR content LIKE '%&lt;%' OR content LIKE '%&gt;%' OR content LIKE '%&quot;%' OR content LIKE '%&#039;%'
   OR excerpt LIKE '%&amp;%' OR excerpt LIKE '%&lt;%' OR excerpt LIKE '%&gt;%' OR excerpt LIKE '%&quot;%' OR excerpt LIKE '%&#039;%'
   OR meta_description LIKE '%&amp;%' OR meta_description LIKE '%&lt;%' OR meta_description LIKE '%&gt;%' OR meta_description LIKE '%&quot;%' OR meta_description LIKE '%&#039;%';

-- Fix service_variations table if it exists
UPDATE service_variations SET 
    name = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(name, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    description = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(description, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'")
WHERE name LIKE '%&amp;%' OR name LIKE '%&lt;%' OR name LIKE '%&gt;%' OR name LIKE '%&quot;%' OR name LIKE '%&#039;%'
   OR description LIKE '%&amp;%' OR description LIKE '%&lt;%' OR description LIKE '%&gt;%' OR description LIKE '%&quot;%' OR description LIKE '%&#039;%';

-- Fix custom_services table if it exists
UPDATE custom_services SET 
    name = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(name, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'"),
    description = REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(description, '&amp;', '&'), '&lt;', '<'), '&gt;', '>'), '&quot;', '"'), '&#039;', "'")
WHERE name LIKE '%&amp;%' OR name LIKE '%&lt;%' OR name LIKE '%&gt;%' OR name LIKE '%&quot;%' OR name LIKE '%&#039;%'
   OR description LIKE '%&amp;%' OR description LIKE '%&lt;%' OR description LIKE '%&gt;%' OR description LIKE '%&quot;%' OR description LIKE '%&#039;%';

SELECT 'HTML entities fix completed!' as status;
