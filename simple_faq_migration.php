<?php
/**
 * Simple FAQ Services Migration
 * Redolence Medi Aesthetics - Simplified migration without stored procedures
 */

require_once __DIR__ . '/config/app.php';

// Check if user is admin
if (!$auth->isAuthenticated() || !$auth->hasRole('ADMIN')) {
    die('Access denied. Admin privileges required.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple FAQ Migration - Redolence Medi Aesthetics</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #10b981; background: #ecfdf5; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #ef4444; background: #fef2f2; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #f59e0b; background: #fffbeb; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #3b82f6; background: #eff6ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .btn { background: #49a75c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #2d6a3e; }
    </style>
</head>
<body>
    <h1>Simple FAQ Services Migration</h1>
    <p>This simplified migration creates the necessary tables for FAQ-to-services linking without stored procedures.</p>

    <?php
    if (isset($_POST['apply_migration'])) {
        echo "<h2>Applying Simple Migration...</h2>";
        
        try {
            // Debug: Show current database connection info
            echo "<div class='info'>🔍 Database connection established</div>";

            // Debug: Check if faqs table exists
            $faqsTableCheck = $database->fetch("SHOW TABLES LIKE 'faqs'");
            if (!$faqsTableCheck) {
                throw new Exception("FAQs table does not exist. Please ensure the basic FAQ system is set up first.");
            }
            echo "<div class='info'>✅ FAQs table exists</div>";

            // Debug: Check if services table exists
            $servicesTableCheck = $database->fetch("SHOW TABLES LIKE 'services'");
            if (!$servicesTableCheck) {
                throw new Exception("Services table does not exist. Please ensure the services system is set up first.");
            }
            echo "<div class='info'>✅ Services table exists</div>";

            // Step 1: Create faq_services table
            echo "<h3>Step 1: Creating faq_services table</h3>";

            // First check if table already exists
            $tableCheck = $database->fetch("SHOW TABLES LIKE 'faq_services'");
            if ($tableCheck) {
                echo "<div class='info'>ℹ️ faq_services table already exists</div>";
            } else {
                $createTableSQL = "
                    CREATE TABLE faq_services (
                        id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
                        faq_id INT NOT NULL,
                        service_id VARCHAR(36) NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE KEY unique_faq_service (faq_id, service_id),
                        KEY idx_faq_services_faq_id (faq_id),
                        KEY idx_faq_services_service_id (service_id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";

                $database->query($createTableSQL);
                echo "<div class='success'>✅ faq_services table created successfully</div>";
            }
            
            // Step 2: Add service_link_text column to faqs table
            echo "<h3>Step 2: Adding service_link_text column</h3>";

            // Check if column already exists
            $columnCheck = $database->fetch("SHOW COLUMNS FROM faqs LIKE 'service_link_text'");
            if ($columnCheck) {
                echo "<div class='info'>ℹ️ service_link_text column already exists</div>";
            } else {
                try {
                    $addColumnSQL = "
                        ALTER TABLE faqs
                        ADD COLUMN service_link_text VARCHAR(255) DEFAULT NULL
                        COMMENT 'Custom text for service links'
                    ";
                    $database->query($addColumnSQL);
                    echo "<div class='success'>✅ service_link_text column added successfully</div>";
                } catch (Exception $e) {
                    echo "<div class='error'>❌ Failed to add service_link_text column: " . htmlspecialchars($e->getMessage()) . "</div>";
                    throw $e;
                }
            }
            
            // Step 3: Add indexes to faqs table
            echo "<h3>Step 3: Adding indexes for better performance</h3>";

            $indexes = [
                ["name" => "idx_faqs_category", "sql" => "ALTER TABLE faqs ADD INDEX idx_faqs_category (category)"],
                ["name" => "idx_faqs_active", "sql" => "ALTER TABLE faqs ADD INDEX idx_faqs_active (is_active)"],
                ["name" => "idx_faqs_display_order", "sql" => "ALTER TABLE faqs ADD INDEX idx_faqs_display_order (display_order)"]
            ];

            foreach ($indexes as $index) {
                try {
                    // Check if index exists first
                    $indexCheck = $database->fetch("SHOW INDEX FROM faqs WHERE Key_name = ?", [$index['name']]);
                    if ($indexCheck) {
                        echo "<div class='info'>ℹ️ Index {$index['name']} already exists</div>";
                    } else {
                        $database->query($index['sql']);
                        echo "<div class='success'>✅ Index {$index['name']} added successfully</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='warning'>⚠️ Index {$index['name']} creation failed: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            }
            
            // Step 4: Create the view
            echo "<h3>Step 4: Creating faq_with_services view</h3>";

            try {
                // Drop view if it exists first
                $database->query("DROP VIEW IF EXISTS faq_with_services");

                $createViewSQL = "
                    CREATE VIEW faq_with_services AS
                    SELECT
                        f.*,
                        GROUP_CONCAT(
                            CONCAT(s.id, ':', s.name, ':', COALESCE(s.price, 0), ':', COALESCE(s.duration, 0))
                            SEPARATOR '||'
                        ) as linked_services
                    FROM faqs f
                    LEFT JOIN faq_services fs ON f.id = fs.faq_id
                    LEFT JOIN services s ON fs.service_id = s.id AND s.is_active = 1
                    GROUP BY f.id, f.category, f.question, f.answer, f.display_order, f.is_active, f.created_at, f.updated_at, f.service_link_text
                    ORDER BY f.category, f.display_order, f.id
                ";

                $database->query($createViewSQL);
                echo "<div class='success'>✅ faq_with_services view created successfully</div>";
            } catch (Exception $e) {
                echo "<div class='error'>❌ Failed to create view: " . htmlspecialchars($e->getMessage()) . "</div>";
                throw $e;
            }
            
            // Verification
            echo "<h3>Verification</h3>";
            
            // Check if faq_services table was created
            $result = $database->fetch("SHOW TABLES LIKE 'faq_services'");
            if ($result) {
                echo "<div class='success'>✅ faq_services table verified</div>";
            } else {
                echo "<div class='error'>❌ faq_services table verification failed</div>";
            }
            
            // Check if service_link_text column was added
            $result = $database->fetch("SHOW COLUMNS FROM faqs LIKE 'service_link_text'");
            if ($result) {
                echo "<div class='success'>✅ service_link_text column verified</div>";
            } else {
                echo "<div class='error'>❌ service_link_text column verification failed</div>";
            }
            
            // Check if view was created
            $result = $database->fetch("SHOW TABLES LIKE 'faq_with_services'");
            if ($result) {
                echo "<div class='success'>✅ faq_with_services view verified</div>";
            } else {
                echo "<div class='error'>❌ faq_with_services view verification failed</div>";
            }
            
            // Show current counts
            $faqCount = $database->fetch("SELECT COUNT(*) as count FROM faqs")['count'];
            $serviceCount = $database->fetch("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'];
            
            echo "<div class='info'>📈 Current FAQ count: $faqCount</div>";
            echo "<div class='info'>📈 Current active service count: $serviceCount</div>";
            
            echo "<div class='success'><h3>🎉 Migration completed successfully!</h3></div>";
            echo "<div class='info'>The FAQ system now supports service linking. You can proceed to create sample content.</div>";
            
            echo "<br><a href='create_sample_faq_content.php' class='btn'>Create Sample Content</a>";
            echo " <a href='admin/faq/' class='btn'>Manage FAQs</a>";
            echo " <a href='faq.php' class='btn' style='background: #3b82f6;'>View Public FAQ Page</a>";
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Migration failed: " . htmlspecialchars($e->getMessage()) . "</div>";
            echo "<div class='info'>Please check your database permissions and try again.</div>";

            // Debug information
            echo "<h3>Debug Information:</h3>";
            echo "<div class='info'>Error occurred at: " . date('Y-m-d H:i:s') . "</div>";
            echo "<div class='info'>Error file: " . $e->getFile() . "</div>";
            echo "<div class='info'>Error line: " . $e->getLine() . "</div>";

            // Check current database state
            try {
                $tables = $database->fetchAll("SHOW TABLES");
                echo "<div class='info'>Current tables in database: " . count($tables) . "</div>";
                foreach ($tables as $table) {
                    $tableName = array_values($table)[0];
                    echo "<div class='info'>- $tableName</div>";
                }
            } catch (Exception $debugE) {
                echo "<div class='error'>Could not retrieve database information: " . htmlspecialchars($debugE->getMessage()) . "</div>";
            }
        }
        
    } else {
        ?>
        <div class="info">
            <h3>ℹ️ What this migration does:</h3>
            <ul>
                <li>Creates the <code>faq_services</code> table for linking FAQs to services</li>
                <li>Adds the <code>service_link_text</code> column to the <code>faqs</code> table</li>
                <li>Creates database indexes for better performance</li>
                <li>Creates a view for easy data retrieval</li>
                <li>Does NOT create stored procedures (avoiding syntax issues)</li>
            </ul>
        </div>
        
        <div class="warning">
            <h3>⚠️ Before proceeding:</h3>
            <ul>
                <li>Make sure you have a database backup</li>
                <li>Ensure you have proper database permissions</li>
                <li>This migration is safe and can be run multiple times</li>
            </ul>
        </div>
        
        <form method="post">
            <button type="submit" name="apply_migration" class="btn">Apply Simple Migration</button>
        </form>
        <?php
    }
    ?>
</body>
</html>
