<?php
/**
 * Gallery Data Migration Script
 * Converts hard-coded gallery data to database records
 */

require_once __DIR__ . '/../config/app.php';

// Check if running from command line or admin access
if (php_sapi_name() !== 'cli') {
    // If running from web, require admin authentication
    if (!isset($auth) || !$auth->isLoggedIn() || !$auth->hasRole('ADMIN')) {
        die('Access denied. Admin authentication required.');
    }
}

echo "Starting Gallery Data Migration...\n";

try {
    // First, create the table using individual SQL statements
    $createTableSQL = file_get_contents(__DIR__ . '/create_gallery_table.sql');
    
    // Split SQL statements and execute them individually
    $statements = array_filter(array_map('trim', explode(';', $createTableSQL)));
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            $database->execute($statement);
        }
    }
    echo "✓ Gallery table created successfully\n";

    // Check if data already exists
    $existingCount = $database->fetch("SELECT COUNT(*) as count FROM gallery_items")['count'];
    if ($existingCount > 0) {
        echo "⚠ Warning: Gallery table already contains {$existingCount} records.\n";
        echo "Do you want to continue and add demo data? (y/n): ";
        
        if (php_sapi_name() === 'cli') {
            $handle = fopen("php://stdin", "r");
            $response = trim(fgets($handle));
            fclose($handle);
        } else {
            $response = $_GET['force'] ?? 'n';
        }
        
        if (strtolower($response) !== 'y') {
            echo "Migration cancelled.\n";
            exit;
        }
    }

    // Original hard-coded gallery data
    $galleryItems = [
        [
            'title' => 'Facial Rejuvenation Results',
            'category' => 'before-after',
            'treatment' => 'HydraFacial + Chemical Peel',
            'before_image' => 'https://images.unsplash.com/photo-1594824804732-5f8fcaf009d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=90&fm=webp',
            'after_image' => 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=90&fm=webp',
            'description' => '6-week transformation showing improved skin texture, reduced fine lines, and enhanced radiance.',
            'duration' => '6 weeks',
            'sessions' => 4,
            'sort_order' => 1
        ],
        [
            'title' => 'Skin Texture Improvement',
            'category' => 'before-after',
            'treatment' => 'Microneedling + LED Therapy',
            'before_image' => 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=90&fm=webp',
            'after_image' => 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=90&fm=webp',
            'description' => 'Dramatic improvement in skin texture and pore appearance after microneedling treatment series.',
            'duration' => '8 weeks',
            'sessions' => 3,
            'sort_order' => 2
        ],
        [
            'title' => 'Treatment Room 1',
            'category' => 'clinic',
            'image' => 'https://images.unsplash.com/photo-1629909613654-28e377c37b09?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'description' => 'Our state-of-the-art treatment room equipped with the latest medical aesthetics technology.',
            'sort_order' => 3
        ],
        [
            'title' => 'Reception Area',
            'category' => 'clinic',
            'image' => 'https://images.unsplash.com/photo-1629909613654-28e377c37b09?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'description' => 'Welcome to our luxurious reception area designed for comfort and relaxation.',
            'sort_order' => 4
        ],
        [
            'title' => 'Anti-Aging Results',
            'category' => 'before-after',
            'treatment' => 'Botox + Dermal Fillers',
            'before_image' => 'https://images.unsplash.com/photo-**********-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'after_image' => 'https://images.unsplash.com/photo-**********-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'description' => 'Natural-looking anti-aging results with subtle enhancement and wrinkle reduction.',
            'duration' => '2 weeks',
            'sessions' => 1,
            'sort_order' => 5
        ],
        [
            'title' => 'Consultation Room',
            'category' => 'clinic',
            'image' => 'https://images.unsplash.com/photo-1631815589968-fdb09a223b1e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'description' => 'Private consultation room where we discuss your beauty goals and treatment plans.',
            'sort_order' => 6
        ]
    ];

    // Insert data into database
    $insertSQL = "INSERT INTO gallery_items (title, category, treatment, description, duration, sessions, before_image, after_image, image, sort_order, is_active) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)";

    $insertedCount = 0;
    foreach ($galleryItems as $item) {
        $params = [
            $item['title'],
            $item['category'],
            $item['treatment'] ?? null,
            $item['description'],
            $item['duration'] ?? null,
            $item['sessions'] ?? null,
            $item['before_image'] ?? null,
            $item['after_image'] ?? null,
            $item['image'] ?? null,
            $item['sort_order']
        ];

        if ($database->execute($insertSQL, $params)) {
            $insertedCount++;
            echo "✓ Inserted: {$item['title']}\n";
        } else {
            echo "✗ Failed to insert: {$item['title']}\n";
        }
    }

    echo "\n=== Migration Complete ===\n";
    echo "Successfully inserted {$insertedCount} gallery items.\n";
    echo "Gallery table is now ready for use.\n";

} catch (Exception $e) {
    echo "✗ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
