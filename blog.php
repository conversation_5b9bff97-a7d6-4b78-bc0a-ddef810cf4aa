<?php
/**
 * Ultra-Modern Blog Page - Completely Redesigned UI/UX
 * Premium Medical Aesthetics Knowledge Center
 * Redolence Medi Aesthetics - Luxury Medical Beauty Experience
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/blog_functions.php';

// Check if we're viewing a single blog post
$slug = $_GET['slug'] ?? null;
$search = $_GET['search'] ?? '';
$singlePost = null;

if ($slug) {
    // Get the single blog post by slug
    $singlePost = $database->fetch(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.slug = ? AND bp.status = 'published'",
        [$slug]
    );

    // If post not found, redirect to blog index
    if (!$singlePost) {
        redirect('/blog');
    }

    $pageTitle = $singlePost['title'] . ' - Redolence Blog';
} else {
    // Get search and pagination parameters
    $page = (int)($_GET['page'] ?? 1);
    $limit = 8; // Changed for new layout
    $offset = ($page - 1) * $limit;

    // Build search query
    $searchCondition = '';
    $searchConditionCount = '';
    $searchParams = [];
    if (!empty($search)) {
        $searchCondition = " AND (bp.title LIKE ? OR bp.summary LIKE ? OR bp.full_content LIKE ?)";
        $searchConditionCount = " AND (title LIKE ? OR summary LIKE ? OR full_content LIKE ?)";
        $searchTerm = '%' . $search . '%';
        $searchParams = [$searchTerm, $searchTerm, $searchTerm];
    }

    // Get total posts count
    $totalPosts = $database->fetch(
        "SELECT COUNT(*) as count FROM blog_posts WHERE status = 'published'" . $searchConditionCount,
        $searchParams
    )['count'];

    // Get featured posts (latest 3 posts)
    $featuredPosts = $database->fetchAll(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.status = 'published'
         ORDER BY bp.publish_date DESC, bp.created_at DESC
         LIMIT 3"
    );

    // Get regular blog posts (excluding featured ones if on first page)
    $excludeFeatured = ($page === 1 && empty($search)) ? " AND bp.id NOT IN ('" . implode("','", array_column($featuredPosts, 'id')) . "')" : '';

    $blogPosts = $database->fetchAll(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.status = 'published'" . $searchCondition . $excludeFeatured . "
         ORDER BY bp.publish_date DESC, bp.created_at DESC
         LIMIT $limit OFFSET $offset",
        $searchParams
    );

    $totalPages = ceil($totalPosts / $limit);
    $pageTitle = !empty($search) ? "Search: $search - Blog" : 'Medical Aesthetics Knowledge Hub - Redolence';
}

// Include header
include __DIR__ . '/includes/header.php';
?>

<!-- Revolutionary Blog Design - Completely Different from Original -->
<style>
:root {
    --redolence-green: #3EB489; /* Updated to a more luxurious teal */
    --redolence-blue: #4A6FA5; /* Updated to a deeper blue */
    --redolence-accent: #D4AF37; /* Gold accent color */
    --gradient-primary: linear-gradient(135deg, #3EB489 0%, #4A6FA5 100%);
    --gradient-secondary: linear-gradient(135deg, #4A6FA5 0%, #3EB489 100%);
    --gradient-gold: linear-gradient(135deg, #D4AF37 0%, #F5F5DC 100%);
    --gradient-hero: linear-gradient(135deg, #F9FAFB 0%, #EDF2F7 50%, #E2E8F0 100%);
    --text-primary: #1A202C;
    --text-secondary: #4A5568;
    --text-muted: #718096;
    --text-light: #F7FAFC;
    --bg-primary: #FFFFFF;
    --bg-secondary: #F7FAFC;
    --bg-tertiary: #EDF2F7;
    --bg-glass: rgba(255, 255, 255, 0.95);
    --bg-glass-dark: rgba(26, 32, 44, 0.8);
    --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.1);
    --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.12);
    --border-radius-sm: 8px;
    --border-radius-md: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;
    --border-radius-full: 9999px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Ultra-Modern Layout System */
.blog-revolution {
    min-height: 100vh;
    background: var(--bg-primary);
    position: relative;
    overflow-x: hidden;
    font-family: 'Poppins', 'Helvetica Neue', sans-serif;
    color: var(--text-primary);
}

/* Blog Container */
.blog-revolution {
    position: relative;
    display: flex;
    flex-direction: column;
}

/* Ultra-Modern Hero Section - Enhanced */
.magazine-hero {
    padding: 180px 0 120px;
    position: relative;
    background: var(--bg-secondary);
    overflow: hidden;
}

.magazine-hero::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -5%;
    width: 700px;
    height: 700px;
    border-radius: 40% 60% 70% 30% / 40% 50% 60% 50%;
    background: var(--gradient-primary);
    opacity: 0.07;
    z-index: 0;
    animation: float-slow 20s infinite alternate ease-in-out;
}

.magazine-hero::after {
    content: '';
    position: absolute;
    bottom: -15%;
    left: -10%;
    width: 900px;
    height: 900px;
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    background: var(--gradient-secondary);
    opacity: 0.07;
    z-index: 0;
    animation: float-slow 15s infinite alternate-reverse ease-in-out;
}

@keyframes float-slow {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(2%, 2%) rotate(5deg); }
}

.hero-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 120px;
    align-items: center;
    position: relative;
    z-index: 1;
    animation: fade-in 1s ease-out;
}

@keyframes fade-in {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.hero-text {
    position: relative;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background: var(--gradient-gold);
    color: var(--text-primary);
    padding: 12px 28px;
    border-radius: var(--border-radius-full);
    font-size: 14px;
    font-weight: 700;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    margin-bottom: 36px;
    box-shadow: var(--shadow-soft);
    position: relative;
    overflow: hidden;
}

.hero-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.3);
    transform: skewX(-30deg);
    transition: all 0.6s ease;
    animation: badge-shine 3s infinite;
}

@keyframes badge-shine {
    0% { left: -100%; }
    20% { left: 100%; }
    100% { left: 100%; }
}

.hero-title {
    font-size: clamp(3.5rem, 6vw, 5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 28px;
    color: var(--text-primary);
    letter-spacing: -0.02em;
    position: relative;
}

.hero-title .highlight {
    position: relative;
    display: inline-block;
    color: var(--redolence-green);
    z-index: 1;
}

.hero-title .highlight::after {
    content: '';
    position: absolute;
    bottom: 5%;
    left: -5%;
    width: 110%;
    height: 30%;
    background: var(--gradient-primary);
    opacity: 0.2;
    z-index: -1;
    border-radius: var(--border-radius-sm);
    transform: rotate(-2deg);
}

.hero-subtitle {
    font-size: 1.35rem;
    font-weight: 400;
    line-height: 1.1;
    color: var(--text-primary);
    margin-bottom: 32px;
    letter-spacing: -0.02em;
}

.hero-title .highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    display: inline-block;
}

.hero-title .highlight::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    width: 100%;
    height: 8px;
    background: var(--gradient-primary);
    opacity: 0.2;
    z-index: -1;
}

.hero-subtitle {
    font-size: 1.35rem;
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 48px;
    max-width: 540px;
    font-weight: 300;
    position: relative;
    padding-left: 16px;
    border-left: 3px solid var(--redolence-green);
}

/* Ultra-Modern Search Design - Enhanced */
.hero-search {
    position: relative;
    max-width: 700px;
}

.search-wrapper {
    position: relative;
    background: var(--bg-glass);
    border-radius: var(--border-radius-xl);
    padding: 8px;
    box-shadow: var(--shadow-medium);
    border: 2px solid rgba(0, 0, 0, 0.03);
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
}

.search-wrapper:focus-within {
    border-color: var(--redolence-green);
    box-shadow: var(--shadow-strong);
    transform: translateY(-4px);
}

.search-wrapper::before {
    content: '🔍';
    position: absolute;
    left: 28px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    color: var(--redolence-green);
    z-index: 1;
    transition: all var(--transition-normal);
}

.search-wrapper:focus-within::before {
    color: var(--redolence-accent);
}

.search-input {
    width: 100%;
    padding: 22px 140px 22px 70px;
    border: none;
    border-radius: var(--border-radius-xl);
    font-size: 18px;
    background: transparent;
    color: var(--text-primary);
    outline: none;
    font-weight: 400;
    letter-spacing: 0.01em;
}

.search-input::placeholder {
    color: var(--text-muted);
    font-weight: 300;
    opacity: 0.7;
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--border-radius-full);
    padding: 18px 32px;
    color: var(--text-light);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-soft);
    display: flex;
    align-items: center;
    gap: 10px;
    overflow: hidden;
}

.search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transform: skewX(-20deg);
    transition: all 0.6s ease;
}

.search-btn:hover::before {
    left: 100%;
}

.search-btn::after {
    content: '→';
    font-size: 22px;
    transition: transform var(--transition-fast);
}

.search-btn:hover {
    transform: translateY(-50%) scale(1.03);
    box-shadow: var(--shadow-medium);
}

.search-btn:hover::after {
    transform: translateX(6px);
}

/* Hero Visual Grid - Ultra-Modern 3D Cards */
.hero-visual {
    position: relative;
    height: 580px;
    display: flex;
    align-items: center;
    justify-content: center;
    perspective: 1200px;
    z-index: 2;
}

.hero-visual::before {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: var(--gradient-primary);
    opacity: 0.1;
    filter: blur(60px);
    z-index: -1;
    animation: float-blob 15s infinite alternate ease-in-out;
}

@keyframes float-blob {
    0% { transform: translate(0, 0) scale(1); }
    100% { transform: translate(50px, 30px) scale(1.3); }
}

.visual-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transform: rotateX(5deg) rotateY(-5deg);
    transition: all var(--transition-slow);
}

.visual-grid:hover {
    transform: rotateX(0) rotateY(0);
}

.visual-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-xl);
    padding: 40px;
    box-shadow: var(--shadow-soft);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-slow);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    display: flex;
    flex-direction: column;
    transform: translateZ(0);
}

.visual-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-slow);
    transform-origin: left;
    z-index: 1;
}

.visual-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 0;
}

.visual-card:hover::before {
    transform: scaleX(1);
}

.visual-card:hover {
    transform: translateY(-15px) translateZ(40px);
    box-shadow: var(--shadow-strong);
    background: var(--bg-glass);
}

.visual-card:nth-child(odd):hover {
    transform: translateY(-15px) translateZ(40px) rotate(2deg);
}

.visual-card:nth-child(even):hover {
    transform: translateY(-15px) translateZ(40px) rotate(-2deg);
}

.card-icon {
    width: 64px;
    height: 64px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    color: var(--text-light);
    font-size: 28px;
    box-shadow: 0 10px 25px rgba(62, 180, 137, 0.3);
    position: relative;
    z-index: 2;
    transform: translateZ(15px);
    transition: all var(--transition-fast);
}

.visual-card:hover .card-icon {
    transform: translateZ(25px) scale(1.05);
    box-shadow: 0 15px 30px rgba(62, 180, 137, 0.4);
}

.card-title {
    font-size: 22px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
    position: relative;
    z-index: 2;
    transform: translateZ(10px);
    transition: all var(--transition-fast);
    letter-spacing: -0.5px;
}

.visual-card:hover .card-title {
    transform: translateZ(20px);
    color: var(--redolence-green);
}

.card-desc {
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-secondary);
    position: relative;
    z-index: 2;
    transform: translateZ(5px);
    transition: all var(--transition-fast);
    font-weight: 300;
}

/* Responsive Design - Modern Approach */
@media (max-width: 1200px) {
    .hero-content {
        gap: 60px;
    }
    
    .hero-title {
        font-size: clamp(3rem, 6vw, 4.5rem);
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
}

@media (max-width: 1024px) {
    .magazine-hero {
        padding: 140px 0 80px;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: 80px;
        text-align: center;
    }
    
    .hero-subtitle {
        margin-left: auto;
        margin-right: auto;
    }
    
    .hero-search {
        margin: 0 auto;
    }
    
    .hero-visual {
        height: auto;
        min-height: 480px;
    }
}

@media (max-width: 768px) {
    .magazine-hero {
        padding: 120px 0 60px;
        background: var(--bg-secondary);
    }

    .hero-content {
        padding: 0 24px;
        gap: 60px;
    }
    
    .hero-badge {
        padding: 8px 20px;
        font-size: 12px;
        margin-bottom: 24px;
    }
    
    .hero-title {
        font-size: clamp(2.5rem, 10vw, 3.5rem);
        margin-bottom: 24px;
    }
    
    .hero-subtitle {
        font-size: 1.15rem;
        margin-bottom: 36px;
        max-width: 100%;
    }

    .hero-visual {
        min-height: 420px;
    }

    .visual-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .visual-card {
        padding: 28px;
        min-height: 140px;
        background: var(--bg-primary);
        border: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: var(--shadow-medium);
    }

    .card-icon {
        width: 48px;
        height: 48px;
        font-size: 20px;
        margin-bottom: 16px;
        box-shadow: 0 6px 16px rgba(62, 180, 137, 0.2);
    }

    .card-title {
        font-size: 18px;
        margin-bottom: 8px;
    }

    .card-desc {
        font-size: 14px;
        line-height: 1.5;
    }
    
    .search-wrapper::before {
        left: 20px;
        font-size: 16px;
    }
    
    .search-input {
        padding: 18px 100px 18px 50px;
        font-size: 16px;
    }
    
    .search-btn {
        padding: 14px 24px;
    }
}

@media (max-width: 480px) {
    .magazine-hero {
        padding: 100px 0 40px;
    }

    .hero-content {
        padding: 0 20px;
        gap: 40px;
    }

    .hero-title {
        font-size: 2.25rem;
        margin-bottom: 16px;
        letter-spacing: -0.01em;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 28px;
        line-height: 1.6;
    }

    .visual-grid {
        gap: 16px;
    }

    .visual-card {
        padding: 20px;
        min-height: 100px;
    }

    .card-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
        margin-bottom: 12px;
    }

    .card-title {
        font-size: 16px;
        margin-bottom: 6px;
    }

    .card-desc {
        font-size: 13px;
        line-height: 1.4;
    }

    .search-wrapper {
        padding: 5px;
    }
    
    .search-wrapper::before {
        left: 16px;
        font-size: 14px;
    }

    .search-input {
        padding: 15px 80px 15px 42px;
        font-size: 14px;
    }

    .search-btn {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    .search-btn::after {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .article-body-new {
        font-size: 1rem;
        line-height: 1.6;
    }
    
    .article-body-new h2 {
        font-size: 1.5rem;
        padding-left: 15px;
        margin: 2rem 0 1rem;
    }
    
    .article-body-new h3 {
        font-size: 1.25rem;
        margin: 1.5rem 0 0.8rem;
    }
    
    .post-title-hero {
        font-size: 1.8rem;
    }
    
    .post-summary-hero {
        font-size: 1rem;
        line-height: 1.5;
    }
    
    .meta-pill {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    .social-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .back-btn-new {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    .article-container-new {
        padding: 40px 15px;
    }
    
    .article-toc {
        padding: 15px;
    }
    
    .toc-header {
        font-size: 1.2rem;
    }
    
    .toc-body {
        font-size: 0.9rem;
    }
}
</style>

<!-- Revolutionary Blog Container -->
<div class="blog-revolution">


    <?php if ($singlePost): ?>
        <!-- Ultra-Modern Single Post Layout - Premium Experience -->
        <div class="immersive-post">
            <!-- Elegant Full-Screen Header -->
            <div class="post-hero-immersive">
                <?php if ($singlePost['image_url']): ?>
                    <div class="hero-image-full">
                        <img src="<?= htmlspecialchars(getBlogImageUrl($singlePost['image_url'])) ?>"
                             alt="<?= htmlspecialchars($singlePost['title']) ?>">
                        <div class="hero-overlay"></div>
                    </div>
                <?php else: ?>
                    <div class="hero-gradient-fallback"></div>
                <?php endif; ?>

                <div class="hero-content-floating">
                    <div class="post-meta-pills">
                        <span class="meta-pill author-pill">
                            <span class="pill-icon">👤</span>
                            <span class="pill-text"><?= htmlspecialchars($singlePost['author_name'] ?? 'Redolence Team') ?></span>
                        </span>
                        <span class="meta-pill date-pill">
                            <span class="pill-icon">📅</span>
                            <span class="pill-text"><?= $singlePost['publish_date'] ? date('M j, Y', strtotime($singlePost['publish_date'])) : date('M j, Y', strtotime($singlePost['created_at'])) ?></span>
                        </span>
                        <span class="meta-pill time-pill">
                            <span class="pill-icon">⏱️</span>
                            <span class="pill-text">
                                <?php
                                $wordCount = str_word_count(strip_tags($singlePost['full_content']));
                                $readTime = max(1, ceil($wordCount / 200));
                                echo "$readTime min read";
                                ?>
                            </span>
                        </span>
                    </div>

                    <h1 class="post-title-hero"><?= htmlspecialchars($singlePost['title']) ?></h1>

                    <?php if (!empty($singlePost['summary'])): ?>
                        <p class="post-summary-hero"><?= htmlspecialchars($singlePost['summary']) ?></p>
                    <?php endif; ?>
                    
                    <div class="scroll-indicator">
                        <div class="scroll-icon">↓</div>
                        <div class="scroll-text">Scroll to read</div>
                    </div>
                </div>
            </div>

            <!-- Premium Article Content -->
            <div class="article-container-new">
                <div class="article-content-wrapper">
                    <!-- Table of Contents -->
                    <div class="article-toc">
                        <div class="toc-header">Contents</div>
                        <div class="toc-body" id="article-toc-content">
                            <!-- JavaScript will populate this -->
                            <div class="toc-placeholder">Loading contents...</div>
                        </div>
                    </div>
                    
                    <div class="article-body-new">
                        <?= $singlePost['full_content'] ?>
                    </div>

                    <!-- Floating Social Actions -->
                    <div class="social-actions-floating">
                        <button class="social-btn share-btn" title="Share" aria-label="Share this article">
                            <span class="btn-icon">📤</span>
                            <span class="btn-tooltip">Share</span>
                        </button>
                        <button class="social-btn bookmark-btn" title="Bookmark" aria-label="Bookmark this article">
                            <span class="btn-icon">🔖</span>
                            <span class="btn-tooltip">Save</span>
                        </button>
                        <button class="social-btn like-btn" title="Like" aria-label="Like this article">
                            <span class="btn-icon">❤️</span>
                            <span class="btn-tooltip">Like</span>
                        </button>
                    </div>
                </div>
                
                <!-- Author Bio Section -->
                <div class="author-bio-section">
                    <div class="author-avatar">
                        <div class="avatar-placeholder">👤</div>
                    </div>
                    <div class="author-details">
                        <h3 class="author-name"><?= htmlspecialchars($singlePost['author_name'] ?? 'Redolence Team') ?></h3>
                        <p class="author-description">Medical aesthetics specialist with expertise in advanced treatments and procedures. Passionate about helping clients achieve their beauty and wellness goals.</p>
                        <div class="author-social">
                            <a href="#" class="social-link" aria-label="Author's LinkedIn profile">LinkedIn</a>
                            <a href="#" class="social-link" aria-label="Author's Twitter profile">Twitter</a>
                        </div>
                    </div>
                </div>

                <!-- Back Navigation -->
                <div class="back-navigation">
                    <a href="<?= getBasePath() ?>/blog" class="back-btn-new">
                        <span class="back-icon">←</span>
                        <span class="back-text">Back to Knowledge Center</span>
                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Revolutionary Magazine-Style Hero -->
        <div class="magazine-hero">
            <div class="hero-content">
                <div class="hero-text">
                    <div class="hero-badge">
                        ⭐ Medical Aesthetics Hub
                    </div>

                    <h1 class="hero-title">
                        Discover the <span class="highlight">Future</span> of Medical Beauty
                    </h1>

                    <p class="hero-subtitle">
                        Explore cutting-edge treatments, expert insights, and revolutionary techniques that are transforming the world of medical aesthetics.
                    </p>

                    <!-- Revolutionary Search -->
                    <form method="GET" action="<?= getBasePath() ?>/blog" class="hero-search">
                        <div class="search-wrapper">
                            <input
                                type="text"
                                name="search"
                                value="<?= htmlspecialchars($search) ?>"
                                placeholder="Search treatments, procedures, expert advice..."
                                class="search-input"
                            >
                            <button type="submit" class="search-btn">
                                🔍 Explore
                            </button>
                        </div>
                    </form>
                </div>

                <div class="hero-visual">
                    <div class="visual-grid">
                        <div class="visual-card">
                            <div class="card-icon">🏥</div>
                            <h3 class="card-title">Expert Insights</h3>
                            <p class="card-desc">Professional guidance from certified medical aesthetics specialists</p>
                        </div>

                        <div class="visual-card">
                            <div class="card-icon">💡</div>
                            <h3 class="card-title">Latest Innovations</h3>
                            <p class="card-desc">Cutting-edge treatments and breakthrough technologies</p>
                        </div>

                        <div class="visual-card">
                            <div class="card-icon">📚</div>
                            <h3 class="card-title">Educational Content</h3>
                            <p class="card-desc">Comprehensive guides and evidence-based information</p>
                        </div>

                        <div class="visual-card">
                            <div class="card-icon">📊</div>
                            <h3 class="card-title">Real Results</h3>
                            <p class="card-desc">Case studies and patient transformation stories</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revolutionary Content Sections -->

        <!-- Spotlight Section - Completely Different Layout -->
        <?php if ($page === 1 && empty($search) && !empty($featuredPosts)): ?>
        <section class="spotlight-revolution">
            <div class="spotlight-wrapper">
                <div class="section-header-new">
                    <div class="header-badge-new">✨ Featured Spotlight</div>
                    <h2 class="section-title-new">
                        <span class="title-word">Latest</span>
                        <span class="title-word highlight">Breakthroughs</span>
                    </h2>
                </div>

                <!-- Revolutionary Asymmetric Layout -->
                <div class="asymmetric-grid">
                    <?php $mainPost = $featuredPosts[0]; ?>
                    <!-- Dominant Feature Article -->
                    <div class="feature-dominant">
                        <div class="feature-card-new">
                            <?php if ($mainPost['image_url']): ?>
                                <div class="feature-image-new">
                                    <img src="<?= htmlspecialchars(getBlogImageUrl($mainPost['image_url'])) ?>"
                                         alt="<?= htmlspecialchars($mainPost['title']) ?>">
                                    <div class="image-gradient-new"></div>
                                </div>
                            <?php endif; ?>

                            <div class="feature-content-new">
                                <div class="feature-meta-new">
                                    <span class="trending-badge">🔥 Trending Now</span>
                                    <span class="date-badge">
                                        <?= $mainPost['publish_date'] ? date('M j', strtotime($mainPost['publish_date'])) : date('M j', strtotime($mainPost['created_at'])) ?>
                                    </span>
                                </div>

                                <h3 class="feature-title-new">
                                    <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($mainPost['slug']) ?>">
                                        <?= htmlspecialchars($mainPost['title']) ?>
                                    </a>
                                </h3>

                                <?php if (!empty($mainPost['summary'])): ?>
                                    <p class="feature-excerpt-new"><?= htmlspecialchars($mainPost['summary']) ?></p>
                                <?php endif; ?>

                                <div class="feature-footer-new">
                                    <div class="author-info-new">
                                        <div class="author-avatar-new">👤</div>
                                        <span class="author-name-new"><?= htmlspecialchars($mainPost['author_name'] ?? 'Redolence Team') ?></span>
                                    </div>

                                    <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($mainPost['slug']) ?>" class="read-btn-new">
                                        Read Article →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Compact Side Articles -->
                    <div class="feature-sidebar-new">
                        <?php for ($i = 1; $i < min(3, count($featuredPosts)); $i++): ?>
                            <?php $post = $featuredPosts[$i]; ?>
                            <div class="compact-article">
                                <?php if ($post['image_url']): ?>
                                    <div class="compact-image">
                                        <img src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>"
                                             alt="<?= htmlspecialchars($post['title']) ?>">
                                    </div>
                                <?php endif; ?>

                                <div class="compact-content">
                                    <div class="compact-meta">
                                        <span class="category-tag">Featured</span>
                                        <span class="time-tag">
                                            <?= $post['publish_date'] ? date('M j', strtotime($post['publish_date'])) : date('M j', strtotime($post['created_at'])) ?>
                                        </span>
                                    </div>

                                    <h4 class="compact-title">
                                        <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>">
                                            <?= htmlspecialchars($post['title']) ?>
                                        </a>
                                    </h4>

                                    <div class="compact-author">
                                        👤 <?= htmlspecialchars($post['author_name'] ?? 'Redolence Team') ?>
                                    </div>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Revolutionary Masonry-Style Grid -->
        <?php if (!empty($blogPosts)): ?>
        <section class="masonry-revolution">
            <div class="masonry-wrapper">
                <?php if ($page === 1 && empty($search)): ?>
                    <div class="section-header-masonry">
                        <h2 class="masonry-title">
                            <span class="title-accent">Latest</span> Insights
                        </h2>
                        <p class="masonry-subtitle">Discover the most recent advances in medical aesthetics</p>
                    </div>
                <?php elseif (!empty($search)): ?>
                    <div class="search-results-header-new">
                        <h2 class="results-title-new">
                            Search Results for "<span class="search-term-new"><?= htmlspecialchars($search) ?></span>"
                        </h2>
                        <p class="results-count-new"><?= $totalPosts ?> article<?= $totalPosts !== 1 ? 's' : '' ?> found</p>
                    </div>
                <?php endif; ?>

                <!-- Revolutionary Pinterest-Style Grid -->
                <div class="pinterest-grid">
                    <?php foreach ($blogPosts as $index => $post): ?>
                        <article class="pinterest-item <?= $index % 3 === 0 ? 'pinterest-tall' : '' ?>">
                            <div class="pinterest-card">
                                <?php if ($post['image_url']): ?>
                                    <div class="pinterest-image">
                                        <img src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>"
                                             alt="<?= htmlspecialchars($post['title']) ?>"
                                             loading="lazy">
                                        <div class="image-overlay-pinterest"></div>
                                    </div>
                                <?php endif; ?>

                                <div class="pinterest-content">
                                    <div class="pinterest-meta">
                                        <span class="meta-category-new">Medical Aesthetics</span>
                                        <span class="meta-date-new">
                                            <?= $post['publish_date'] ? date('M j, Y', strtotime($post['publish_date'])) : date('M j, Y', strtotime($post['created_at'])) ?>
                                        </span>
                                    </div>

                                    <h3 class="pinterest-title">
                                        <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>">
                                            <?= htmlspecialchars($post['title']) ?>
                                        </a>
                                    </h3>

                                    <?php if (!empty($post['summary'])): ?>
                                        <p class="pinterest-excerpt"><?= htmlspecialchars($post['summary']) ?></p>
                                    <?php endif; ?>

                                    <div class="pinterest-footer">
                                        <div class="author-badge-new">
                                            👤 <span><?= htmlspecialchars($post['author_name'] ?? 'Redolence Team') ?></span>
                                        </div>

                                        <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>" class="continue-btn">
                                            Continue Reading →
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Revolutionary Pagination -->
        <?php if ($totalPages > 1): ?>
        <section class="pagination-revolution">
            <div class="pagination-wrapper-new">
                <div class="pagination-controls">
                    <!-- Previous Button -->
                    <?php if ($page > 1): ?>
                        <a href="<?= getBasePath() ?>/blog?page=<?= $page - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>"
                           class="pagination-btn-new pagination-prev-new">
                            ← Previous
                        </a>
                    <?php endif; ?>

                    <!-- Page Numbers -->
                    <div class="pagination-numbers-new">
                        <?php
                        $startPage = max(1, $page - 2);
                        $endPage = min($totalPages, $page + 2);

                        for ($i = $startPage; $i <= $endPage; $i++):
                        ?>
                            <?php if ($i == $page): ?>
                                <span class="pagination-number-new active"><?= $i ?></span>
                            <?php else: ?>
                                <a href="<?= getBasePath() ?>/blog?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>"
                                   class="pagination-number-new"><?= $i ?></a>
                            <?php endif; ?>
                        <?php endfor; ?>
                    </div>

                    <!-- Next Button -->
                    <?php if ($page < $totalPages): ?>
                        <a href="<?= getBasePath() ?>/blog?page=<?= $page + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>"
                           class="pagination-btn-new pagination-next-new">
                            Next →
                        </a>
                    <?php endif; ?>
                </div>

                <div class="pagination-info-new">
                    <span>Page <?= $page ?> of <?= $totalPages ?></span>
                    <span class="separator">•</span>
                    <span><?= $totalPosts ?> total articles</span>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- No Results State -->
        <?php if (empty($blogPosts) && (empty($featuredPosts) || $page > 1 || !empty($search))): ?>
        <section class="no-results-revolution">
            <div class="no-results-wrapper">
                <div class="no-results-visual-new">
                    <div class="empty-icon">🔍</div>
                </div>

                <div class="no-results-content-new">
                    <h3 class="no-results-title-new">
                        <?= !empty($search) ? 'No articles found' : 'No articles available' ?>
                    </h3>
                    <p class="no-results-text-new">
                        <?= !empty($search) ? 'Try adjusting your search terms or explore our featured content.' : 'Check back soon for new medical aesthetics insights and expert guidance.' ?>
                    </p>

                    <?php if (!empty($search)): ?>
                        <div class="no-results-actions-new">
                            <a href="<?= getBasePath() ?>/blog" class="action-btn-primary-new">
                                ⊞ Browse All Articles
                            </a>
                            <button class="action-btn-secondary-new" onclick="document.querySelector('.search-input').focus()">
                                🔍 Try New Search
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
/* Revolutionary Content Styling - Completely New */

/* Ultra-Modern Single Post Styles */
.immersive-post {
    background: var(--bg-primary);
    position: relative;
}

.post-hero-immersive {
    position: relative;
    height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-color: var(--bg-dark);
}

.hero-image-full {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.hero-image-full img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 10s ease;
    transform-origin: center;
    animation: subtle-zoom 20s infinite alternate ease-in-out;
}

@keyframes subtle-zoom {
    0% { transform: scale(1); }
    100% { transform: scale(1.05); }
}

.hero-gradient-fallback {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-secondary);
    opacity: 0.9;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.3));
    backdrop-filter: blur(5px);
}

.hero-content-floating {
    position: relative;
    z-index: 10;
    text-align: center;
    color: var(--text-light);
    max-width: 1000px;
    padding: 0 40px;
    animation: fade-in-up 1.2s ease-out;
}

@keyframes fade-in-up {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.post-meta-pills {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 36px;
    flex-wrap: wrap;
}

.meta-pill {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    padding: 12px 24px;
    border-radius: var(--border-radius-full);
    font-size: 15px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.meta-pill:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-4px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.2);
}

.pill-icon {
    font-size: 16px;
}

.pill-text {
    letter-spacing: 0.5px;
}

.post-title-hero {
    font-size: clamp(3rem, 8vw, 5.5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 32px;
    text-shadow: 0 8px 40px rgba(0, 0, 0, 0.5);
    letter-spacing: -0.03em;
    background: linear-gradient(to right, #ffffff, #e0e0e0);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    animation: title-shimmer 3s infinite alternate;
}

@keyframes title-shimmer {
    0% { background-position: -100px; }
    100% { background-position: 100px; }
}

.post-summary-hero {
    font-size: 1.5rem;
    line-height: 1.8;
    opacity: 0.95;
    max-width: 800px;
    margin: 0 auto 50px;
    font-weight: 300;
    position: relative;
    padding: 0 20px;
}

.post-summary-hero::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10%;
    height: 80%;
    width: 4px;
    background: var(--redolence-green);
    border-radius: var(--border-radius-sm);
}

.scroll-indicator {
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--text-light);
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.8;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0) translateX(-50%); }
    40% { transform: translateY(-10px) translateX(-50%); }
    60% { transform: translateY(-5px) translateX(-50%); }
}

.scroll-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.scroll-text {
    font-size: 14px;
    font-weight: 300;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.article-container-new {
    max-width: 1200px;
    margin: 0 auto;
    padding: 120px 40px;
    position: relative;
}

.article-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 80px;
    position: relative;
}

.article-toc {
    position: sticky;
    top: 100px;
    align-self: start;
    background: var(--bg-glass);
    backdrop-filter: blur(15px);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-soft);
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.article-toc:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-5px);
}

.toc-header {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    letter-spacing: -0.5px;
    position: relative;
}

.toc-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--redolence-green);
    border-radius: var(--border-radius-sm);
}

.toc-body {
    font-size: 15px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.toc-placeholder {
    color: var(--text-muted);
    font-style: italic;
    padding: 10px 0;
    position: relative;
}

.toc-placeholder::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--redolence-green);
    transform: translateY(-50%);
    opacity: 0.6;
}

.article-body-new {
    font-size: 1.2rem;
    line-height: 1.9;
    color: var(--text-primary);
    position: relative;
}

.article-body-new p {
    margin-bottom: 1.8rem;
}

.article-body-new a {
    color: var(--redolence-green);
    text-decoration: none;
    position: relative;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.article-body-new a:hover {
    color: var(--redolence-dark-green);
}

.article-body-new a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background: var(--redolence-green);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform var(--transition-fast);
}

.article-body-new a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

.article-body-new h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 4rem 0 1.8rem;
    color: var(--text-primary);
    position: relative;
    padding-left: 28px;
    letter-spacing: -0.02em;
}

.article-body-new h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-sm);
}

.article-body-new h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 2.5rem 0 1.25rem;
    color: var(--text-primary);
}

.article-body-new p {
    margin-bottom: 1.75rem;
    font-weight: 400;
}

.article-body-new a {
    color: var(--redolence-green);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all var(--transition-normal);
    font-weight: 500;
}

.article-body-new a:hover {
    border-bottom-color: var(--redolence-green);
    color: var(--redolence-green);
}

.article-body-new ul, .article-body-new ol {
    margin: 1.5rem 0 2rem 1.5rem;
}

.article-body-new li {
    margin-bottom: 0.75rem;
}

.article-body-new blockquote {
    border-left: 4px solid var(--redolence-green);
    padding: 1rem 0 1rem 2rem;
    margin: 2rem 0;
    font-style: italic;
    background: var(--bg-secondary);
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.social-actions-floating {
    position: fixed;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 25px;
    z-index: 100;
}

.social-btn {
    width: 65px;
    height: 65px;
    background: var(--bg-glass);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--redolence-green);
    font-size: 24px;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-soft);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.social-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.btn-tooltip {
    position: absolute;
    right: 80px;
    background: var(--bg-glass);
    color: var(--text-primary);
    padding: 8px 16px;
    border-radius: var(--border-radius-md);
    font-size: 15px;
    font-weight: 500;
    opacity: 0;
    transform: translateX(15px);
    transition: all var(--transition-normal);
    pointer-events: none;
    white-space: nowrap;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-soft);
}

.social-btn:hover .btn-tooltip {
    opacity: 1;
    transform: translateX(0);
}

.social-btn:hover {
    transform: scale(1.15);
    box-shadow: var(--shadow-medium);
    color: var(--text-light);
    border-color: transparent;
}

.social-btn:hover::before {
    opacity: 1;
}

.author-bio-section {
    margin-top: 80px;
    padding: 50px;
    background: var(--bg-glass);
    backdrop-filter: blur(15px);
    border-radius: var(--border-radius-xl);
    display: flex;
    gap: 40px;
    align-items: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-soft);
    position: relative;
    overflow: hidden;
}

.author-bio-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
    z-index: 1;
}

.author-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    color: var(--text-light);
    box-shadow: var(--shadow-strong);
    position: relative;
    z-index: 2;
    border: 4px solid rgba(255, 255, 255, 0.2);
    transform: translateZ(10px);
}

.author-details {
    flex: 1;
    position: relative;
    z-index: 2;
}

.author-name {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 12px;
    color: var(--text-primary);
    letter-spacing: -0.5px;
}

.author-description {
    color: var(--text-secondary);
    line-height: 1.7;
    font-size: 1.05rem;
    margin-bottom: 20px;
}

.author-social {
    display: flex;
    gap: 15px;
    margin-top: 5px;
}

.social-link {
    display: inline-block;
    padding: 8px 16px;
    background: var(--bg-primary);
    color: var(--redolence-green);
    border-radius: var(--border-radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    transition: all var(--transition-fast);
    border: 1px solid rgba(62, 180, 137, 0.2);
}

.social-link:hover {
    background: var(--redolence-green);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(62, 180, 137, 0.25);
}

.back-navigation {
    text-align: center;
    margin-top: 100px;
    margin-bottom: 40px;
}

.back-btn-new {
    display: inline-flex;
    align-items: center;
    gap: 15px;
    background: var(--bg-glass);
    color: var(--redolence-green);
    padding: 20px 40px;
    border-radius: var(--border-radius-full);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-soft);
    border: 2px solid rgba(62, 180, 137, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.back-btn-new::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    z-index: -1;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.back-icon {
    font-size: 22px;
    transition: transform var(--transition-fast);
    display: inline-block;
}

.back-btn-new:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    color: var(--text-light);
    border-color: transparent;
}

.back-btn-new:hover::before {
    opacity: 1;
}

.back-btn-new:hover .back-icon {
    transform: translateX(-5px);
}

/* Spotlight Section */
.spotlight-revolution {
    padding: 100px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
}

.spotlight-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

.section-header-new {
    text-align: center;
    margin-bottom: 80px;
}

.header-badge-new {
    display: inline-block;
    background: var(--gradient-primary);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 20px;
}

.section-title-new {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 900;
    line-height: 1.1;
    color: var(--text-primary);
}

.title-word {
    display: inline-block;
    margin-right: 0.5em;
}

.title-word.highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Asymmetric Grid */
.asymmetric-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: start;
}

.feature-dominant {
    position: relative;
}

.feature-card-new {
    background: white;
    border-radius: 32px;
    overflow: hidden;
    box-shadow: var(--shadow-strong);
    transition: all 0.5s ease;
    position: relative;
}

.feature-card-new:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2);
}

.feature-image-new {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.feature-image-new img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.7s ease;
}

.feature-card-new:hover .feature-image-new img {
    transform: scale(1.1);
}

.image-gradient-new {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}

.feature-content-new {
    padding: 40px;
}

.feature-meta-new {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
}

.trending-badge {
    background: var(--gradient-primary);
    color: white;
    padding: 6px 16px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.date-badge {
    color: var(--text-muted);
    font-size: 14px;
    font-weight: 600;
}

.feature-title-new {
    font-size: 2rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 16px;
}

.feature-title-new a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.feature-title-new a:hover {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.feature-excerpt-new {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 32px;
}

.feature-footer-new {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.author-info-new {
    display: flex;
    align-items: center;
    gap: 12px;
}

.author-avatar-new {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.author-name-new {
    font-weight: 600;
    color: var(--text-primary);
}

.read-btn-new {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--gradient-primary);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
}

.read-btn-new:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Compact Side Articles */
.feature-sidebar-new {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.compact-article {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    transition: all 0.3s ease;
    display: flex;
    gap: 16px;
    padding: 20px;
}

.compact-article:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.compact-image {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
}

.compact-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.compact-content {
    flex: 1;
}

.compact-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.category-tag {
    background: rgba(73, 167, 92, 0.1);
    color: var(--redolence-green);
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
}

.time-tag {
    color: var(--text-muted);
    font-size: 12px;
}

.compact-title {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 8px;
}

.compact-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.compact-title a:hover {
    color: var(--redolence-green);
}

.compact-author {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-muted);
    font-size: 12px;
}

/* Masonry Section */
.masonry-revolution {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.masonry-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

.section-header-masonry {
    text-align: center;
    margin-bottom: 80px;
}

.masonry-title {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 900;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.title-accent {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.masonry-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.search-results-header-new {
    text-align: center;
    margin-bottom: 60px;
}

.results-title-new {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.search-term-new {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.results-count-new {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Pinterest-Style Grid */
.pinterest-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    align-items: start;
}

.pinterest-item.pinterest-tall {
    grid-row: span 2;
}

.pinterest-card {
    background: white;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    transition: all 0.5s ease;
    position: relative;
}

.pinterest-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-strong);
}

.pinterest-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.pinterest-tall .pinterest-image {
    height: 350px;
}

.pinterest-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.7s ease;
}

.pinterest-card:hover .pinterest-image img {
    transform: scale(1.1);
}

.image-overlay-pinterest {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pinterest-card:hover .image-overlay-pinterest {
    opacity: 1;
}

.pinterest-content {
    padding: 32px;
}

.pinterest-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.meta-category-new {
    background: rgba(73, 167, 92, 0.1);
    color: var(--redolence-green);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
}

.meta-date-new {
    color: var(--text-muted);
    font-size: 14px;
}

.pinterest-title {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 16px;
}

.pinterest-tall .pinterest-title {
    font-size: 1.75rem;
}

.pinterest-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.pinterest-title a:hover {
    color: var(--redolence-green);
}

.pinterest-excerpt {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 24px;
}

.pinterest-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.author-badge-new {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-muted);
    font-size: 14px;
}

.continue-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--redolence-green);
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
}

.continue-btn:hover {
    gap: 10px;
    color: var(--redolence-green-dark);
}

/* Revolutionary Pagination */
.pagination-revolution {
    padding: 80px 0;
    background: white;
}

.pagination-wrapper-new {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-bottom: 24px;
}

.pagination-btn-new {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--gradient-primary);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-soft);
}

.pagination-btn-new:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.pagination-numbers-new {
    display: flex;
    gap: 8px;
}

.pagination-number-new {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid transparent;
}

.pagination-number-new:hover,
.pagination-number-new.active {
    background: var(--gradient-primary);
    color: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-soft);
}

.pagination-info-new {
    color: var(--text-muted);
    font-size: 14px;
}

.separator {
    margin: 0 8px;
}

/* No Results State */
.no-results-revolution {
    padding: 120px 0;
    text-align: center;
}

.no-results-wrapper {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 40px;
}

.no-results-visual-new {
    margin-bottom: 40px;
}

.empty-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 48px;
    color: var(--redolence-green);
}

.no-results-title-new {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.no-results-text-new {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 40px;
}

.no-results-actions-new {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn-primary-new,
.action-btn-secondary-new {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 32px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 16px;
}

.action-btn-primary-new {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-soft);
}

.action-btn-primary-new:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.action-btn-secondary-new {
    background: white;
    color: var(--redolence-green);
    border: 2px solid var(--redolence-green);
}

.action-btn-secondary-new:hover {
    background: var(--redolence-green);
    color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .asymmetric-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .pinterest-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
    }

    .social-actions-floating {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin: 40px 0;
        transform: none;
        gap: 15px;
    }
    
    .social-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .btn-tooltip {
        display: none;
    }
    
    .back-navigation {
        margin: 60px 0;
        display: flex;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .hero-content {
        padding: 0 20px;
    }

    .spotlight-wrapper,
    .masonry-wrapper {
        padding: 0 20px;
    }

    .pinterest-grid {
        grid-template-columns: 1fr;
    }

    .pagination-controls {
        flex-wrap: wrap;
        gap: 12px;
    }

    .post-meta-pills {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .hero-content-floating {
        padding: 0 20px;
    }
    
    .article-content-wrapper {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .article-toc {
        position: relative;
        top: 0;
        margin-bottom: 30px;
        padding: 20px;
        max-height: none;
        overflow-y: visible;
    }
    
    .toc-header {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }
    
    .toc-body {
        font-size: 0.95rem;
    }
    
    .toc-placeholder {
        margin-bottom: 8px;
    }
    
    .article-container-new {
        padding: 60px 20px;
    }
    
    .article-body-new {
        font-size: 1.05rem;
        line-height: 1.7;
    }
    
    .article-body-new p {
        margin-bottom: 1.5rem;
    }
    
    .article-body-new h2 {
        font-size: 1.7rem;
        padding-left: 20px;
        margin: 2.5rem 0 1.2rem;
    }
    
    .article-body-new h3 {
        font-size: 1.4rem;
        margin: 2rem 0 1rem;
    }
    
    .article-body-new ul, .article-body-new ol {
        margin: 1.2rem 0 1.5rem 1.2rem;
    }
    
    .article-body-new li {
        margin-bottom: 0.6rem;
    }
    
    .article-body-new blockquote {
        padding: 0.8rem 0 0.8rem 1.5rem;
        margin: 1.5rem 0;
        font-size: 0.95rem;
    }
    
    .author-bio-section {
        padding: 25px 20px;
        flex-direction: column;
        text-align: center;
        margin-top: 50px;
        gap: 25px;
    }
    
    .post-hero-immersive {
        height: 70vh;
    }
    
    .post-title-hero {
        font-size: 2.2rem;
        margin-bottom: 15px;
    }
    
    .post-summary-hero {
        font-size: 1.1rem;
        max-width: 100%;
        margin-bottom: 25px;
    }
    
    .scroll-indicator {
        bottom: 15px;
    }
}
</style>

<script>
// Revolutionary Interactive Features
document.addEventListener('DOMContentLoaded', function() {

    // Search Input Enhancement
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    }
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
