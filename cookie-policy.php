<?php
/**
 * <PERSON>ie Policy Page
 * Redolence Medi Aesthetics - Professional Medical Aesthetics
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Cookie Policy - Redolence Medi Aesthetics";
$pageDescription = "Learn about how Redolence Medi Aesthetics uses cookies and tracking technologies to enhance your browsing experience.";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Modern Cookie Policy Styles - Redolence Medical Aesthetics */
:root {
    --redolence-green: #49a75c;
    --redolence-blue: #5894d2;
    --redolence-navy: #1a2332;
}

.cookie-page {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    min-height: 100vh;
}

.cookie-hero {
    background: linear-gradient(135deg, var(--redolence-green) 0%, var(--redolence-blue) 100%);
    position: relative;
    overflow: hidden;
}

.cookie-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.05) 0%, transparent 50%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.content-section {
    background: white;
    border-radius: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(73, 167, 92, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    overflow: hidden;
    position: relative;
}

.content-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--redolence-green), var(--redolence-blue));
}

.content-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.section-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--redolence-green), var(--redolence-blue));
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

.cookie-content h3 {
    color: var(--redolence-navy);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--redolence-green);
}

.cookie-content h4 {
    color: var(--redolence-green);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem 0;
}

.cookie-content ul {
    list-style: none;
    padding-left: 0;
    margin: 1rem 0;
}

.cookie-content li {
    padding: 0.5rem 0;
    padding-left: 2rem;
    position: relative;
}

.cookie-content li::before {
    content: '🍪';
    position: absolute;
    left: 0;
    font-size: 1.2rem;
}

.cookie-content p {
    margin: 1rem 0;
    line-height: 1.8;
    color: #374151;
}

.cookie-content strong {
    color: var(--redolence-navy);
    font-weight: 600;
}

.cookie-type-card {
    background: linear-gradient(135deg, var(--redolence-green)/5 0%, var(--redolence-blue)/5 100%);
    border: 2px solid var(--redolence-green)/20;
    border-radius: 1rem;
    padding: 1.5rem;
    margin: 1rem 0;
}

.contact-card {
    background: linear-gradient(135deg, var(--redolence-green)/5 0%, var(--redolence-blue)/5 100%);
    border: 2px solid var(--redolence-green)/20;
    border-radius: 1.5rem;
    padding: 2rem;
    text-align: center;
}

.contact-button {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, var(--redolence-green), var(--redolence-blue));
    color: white;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(73, 167, 92, 0.3);
}

.contact-button:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(73, 167, 92, 0.4);
    text-decoration: none;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-section {
        margin-bottom: 1.5rem;
    }
    
    .cookie-hero {
        padding: 3rem 0;
    }
}
</style>

<!-- Modern Cookie Policy Page -->
<div class="cookie-page">

<!-- Hero Section -->
<section class="cookie-hero py-20">
    <div class="max-w-7xl mx-auto px-6 text-center relative z-10">
        <!-- Cookie Badge -->
        <div class="inline-flex items-center bg-white/20 text-white px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-white/30 backdrop-blur-sm animate-on-scroll">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
            </svg>
            Cookie & Tracking Policy
        </div>

        <h1 class="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight animate-on-scroll">
            Cookie Policy
        </h1>
        
        <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed animate-on-scroll">
            Learn how Redolence Medi Aesthetics uses cookies and similar technologies to enhance your website experience
        </p>
        
        <div class="flex items-center justify-center space-x-8 text-white/80 animate-on-scroll">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Transparent Usage</span>
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>User Control</span>
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Clear Information</span>
            </div>
        </div>
    </div>
</section>

<!-- Content Section -->
<section class="py-16">
    <div class="max-w-6xl mx-auto px-6">
        
        <!-- Last Updated -->
        <div class="text-center mb-12">
            <p class="text-gray-600">Last updated: <span class="font-semibold text-redolence-green"><?= date('F d, Y') ?></span></p>
        </div>

        <!-- What Are Cookies -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">What Are Cookies?</h2>

                <div class="cookie-content">
                    <p>
                        Cookies are small text files that are stored on your device when you visit our website. They help us provide you with a better browsing experience by remembering your preferences and improving website functionality.
                    </p>
                    <p>
                        At <strong>Redolence Medi Aesthetics</strong>, we use cookies responsibly and transparently to enhance your experience while respecting your privacy.
                    </p>
                    <p>
                        This Cookie Policy explains what cookies we use, why we use them, and how you can control them.
                    </p>
                </div>
            </div>
        </div>

        <!-- Types of Cookies -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Types of Cookies We Use</h2>

                <div class="cookie-content">
                    <div class="cookie-type-card">
                        <h3>🔧 Essential Cookies</h3>
                        <p>
                            These cookies are necessary for the website to function properly. They enable basic features like page navigation, access to secure areas, and form submissions.
                        </p>
                        <ul>
                            <li>Session management and user authentication</li>
                            <li>Shopping cart functionality</li>
                            <li>Security and fraud prevention</li>
                            <li>Website accessibility features</li>
                        </ul>
                        <p><strong>Duration:</strong> Session cookies (deleted when you close your browser)</p>
                    </div>

                    <div class="cookie-type-card">
                        <h3>📊 Analytics Cookies</h3>
                        <p>
                            These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.
                        </p>
                        <ul>
                            <li>Page views and user behavior analysis</li>
                            <li>Website performance monitoring</li>
                            <li>Traffic source identification</li>
                            <li>Popular content and features tracking</li>
                        </ul>
                        <p><strong>Duration:</strong> Up to 2 years</p>
                    </div>

                    <div class="cookie-type-card">
                        <h3>⚙️ Functional Cookies</h3>
                        <p>
                            These cookies enable enhanced functionality and personalization, such as remembering your preferences and settings.
                        </p>
                        <ul>
                            <li>Language and region preferences</li>
                            <li>Font size and accessibility settings</li>
                            <li>Previously viewed services or content</li>
                            <li>Form data retention for convenience</li>
                        </ul>
                        <p><strong>Duration:</strong> Up to 1 year</p>
                    </div>

                    <div class="cookie-type-card">
                        <h3>🎯 Marketing Cookies</h3>
                        <p>
                            These cookies are used to deliver relevant advertisements and track the effectiveness of our marketing campaigns.
                        </p>
                        <ul>
                            <li>Personalized content and advertisements</li>
                            <li>Social media integration</li>
                            <li>Marketing campaign effectiveness</li>
                            <li>Retargeting and remarketing</li>
                        </ul>
                        <p><strong>Duration:</strong> Up to 1 year</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Third-Party Cookies -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Third-Party Services</h2>

                <div class="cookie-content">
                    <p>
                        We may use third-party services that set their own cookies. These services help us provide better functionality and analyze website performance.
                    </p>

                    <h3>Services We Use</h3>
                    <ul>
                        <li><strong>Google Analytics:</strong> Website traffic and user behavior analysis</li>
                        <li><strong>Google Maps:</strong> Location services and directions</li>
                        <li><strong>Social Media Platforms:</strong> Social sharing and integration</li>
                        <li><strong>Payment Processors:</strong> Secure payment processing</li>
                        <li><strong>Booking Systems:</strong> Appointment scheduling functionality</li>
                    </ul>

                    <p>
                        These third-party services have their own privacy policies and cookie practices. We recommend reviewing their policies to understand how they use cookies.
                    </p>
                </div>
            </div>
        </div>

        <!-- Managing Cookies -->
        <div class="content-section animate-on-scroll">
            <div class="p-8">
                <div class="section-icon">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-redolence-navy mb-6">Managing Your Cookie Preferences</h2>

                <div class="cookie-content">
                    <h3>Browser Settings</h3>
                    <p>
                        You can control cookies through your browser settings. Most browsers allow you to:
                    </p>
                    <ul>
                        <li>View and delete existing cookies</li>
                        <li>Block cookies from specific websites</li>
                        <li>Block third-party cookies</li>
                        <li>Delete all cookies when you close your browser</li>
                        <li>Receive notifications when cookies are set</li>
                    </ul>

                    <h3>Browser-Specific Instructions</h3>
                    <ul>
                        <li><strong>Chrome:</strong> Settings > Privacy and Security > Cookies and other site data</li>
                        <li><strong>Firefox:</strong> Options > Privacy & Security > Cookies and Site Data</li>
                        <li><strong>Safari:</strong> Preferences > Privacy > Manage Website Data</li>
                        <li><strong>Edge:</strong> Settings > Cookies and site permissions</li>
                    </ul>

                    <h3>Important Note</h3>
                    <p>
                        Please note that disabling certain cookies may affect the functionality of our website and your user experience. Essential cookies cannot be disabled as they are necessary for the website to function properly.
                    </p>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="contact-card animate-on-scroll">
            <div class="section-icon mx-auto">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
            </div>

            <h2 class="text-3xl font-bold text-redolence-navy mb-4">Questions About Cookies?</h2>
            <p class="text-gray-600 mb-6 text-lg">
                If you have any questions about our use of cookies or this Cookie Policy, please don't hesitate to contact us.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="text-center">
                    <h4 class="font-semibold text-redolence-navy mb-2">Privacy Team</h4>
                    <p class="text-gray-600"><EMAIL></p>
                </div>
                <div class="text-center">
                    <h4 class="font-semibold text-redolence-navy mb-2">Phone</h4>
                    <p class="text-gray-600">+255 123 456 789</p>
                </div>
            </div>

            <a href="contact.php" class="contact-button">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Contact Us
            </a>
        </div>
    </div>
</section>

</div>
<!-- End Cookie Policy Page -->

<script>
// Handle scroll animations
function handleScrollAnimations() {
    const elements = document.querySelectorAll('.animate-on-scroll:not(.visible)');

    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;

        if (elementTop < window.innerHeight - elementVisible) {
            element.classList.add('visible');
        }
    });
}

// Listen for scroll events
window.addEventListener('scroll', handleScrollAnimations);

// Check for elements in view on page load
document.addEventListener('DOMContentLoaded', () => {
    handleScrollAnimations();
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
