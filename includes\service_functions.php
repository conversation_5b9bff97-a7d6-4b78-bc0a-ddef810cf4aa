<?php
/**
 * Service Management Functions
 * Flix Salonce - PHP Version
 */

// Include service variation functions
require_once __DIR__ . '/service_variation_functions.php';

/**
 * Create a new service
 */
function createService($data) {
    global $database;
    
    try {
        // Validate required fields - only name is required for medical treatments
        if (empty($data['name'])) {
            return ['success' => false, 'error' => 'Treatment name is required'];
        }
        
        // Handle image (either upload or URL)
        $imagePath = null;

        // Check if image URL is provided
        if (!empty($data['image_url'])) {
            // Validate URL
            if (filter_var($data['image_url'], FILTER_VALIDATE_URL)) {
                $imagePath = $data['image_url'];
            } else {
                return ['success' => false, 'error' => 'Invalid image URL provided'];
            }
        }
        // Check if file upload is provided
        elseif (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = handleImageUpload($_FILES['image'], 'services');
            if ($uploadResult['success']) {
                $imagePath = $uploadResult['path'];
            } else {
                return ['success' => false, 'error' => $uploadResult['error']];
            }
        }
        
        $serviceId = generateUUID();
        
        // Handle subcategory_id - convert empty string to null
        $subcategoryId = !empty($data['subcategory_id']) ? $data['subcategory_id'] : null;

        // Get category_id from category name for foreign key relationship
        $categoryId = null;
        if (!empty($data['category'])) {
            $categoryRecord = $database->fetch("SELECT id FROM service_categories WHERE name = ?", [$data['category']]);
            if ($categoryRecord) {
                $categoryId = $categoryRecord['id'];
            }
        }

        // Handle optional price and duration for medical treatments
        $price = !empty($data['price']) ? floatval($data['price']) : null;
        $duration = !empty($data['duration']) ? intval($data['duration']) : null;

        $database->query(
            "INSERT INTO services (id, name, description, price, duration, category, category_id, subcategory_id, image, is_active, session_frequency, technology_used, featured, popular, new_treatment, sort_order, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $serviceId,
                cleanText($data['name']), // Use cleanText to preserve & characters
                $data['description'] ?? '', // Keep HTML for rich descriptions
                $price,
                $duration,
                cleanText($data['category'] ?? null),
                $categoryId,
                $subcategoryId,
                $imagePath,
                isset($data['is_active']) ? 1 : 0,
                cleanText($data['session_frequency'] ?? ''), // Use cleanText for medical fields
                cleanText($data['technology_used'] ?? ''),
                isset($data['featured']) ? 1 : 0,
                isset($data['popular']) ? 1 : 0,
                isset($data['new_treatment']) ? 1 : 0,
                intval($data['sort_order'] ?? 0)
            ]
        );
        
        return ['success' => true, 'id' => $serviceId];
        
    } catch (Exception $e) {
        error_log("Service creation error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create service'];
    }
}

/**
 * Update an existing service
 */
function updateService($serviceId, $data) {
    global $database;
    
    try {
        // Validate required fields - only name is required for medical treatments
        if (empty($data['name'])) {
            return ['success' => false, 'error' => 'Treatment name is required'];
        }
        
        // Get current service data
        $currentService = $database->fetch("SELECT * FROM services WHERE id = ?", [$serviceId]);
        if (!$currentService) {
            return ['success' => false, 'error' => 'Service not found'];
        }
        
        // Handle image (either upload or URL)
        $imagePath = $currentService['image'];

        // Check if image URL is provided
        if (!empty($data['image_url'])) {
            // Validate URL
            if (filter_var($data['image_url'], FILTER_VALIDATE_URL)) {
                // Delete old uploaded image if it exists and is not a URL
                if ($imagePath && !filter_var($imagePath, FILTER_VALIDATE_URL) && file_exists(UPLOAD_PATH . $imagePath)) {
                    unlink(UPLOAD_PATH . $imagePath);
                }
                $imagePath = $data['image_url'];
            } else {
                return ['success' => false, 'error' => 'Invalid image URL provided'];
            }
        }
        // Check if file upload is provided
        elseif (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $uploadResult = handleImageUpload($_FILES['image'], 'services');
            if ($uploadResult['success']) {
                // Delete old uploaded image if it exists and is not a URL
                if ($imagePath && !filter_var($imagePath, FILTER_VALIDATE_URL) && file_exists(UPLOAD_PATH . $imagePath)) {
                    unlink(UPLOAD_PATH . $imagePath);
                }
                $imagePath = $uploadResult['path'];
            } else {
                return ['success' => false, 'error' => $uploadResult['error']];
            }
        }
        
        // Handle subcategory_id - convert empty string to null
        $subcategoryId = !empty($data['subcategory_id']) ? $data['subcategory_id'] : null;

        // Get category_id from category name for foreign key relationship
        $categoryId = null;
        if (!empty($data['category'])) {
            $categoryRecord = $database->fetch("SELECT id FROM service_categories WHERE name = ?", [$data['category']]);
            if ($categoryRecord) {
                $categoryId = $categoryRecord['id'];
            }
        }

        // Handle optional price and duration for medical treatments
        $price = !empty($data['price']) ? floatval($data['price']) : null;
        $duration = !empty($data['duration']) ? intval($data['duration']) : null;

        $database->query(
            "UPDATE services SET name = ?, description = ?, price = ?, duration = ?, category = ?, category_id = ?, subcategory_id = ?, image = ?, is_active = ?, session_frequency = ?, technology_used = ?, featured = ?, popular = ?, new_treatment = ?, sort_order = ?, updated_at = NOW()
             WHERE id = ?",
            [
                cleanText($data['name']), // Use cleanText to preserve & characters
                $data['description'] ?? '', // Keep HTML for rich descriptions
                $price,
                $duration,
                cleanText($data['category'] ?? null),
                $categoryId,
                $subcategoryId,
                $imagePath,
                isset($data['is_active']) ? 1 : 0,
                cleanText($data['session_frequency'] ?? ''), // Use cleanText for medical fields
                cleanText($data['technology_used'] ?? ''),
                isset($data['featured']) ? 1 : 0,
                isset($data['popular']) ? 1 : 0,
                isset($data['new_treatment']) ? 1 : 0,
                intval($data['sort_order'] ?? 0),
                $serviceId
            ]
        );
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Service update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update service'];
    }
}

/**
 * Delete a service
 */
function deleteService($serviceId) {
    global $database;
    
    try {
        // Check if service exists
        $service = $database->fetch("SELECT * FROM services WHERE id = ?", [$serviceId]);
        if (!$service) {
            return ['success' => false, 'error' => 'Service not found'];
        }

        // Check if service has active bookings
        $activeBookings = $database->fetchAll(
            "SELECT b.id, b.date, b.start_time, b.status, u.name as customer_name
             FROM bookings b
             JOIN users u ON b.user_id = u.id
             WHERE b.service_id = ? AND b.status IN ('PENDING', 'CONFIRMED')",
            [$serviceId]
        );

        if (count($activeBookings) > 0) {
            $bookingDetails = [];
            foreach ($activeBookings as $booking) {
                $bookingDetails[] = "• {$booking['customer_name']} on {$booking['date']} at {$booking['start_time']} ({$booking['status']})";
            }
            return [
                'success' => false,
                'error' => 'Cannot delete service with active bookings',
                'details' => 'The following bookings must be cancelled or completed first:',
                'bookings' => $bookingDetails
            ];
        }

        // Check if service has completed bookings (offer alternative)
        $completedBookings = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings WHERE service_id = ? AND status = 'COMPLETED'",
            [$serviceId]
        )['count'];

        if ($completedBookings > 0) {
            return [
                'success' => false,
                'error' => 'Service has booking history',
                'details' => "This service has {$completedBookings} completed booking(s). Instead of deleting, consider:",
                'suggestions' => [
                    'Mark the service as inactive to hide it from new bookings',
                    'Archive the service for historical records',
                    'Contact administrator for data cleanup'
                ]
            ];
        }

        // Check if service is part of any packages
        $packageCount = $database->fetch(
            "SELECT COUNT(*) as count FROM package_services WHERE service_id = ?",
            [$serviceId]
        )['count'];

        if ($packageCount > 0) {
            // Get package names for better error message
            $packages = $database->fetchAll(
                "SELECT p.name FROM packages p
                 JOIN package_services ps ON p.id = ps.package_id
                 WHERE ps.service_id = ?",
                [$serviceId]
            );
            $packageNames = array_column($packages, 'name');
            return ['success' => false, 'error' => 'Cannot delete service that is part of packages: ' . implode(', ', $packageNames)];
        }

        // Delete the service
        $result = $database->query("DELETE FROM services WHERE id = ?", [$serviceId]);

        if ($result === false) {
            return ['success' => false, 'error' => 'Failed to delete service from database'];
        }

        // Delete associated image (only if it's an uploaded file, not a URL)
        if ($service['image'] && !filter_var($service['image'], FILTER_VALIDATE_URL) && file_exists(UPLOAD_PATH . $service['image'])) {
            unlink(UPLOAD_PATH . $service['image']);
        }

        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Service deletion error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => 'Database error occurred',
            'details' => 'An unexpected error occurred while deleting the service. Please try again or contact support.',
            'technical_error' => $e->getMessage()
        ];
    }
}

/**
 * Get service by ID
 */
function getServiceById($serviceId) {
    global $database;
    
    return $database->fetch("SELECT * FROM services WHERE id = ?", [$serviceId]);
}

/**
 * Get all active services
 */
function getActiveServices() {
    global $database;
    
    return $database->fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name");
}

/**
 * Get services by category
 */
function getServicesByCategory($category) {
    global $database;
    
    return $database->fetchAll(
        "SELECT * FROM services WHERE category = ? AND is_active = 1 ORDER BY name",
        [$category]
    );
}

/**
 * Handle image upload for services
 */
function handleImageUpload($file, $folder = 'services') {
    try {
        // Validate file
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            return ['success' => false, 'error' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'];
        }
        
        // Check file size (max 5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            return ['success' => false, 'error' => 'File size too large. Maximum 5MB allowed.'];
        }
        
        // Create upload directory if it doesn't exist
        $uploadDir = UPLOAD_PATH . $folder . '/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = generateUUID() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            // Resize image if needed
            resizeImage($filepath, 800, 600);
            
            return ['success' => true, 'path' => $folder . '/' . $filename];
        } else {
            return ['success' => false, 'error' => 'Failed to upload file'];
        }
        
    } catch (Exception $e) {
        error_log("Image upload error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Upload failed'];
    }
}

/**
 * Resize image to fit within specified dimensions
 * Returns true if successful or if GD extension is not available
 */
function resizeImage($filepath, $maxWidth, $maxHeight) {
    try {
        // Check if GD extension is available
        if (!extension_loaded('gd')) {
            error_log("GD extension not available - skipping image resize for: $filepath");
            return true; // Return true to continue without resizing
        }

        // Check if required GD functions exist
        if (!function_exists('imagecreatefromjpeg') || !function_exists('imagecreatetruecolor')) {
            error_log("Required GD functions not available - skipping image resize for: $filepath");
            return true; // Return true to continue without resizing
        }

        $imageInfo = getimagesize($filepath);
        if (!$imageInfo) {
            error_log("Could not get image info for: $filepath");
            return true; // Return true to continue without resizing
        }

        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $type = $imageInfo[2];

        // Check if resize is needed
        if ($width <= $maxWidth && $height <= $maxHeight) {
            return true;
        }

        // Calculate new dimensions
        $ratio = min($maxWidth / $width, $maxHeight / $height);
        $newWidth = round($width * $ratio);
        $newHeight = round($height * $ratio);

        // Create image resource based on type
        $source = null;
        switch ($type) {
            case IMAGETYPE_JPEG:
                if (function_exists('imagecreatefromjpeg')) {
                    $source = imagecreatefromjpeg($filepath);
                }
                break;
            case IMAGETYPE_PNG:
                if (function_exists('imagecreatefrompng')) {
                    $source = imagecreatefrompng($filepath);
                }
                break;
            case IMAGETYPE_GIF:
                if (function_exists('imagecreatefromgif')) {
                    $source = imagecreatefromgif($filepath);
                }
                break;
            case IMAGETYPE_WEBP:
                if (function_exists('imagecreatefromwebp')) {
                    $source = imagecreatefromwebp($filepath);
                }
                break;
            default:
                error_log("Unsupported image type for resize: $type");
                return true; // Return true to continue without resizing
        }

        if (!$source) {
            error_log("Could not create image resource from: $filepath");
            return true; // Return true to continue without resizing
        }
        
        // Create new image
        $destination = imagecreatetruecolor($newWidth, $newHeight);
        if (!$destination) {
            imagedestroy($source);
            error_log("Could not create destination image for resize: $filepath");
            return true; // Return true to continue without resizing
        }

        // Preserve transparency for PNG and GIF
        if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
            if (function_exists('imagealphablending') && function_exists('imagesavealpha')) {
                imagealphablending($destination, false);
                imagesavealpha($destination, true);
                $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
                imagefilledrectangle($destination, 0, 0, $newWidth, $newHeight, $transparent);
            }
        }

        // Resize image
        $resizeResult = imagecopyresampled($destination, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
        if (!$resizeResult) {
            imagedestroy($source);
            imagedestroy($destination);
            error_log("Image resize operation failed for: $filepath");
            return true; // Return true to continue without resizing
        }

        // Save resized image
        $saveResult = false;
        switch ($type) {
            case IMAGETYPE_JPEG:
                if (function_exists('imagejpeg')) {
                    $saveResult = imagejpeg($destination, $filepath, 85);
                }
                break;
            case IMAGETYPE_PNG:
                if (function_exists('imagepng')) {
                    $saveResult = imagepng($destination, $filepath, 8);
                }
                break;
            case IMAGETYPE_GIF:
                if (function_exists('imagegif')) {
                    $saveResult = imagegif($destination, $filepath);
                }
                break;
            case IMAGETYPE_WEBP:
                if (function_exists('imagewebp')) {
                    $saveResult = imagewebp($destination, $filepath, 85);
                }
                break;
        }

        // Clean up
        imagedestroy($source);
        imagedestroy($destination);

        if (!$saveResult) {
            error_log("Could not save resized image: $filepath");
            return true; // Return true to continue - original image is still there
        }

        return true;

    } catch (Exception $e) {
        error_log("Image resize error: " . $e->getMessage());
        return true; // Return true to continue without resizing
    } catch (Error $e) {
        error_log("Image resize fatal error: " . $e->getMessage());
        return true; // Return true to continue without resizing
    }
}

/**
 * Get service statistics
 */
function getServiceStats() {
    global $database;
    
    $stats = [];
    
    // Total services
    $stats['total'] = $database->fetch("SELECT COUNT(*) as count FROM services")['count'];
    
    // Active services
    $stats['active'] = $database->fetch("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'];
    
    // Services by category
    $stats['by_category'] = $database->fetchAll(
        "SELECT category, COUNT(*) as count FROM services WHERE category IS NOT NULL GROUP BY category ORDER BY count DESC"
    );
    
    // Most booked services
    $stats['most_booked'] = $database->fetchAll(
        "SELECT s.name, COUNT(b.id) as booking_count 
         FROM services s 
         LEFT JOIN bookings b ON s.id = b.service_id 
         GROUP BY s.id, s.name 
         ORDER BY booking_count DESC 
         LIMIT 5"
    );
    
    // Average service price
    $stats['avg_price'] = $database->fetch("SELECT AVG(price) as avg_price FROM services WHERE is_active = 1")['avg_price'];
    
    return $stats;
}
?>
