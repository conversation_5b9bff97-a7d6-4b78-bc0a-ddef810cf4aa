<?php
/**
 * FAQ Page - Medical Aesthetics Redesign
 * Redolence Medi Aesthetics - Advanced Medical Beauty Center
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "Frequently Asked Questions - Redolence Medi Aesthetics";
$pageDescription = "Find answers to common questions about our advanced medical aesthetics treatments, procedures, and policies at Redolence Medi Aesthetics.";

include __DIR__ . '/includes/header.php';
?>

<!-- AOS (Animate On Scroll) Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<?php
// Check if faq_services table exists (migration might not be applied yet)
$tableExists = false;
try {
    $database->query("SELECT 1 FROM faq_services LIMIT 1");
    $tableExists = true;
} catch (Exception $e) {
    // Table doesn't exist, use fallback queries
}

// Get FAQ data from database with or without linked services
if ($tableExists) {
    $faqsQuery = "
        SELECT f.*,
               GROUP_CONCAT(
                   CONCAT(s.id, ':', s.name, ':', COALESCE(s.price, 0), ':', COALESCE(s.duration, 0))
                   SEPARATOR '||'
               ) as linked_services_data
        FROM faqs f
        LEFT JOIN faq_services fs ON f.id = fs.faq_id
        LEFT JOIN services s ON fs.service_id = s.id AND s.is_active = 1
        WHERE f.is_active = 1
        GROUP BY f.id
        ORDER BY f.category, f.display_order, f.id
    ";
} else {
    // Fallback query without service linking
    $faqsQuery = "
        SELECT *, '' as linked_services_data
        FROM faqs
        WHERE is_active = 1
        ORDER BY category, display_order, id
    ";
}

$allFaqs = $database->fetchAll($faqsQuery);

// Group FAQs by category
$faqsByCategory = [];
foreach ($allFaqs as $faq) {
    // Parse linked services data
    $faq['linked_services'] = [];
    if (!empty($faq['linked_services_data'])) {
        $servicesData = explode('||', $faq['linked_services_data']);
        foreach ($servicesData as $serviceData) {
            $parts = explode(':', $serviceData);
            if (count($parts) >= 4) {
                $faq['linked_services'][] = [
                    'id' => $parts[0],
                    'name' => $parts[1],
                    'price' => (int)$parts[2],
                    'duration' => (int)$parts[3]
                ];
            }
        }
    }

    $faqsByCategory[$faq['category']][] = $faq;
}

?>

<!-- Revolutionary Medical Aesthetics CSS -->
<style>
/* Advanced Medical Aesthetics Design System */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --accent-gold: #f4d03f;
    --deep-navy: #1a2332;
    --soft-gray: #f8fafc;
    --medical-white: #ffffff;
    --shadow-primary: rgba(73, 167, 92, 0.15);
    --shadow-blue: rgba(88, 148, 210, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
}

/* Revolutionary Animation Framework */
@keyframes morphingMedicalBg {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes floatingMedical {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(5deg); }
    66% { transform: translateY(-10px) rotate(-3deg); }
}

@keyframes pulseGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(73, 167, 92, 0.3); }
    50% { box-shadow: 0 0 40px rgba(88, 148, 210, 0.5); }
}

@keyframes slideInFromLeft {
    0% { opacity: 0; transform: translateX(-50px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
    0% { opacity: 0; transform: scale(0.9); }
    100% { opacity: 1; transform: scale(1); }
}

/* Medical FAQ Card System */
.medical-faq-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.medical-faq-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    transition: left 0.6s ease;
}

.medical-faq-card:hover::before {
    left: 100%;
}

.medical-faq-card:hover {
    transform: translateY(-5px);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 20px 40px var(--shadow-primary);
}

.faq-toggle {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.faq-toggle:hover .faq-question {
    color: var(--primary-green);
}

.faq-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.23, 1, 0.32, 1), padding 0.4s ease;
    background: rgba(73, 167, 92, 0.02);
    border-top: 1px solid rgba(73, 167, 92, 0.1);
}

.faq-content.show {
    max-height: 800px; /* Increased from 300px to accommodate longer content */
    padding: 2rem;
}

.faq-icon {
    transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    filter: drop-shadow(0 2px 4px rgba(73, 167, 92, 0.2));
}

.faq-icon.rotate {
    transform: rotate(180deg);
}

.category-header {
    position: relative;
    text-align: center;
    margin-bottom: 3rem;
}

.category-header::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.search-box {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(73, 167, 92, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 3rem;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: var(--primary-green);
    box-shadow: 0 0 30px rgba(73, 167, 92, 0.2);
}

.search-input {
    background: transparent;
    border: none;
    outline: none;
    color: var(--deep-navy);
    width: 100%;
    font-size: 1.1rem;
    font-weight: 500;
}

.search-input::placeholder {
    color: rgba(26, 35, 50, 0.6);
}

.quick-links {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 3rem;
    justify-content: center;
}

.quick-link {
    background: rgba(73, 167, 92, 0.1);
    border: 2px solid rgba(73, 167, 92, 0.2);
    color: var(--primary-green);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 0.95rem;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.quick-link:hover {
    background: rgba(73, 167, 92, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(73, 167, 92, 0.2);
}

/* Medical Category Icons */
.category-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-soft);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.category-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 10px 30px var(--shadow-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .medical-faq-card {
        margin: 0;
        border-radius: 16px;
    }

    .faq-content.show {
        max-height: none; /* Remove height restriction on mobile */
        padding: 1.5rem;
        overflow: visible; /* Ensure all content is visible */
    }

    .faq-toggle {
        padding: 1.5rem; /* Reduce padding for better mobile experience */
    }

    .faq-question {
        font-size: 1.125rem; /* Slightly smaller text on mobile */
        line-height: 1.4;
    }

    .quick-links {
        gap: 0.5rem;
    }

    .quick-link {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .search-box {
        padding: 1rem;
        margin-bottom: 2rem;
    }

    /* Ensure FAQ content is fully scrollable on mobile */
    .faq-content p {
        margin-bottom: 1rem;
        line-height: 1.6;
    }

    /* Better spacing for related services on mobile */
    .faq-content .grid-cols-1.md\\:grid-cols-2 {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    /* Ensure mobile FAQ cards don't get cut off */
    .medical-faq-card {
        margin-bottom: 1rem;
    }

    /* Mobile-specific FAQ content styling */
    .faq-content.show {
        animation: expandMobile 0.4s ease-out;
    }
}

/* Mobile FAQ expansion animation */
@keyframes expandMobile {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: none;
        opacity: 1;
    }
}

/* Medical Professional Styling */
.medical-stats {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.medical-stats:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Contact Section Enhancement */
.contact-enhancement {
    background: linear-gradient(135deg, var(--gradient-soft), rgba(255, 255, 255, 0.1));
    border-radius: 30px;
    padding: 3rem;
    position: relative;
    overflow: hidden;
}

.contact-enhancement::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(73, 167, 92, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.contact-enhancement::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(88, 148, 210, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

/* Gradient Animation for CTA */
.bg-size-200 {
    background-size: 200% 200%;
}

@keyframes gradient-x {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.animate-gradient-x {
    animation: gradient-x 6s ease infinite;
}

/* Ultra Modern Hero Section Animations */
@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes gradient-shift-reverse {
    0%, 100% { background-position: 100% 50%; }
    50% { background-position: 0% 50%; }
}

@keyframes float-slow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float-medium {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-180deg); }
}

@keyframes float-fast {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-25px) rotate(360deg); }
}

@keyframes particle-float {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) translateX(10px) rotate(90deg) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-40px) translateX(-5px) rotate(180deg) scale(0.8);
        opacity: 0.4;
    }
    75% {
        transform: translateY(-15px) translateX(-15px) rotate(270deg) scale(1.1);
        opacity: 0.9;
    }
}

@keyframes text-shimmer {
    0%, 100% { background-position: -200% center; }
    50% { background-position: 200% center; }
}

@keyframes gradient-text {
    0%, 100% {
        background-position: 0% 50%;
        filter: hue-rotate(0deg);
    }
    50% {
        background-position: 100% 50%;
        filter: hue-rotate(20deg);
    }
}

@keyframes grid-pulse {
    0%, 100% { opacity: 0.02; }
    50% { opacity: 0.05; }
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    0%, 50% { border-color: rgba(73, 167, 92, 0.8); }
    51%, 100% { border-color: transparent; }
}

/* Ultra Modern Hero Section Styles */
.animate-gradient-shift {
    background-size: 400% 400%;
    animation: gradient-shift 8s ease-in-out infinite;
}

.animate-gradient-shift-reverse {
    background-size: 400% 400%;
    animation: gradient-shift-reverse 10s ease-in-out infinite;
}

.animate-float-slow {
    animation: float-slow 20s ease-in-out infinite;
}

.animate-float-medium {
    animation: float-medium 15s ease-in-out infinite;
}

.animate-float-fast {
    animation: float-fast 12s ease-in-out infinite;
}

.animate-particle-float {
    animation: particle-float 8s ease-in-out infinite;
}

.animate-text-shimmer {
    background-size: 400% 100%;
    animation: text-shimmer 3s ease-in-out infinite;
}

.animate-gradient-text {
    background-size: 300% 300%;
    animation: gradient-text 4s ease-in-out infinite;
}

.typing-text {
    overflow: hidden;
    border-right: 2px solid transparent;
    animation: typing 3s steps(30, end), blink 1s infinite;
    display: inline-block;
}

/* Contact Cards */
.contact-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 2rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
}

.contact-card:hover::before {
    left: 100%;
}

.contact-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.contact-card-inner {
    position: relative;
    z-index: 2;
    text-align: center;
}

.contact-icon-container {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.contact-card:hover .contact-icon-container {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.hero-cta-primary {
    background: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    border: none;
    border-radius: 16px;
    padding: 1rem 2rem;
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
    display: inline-flex;
    align-items: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(73, 167, 92, 0.3);
}

.hero-cta-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.4);
    filter: brightness(1.1);
}

.hero-cta-primary:active {
    transform: translateY(-1px) scale(1.02);
}

/* Mobile Responsive Fixes */
@media (max-width: 768px) {
    .text-6xl {
        font-size: 2.5rem !important;
        line-height: 1.1 !important;
    }

    .text-8xl {
        font-size: 3rem !important;
        line-height: 1.1 !important;
    }

    .text-2xl {
        font-size: 1.25rem !important;
        line-height: 1.4 !important;
    }

    .text-3xl {
        font-size: 1.5rem !important;
        line-height: 1.3 !important;
    }

    .contact-card {
        padding: 1.5rem !important;
        margin-bottom: 1rem !important;
    }

    .contact-icon-container {
        width: 3rem !important;
        height: 3rem !important;
        margin-bottom: 0.75rem !important;
    }

    .hero-cta-primary {
        padding: 0.875rem 1.5rem !important;
        font-size: 1rem !important;
        width: 100% !important;
        justify-content: center !important;
        margin-bottom: 1rem !important;
    }

    .min-h-screen {
        padding: 2rem 0 !important;
        min-height: auto !important;
    }

    .max-w-7xl {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}
</style>

<!-- Ultra Modern Hero Section - FAQ Page -->
<section class="relative min-h-screen md:min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 py-8 md:py-0">
    <!-- Advanced Background Effects -->
    <div class="absolute inset-0">
        <!-- Dynamic Gradient Mesh -->
        <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/20 via-transparent to-redolence-blue/20 animate-gradient-shift"></div>
        <div class="absolute inset-0 bg-gradient-to-tl from-purple-500/10 via-transparent to-emerald-500/10 animate-gradient-shift-reverse"></div>

        <!-- Floating Orbs -->
        <div class="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-redolence-green/30 to-emerald-400/30 rounded-full blur-3xl animate-float-slow"></div>
        <div class="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-redolence-blue/30 to-cyan-400/30 rounded-full blur-3xl animate-float-medium"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-float-fast"></div>

        <!-- Animated Particles -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="particle absolute top-1/4 left-1/4 w-2 h-2 bg-redolence-green/60 rounded-full animate-particle-float"></div>
            <div class="particle absolute top-3/4 right-1/4 w-3 h-3 bg-redolence-blue/60 rounded-full animate-particle-float" style="animation-delay: 1s;"></div>
            <div class="particle absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-purple-400/60 rounded-full animate-particle-float" style="animation-delay: 2s;"></div>
            <div class="particle absolute bottom-1/4 left-1/2 w-2.5 h-2.5 bg-emerald-400/60 rounded-full animate-particle-float" style="animation-delay: 3s;"></div>
            <div class="particle absolute top-1/3 right-1/3 w-2 h-2 bg-cyan-400/60 rounded-full animate-particle-float" style="animation-delay: 4s;"></div>
        </div>

        <!-- Grid Pattern -->
        <div class="absolute inset-0 opacity-[0.02]" style="background-image: linear-gradient(rgba(73, 167, 92, 0.5) 1px, transparent 1px), linear-gradient(90deg, rgba(73, 167, 92, 0.5) 1px, transparent 1px); background-size: 80px 80px; animation: grid-pulse 4s ease-in-out infinite;"></div>
    </div>

    <!-- Main Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 md:px-6 text-center">
        <!-- Premium Badge -->
        <div class="mb-8 md:mb-12" data-aos="fade-up" data-aos-duration="1000">
            <div class="inline-flex items-center bg-white/10 backdrop-blur-md px-4 md:px-8 py-2 md:py-4 rounded-full border border-white/20 shadow-2xl hover:scale-105 transition-all duration-500">
                <div class="w-2 md:w-3 h-2 md:h-3 bg-redolence-green rounded-full mr-2 md:mr-3 animate-pulse"></div>
                <span class="font-bold text-white tracking-wider text-xs md:text-sm uppercase">Frequently Asked Questions</span>
                <div class="w-2 md:w-3 h-2 md:h-3 bg-redolence-blue rounded-full ml-2 md:ml-3 animate-pulse" style="animation-delay: 0.5s;"></div>
            </div>
        </div>

        <!-- Dynamic Title -->
        <div class="mb-8 md:mb-12" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="200">
            <h1 class="text-4xl md:text-6xl lg:text-8xl font-black mb-4 md:mb-6 leading-tight">
                <span class="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent animate-text-shimmer">
                    Get
                </span>
                <br>
                <span class="bg-gradient-to-r from-redolence-green via-emerald-400 to-redolence-blue bg-clip-text text-transparent animate-gradient-text">
                    Answers
                </span>
            </h1>

            <div class="relative">
                <p class="text-lg md:text-2xl lg:text-3xl text-gray-300 font-light leading-relaxed mb-6 md:mb-8">
                    Expert <span class="text-redolence-green font-semibold">Medical Guidance</span> & Support
                    <br class="hidden md:block">
                    <span class="typing-text">From Board-Certified Specialists</span>
                </p>

                <!-- Decorative Elements - Hidden on mobile -->
                <div class="hidden md:block absolute -top-4 -right-8 w-8 h-8 bg-gradient-to-r from-redolence-green/30 to-emerald-400/30 rounded-full blur-sm animate-pulse"></div>
                <div class="hidden md:block absolute -bottom-4 -left-8 w-6 h-6 bg-gradient-to-r from-redolence-blue/30 to-cyan-400/30 rounded-full blur-sm animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <!-- Enhanced FAQ Highlights -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto mb-12 md:mb-16" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="400">
            <!-- Expert Answers -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-redolence-green to-emerald-500">
                        <svg class="w-6 md:w-8 h-6 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg md:text-xl font-bold text-white mb-2 group-hover:text-redolence-green transition-colors duration-300">
                        Expert Answers
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Comprehensive responses from board-certified specialists
                    </p>
                    <div class="text-redolence-green font-bold text-lg">
                        500+ Questions
                    </div>
                </div>
            </div>

            <!-- Medical Safety -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-redolence-blue to-cyan-500">
                        <svg class="w-6 md:w-8 h-6 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg md:text-xl font-bold text-white mb-2 group-hover:text-redolence-blue transition-colors duration-300">
                        Medical Safety
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Safety protocols and medical standards information
                    </p>
                    <div class="text-redolence-blue font-bold text-lg">
                        100% Certified
                    </div>
                </div>
            </div>

            <!-- Treatment Info -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-purple-500 to-pink-500">
                        <svg class="w-6 md:w-8 h-6 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg md:text-xl font-bold text-white mb-2 group-hover:text-purple-400 transition-colors duration-300">
                        Treatment Info
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Detailed information about procedures and recovery
                    </p>
                    <div class="text-purple-400 font-bold text-lg">
                        24/7 Support
                    </div>
                </div>
            </div>
        </div>

        <!-- Call-to-Action -->
        <div class="text-center px-4" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="600">
            <div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4 mb-8 md:mb-0">
                <button onclick="document.getElementById('faq-section').scrollIntoView({behavior: 'smooth'})" class="hero-cta-primary w-full md:w-auto">
                    <span>Browse Questions</span>
                    <svg class="w-4 md:w-5 h-4 md:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <div class="text-gray-400 text-xs md:text-sm">
                    <div class="flex items-center justify-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-center">Expert medical guidance available</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator - Hidden on mobile -->
        <div class="hidden md:block absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div class="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary FAQ Section -->
<section class="py-32 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 30% 30%, var(--primary-green) 2px, transparent 2px), radial-gradient(circle at 70% 70%, var(--primary-blue) 2px, transparent 2px); background-size: 80px 80px;"></div>
    </div>

    <div class="max-w-7xl mx-auto px-6 relative">
        <!-- Search and Navigation Section -->
        <div class="text-center mb-20">
            <div class="inline-flex items-center bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 px-6 py-3 rounded-full mb-8 border border-redolence-green/20">
                <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <span class="font-semibold text-redolence-green">FIND YOUR ANSWERS</span>
            </div>
            
            <h2 class="text-5xl md:text-6xl font-black text-gray-900 mb-6 leading-tight">
                How Can We
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-redolence-green to-redolence-blue">Help You?</span>
            </h2>
            <p class="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
                Search our comprehensive medical aesthetics FAQ or browse by treatment category
            </p>

            <!-- Advanced Search Box -->
            <div class="search-box max-w-3xl mx-auto">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-redolence-green mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="text" id="faqSearch" class="search-input" placeholder="Search for treatment information, procedures, or policies..." onkeyup="searchFAQs()">
                </div>
            </div>

            <!-- Quick Category Navigation -->
            <div class="quick-links">
                <?php foreach (array_keys($faqsByCategory) as $category) : ?>
                    <button onclick="scrollToCategory('<?= htmlspecialchars($category) ?>')" class="quick-link">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <?php if ($category === 'treatments') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            <?php elseif ($category === 'preparation') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            <?php elseif ($category === 'aftercare') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            <?php elseif ($category === 'booking') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            <?php else : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            <?php endif; ?>
                        </svg>
                        <?= htmlspecialchars(ucwords(str_replace('_', ' ', $category))) ?>
                    </button>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- FAQ Categories -->
        <?php foreach ($faqsByCategory as $category => $faqs) : ?>
            <div id="<?= htmlspecialchars($category) ?>" class="mb-20 faq-category">
                <div class="category-header">
                    <div class="category-icon">
                        <svg class="w-8 h-8 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <?php if ($category === 'treatments') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            <?php elseif ($category === 'preparation') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            <?php elseif ($category === 'aftercare') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            <?php elseif ($category === 'booking') : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            <?php else : ?>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            <?php endif; ?>
                        </svg>
                    </div>
                    <h2 class="text-4xl md:text-5xl font-bold text-gray-900">
                        <?= htmlspecialchars(ucwords(str_replace('_', ' ', $category))) ?>
                    </h2>
                </div>

                <div class="grid gap-6">
                    <?php foreach ($faqs as $index => $faq) : ?>
                        <div class="medical-faq-card overflow-hidden">
                            <button class="faq-toggle w-full text-left p-8 focus:outline-none" onclick="toggleFAQ(this)">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <div class="w-14 h-14 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mr-6">
                                            <span class="text-redolence-green font-bold text-lg"><?= $index + 1 ?></span>
                                        </div>
                                        <h3 class="text-xl md:text-2xl font-bold text-gray-900 faq-question pr-4">
                                            <?= htmlspecialchars($faq['question']) ?>
                                        </h3>
                                    </div>
                                    <svg class="faq-icon w-8 h-8 text-redolence-green flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </button>
                            <div class="faq-content">
                                <p class="text-gray-700 leading-relaxed text-lg mb-6">
                                    <?= nl2br(htmlspecialchars($faq['answer'])) ?>
                                </p>

                                <?php if (!empty($faq['linked_services'])): ?>
                                    <div class="border-t border-gray-200 pt-6">
                                        <h4 class="text-lg font-semibold text-redolence-navy mb-4 flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                            </svg>
                                            <?= htmlspecialchars($faq['service_link_text'] ?: 'Related Services') ?>
                                        </h4>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <?php foreach ($faq['linked_services'] as $service): ?>
                                                <div class="bg-gradient-to-r from-redolence-green/5 to-redolence-blue/5 rounded-lg p-4 border border-redolence-green/20 hover:border-redolence-green/40 transition-all duration-300 group">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex-1">
                                                            <h5 class="font-semibold text-redolence-navy group-hover:text-redolence-green transition-colors duration-300">
                                                                <?= htmlspecialchars($service['name']) ?>
                                                            </h5>
                                                            <div class="flex items-center mt-2 text-sm text-gray-600">
                                                                <?php if ($service['price'] > 0): ?>
                                                                    <span class="text-redolence-green font-medium">TSH <?= number_format($service['price']) ?></span>
                                                                <?php endif; ?>
                                                                <?php if ($service['duration'] > 0): ?>
                                                                    <span class="ml-3 flex items-center">
                                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                        </svg>
                                                                        <?= $service['duration'] ?> min
                                                                    </span>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <a href="service-detail.php?id=<?= $service['id'] ?>"
                                                           class="ml-4 bg-redolence-green text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-redolence-green/90 transition-colors duration-300 flex items-center">
                                                            Learn More
                                                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                            </svg>
                                                        </a>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>

        <!-- Prominent Book Now CTA Section -->
        <div class="relative mb-20">
            <div class="bg-gradient-to-r from-redolence-green via-redolence-blue to-redolence-green bg-size-200 animate-gradient-x rounded-3xl p-12 text-center text-white shadow-2xl">
                <div class="max-w-4xl mx-auto">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-white/20 rounded-full mb-8">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h2 class="text-4xl md:text-5xl font-bold mb-6">Ready to Begin Your Transformation?</h2>
                    <p class="text-xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed">
                        Book your personalized consultation with our board-certified medical specialists and discover the perfect treatment plan for your aesthetic goals.
                    </p>

                    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                        <a href="customer/book/"
                           class="inline-flex items-center justify-center bg-white text-redolence-green px-10 py-5 rounded-2xl font-bold text-xl transition-all hover:scale-105 shadow-lg hover:shadow-xl group">
                            <svg class="w-6 h-6 mr-3 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Book Your Consultation Now
                        </a>

                        <div class="flex items-center text-white/90">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-lg">Free consultation • No obligation • Expert guidance</span>
                        </div>
                    </div>

                    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                        <div class="bg-white/10 rounded-xl p-4">
                            <div class="text-2xl font-bold">500+</div>
                            <div class="text-sm opacity-90">Successful Treatments</div>
                        </div>
                        <div class="bg-white/10 rounded-xl p-4">
                            <div class="text-2xl font-bold">98%</div>
                            <div class="text-sm opacity-90">Patient Satisfaction</div>
                        </div>
                        <div class="bg-white/10 rounded-xl p-4">
                            <div class="text-2xl font-bold">5★</div>
                            <div class="text-sm opacity-90">Average Rating</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Contact Section -->
        <div class="contact-enhancement relative">
            <div class="relative text-center z-10">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full mb-8">
                    <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Still Have Questions?</h2>
                <p class="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
                    Our board-certified medical specialists are here to provide personalized answers and expert guidance for your aesthetic journey.
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-gray-900 font-bold text-lg mb-2">Call Our Specialists</h3>
                        <p class="text-gray-600">Immediate medical consultation</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-gray-900 font-bold text-lg mb-2">Email Our Team</h3>
                        <p class="text-gray-600">Detailed medical inquiries</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-gray-900 font-bold text-lg mb-2">Visit Our Clinic</h3>
                        <p class="text-gray-600">In-person consultation</p>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="contact.php" class="inline-flex items-center justify-center bg-gradient-to-r from-redolence-green to-redolence-blue hover:from-redolence-blue hover:to-redolence-green text-white px-8 py-4 rounded-xl font-bold text-lg transition-all hover:scale-105 shadow-lg">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Schedule Consultation
                    </a>
                    <a href="tel:+255781985757" class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-green px-8 py-4 rounded-xl font-bold text-lg transition-all border-2 border-redolence-green/20 hover:border-redolence-green/40">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        Call: +255 781 985 757
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary JavaScript -->
<script>
// Enhanced FAQ functionality with medical aesthetics focus
function toggleFAQ(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('.faq-icon');
    const isOpen = content.classList.contains('show');

    // Close all other FAQs with smooth animation
    document.querySelectorAll('.faq-content').forEach(item => {
        if (item !== content && item.classList.contains('show')) {
            item.classList.remove('show');
            const otherIcon = item.previousElementSibling.querySelector('.faq-icon');
            if (otherIcon) {
                otherIcon.classList.remove('rotate');
            }
        }
    });

    // Toggle current FAQ with smooth animation
    if (isOpen) {
        content.classList.remove('show');
        icon.classList.remove('rotate');
    } else {
        content.classList.add('show');
        icon.classList.add('rotate');

        // Improved mobile scrolling behavior
        setTimeout(() => {
            // On mobile, scroll to show the question and some content
            if (window.innerWidth <= 768) {
                button.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });
            } else {
                // On desktop, use the original behavior
                button.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }
        }, 300); // Increased delay to allow content to expand
    }
}

// Advanced search functionality
function searchFAQs() {
    const searchTerm = document.getElementById('faqSearch').value.toLowerCase();
    const faqCards = document.querySelectorAll('.medical-faq-card');
    let visibleCount = 0;

    faqCards.forEach(card => {
        const question = card.querySelector('.faq-question').textContent.toLowerCase();
        const answer = card.querySelector('.faq-content p').textContent.toLowerCase();

        if (question.includes(searchTerm) || answer.includes(searchTerm)) {
            card.style.display = 'block';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
            visibleCount++;
        } else {
            card.style.display = 'none';
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
        }
    });

    // Show/hide category headers based on visible FAQs
    const categories = document.querySelectorAll('.faq-category');
    categories.forEach(category => {
        const categoryCards = category.querySelectorAll('.medical-faq-card');
        const visibleCards = Array.from(categoryCards).filter(card => card.style.display !== 'none');

        if (visibleCards.length > 0 || searchTerm === '') {
            category.style.display = 'block';
        } else {
            category.style.display = 'none';
        }
    });

    // Show no results message if needed
    showNoResultsMessage(visibleCount === 0 && searchTerm !== '');
}

// Scroll to category with enhanced animation
function scrollToCategory(categoryId) {
    const category = document.getElementById(categoryId);
    if (category) {
        category.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // Add highlight effect
        category.style.transform = 'scale(1.02)';
        category.style.filter = 'brightness(1.05)';
        setTimeout(() => {
            category.style.transform = 'scale(1)';
            category.style.filter = 'brightness(1)';
        }, 500);
    }
}

// Show/hide no results message
function showNoResultsMessage(show) {
    let noResultsDiv = document.getElementById('noResults');

    if (show && !noResultsDiv) {
        noResultsDiv = document.createElement('div');
        noResultsDiv.id = 'noResults';
        noResultsDiv.className = 'text-center py-20';
        noResultsDiv.innerHTML = `
            <div class="w-32 h-32 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-8">
                <svg class="w-16 h-16 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-6">No Results Found</h3>
            <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">We couldn't find any medical aesthetics FAQs matching your search. Try different keywords or browse our treatment categories.</p>
            <button onclick="clearSearch()" class="bg-gradient-to-r from-redolence-green to-redolence-blue text-white px-8 py-4 rounded-xl font-bold text-lg hover:scale-105 transition-all">
                Clear Search & Browse All
            </button>
        `;
        document.querySelector('.max-w-7xl').appendChild(noResultsDiv);
    } else if (!show && noResultsDiv) {
        noResultsDiv.remove();
    }
}

// Clear search
function clearSearch() {
    document.getElementById('faqSearch').value = '';
    searchFAQs();
}

// Initialize page with medical aesthetics enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth entrance animations to FAQ cards
    const faqCards = document.querySelectorAll('.medical-faq-card');
    faqCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.23, 1, 0.32, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });

    // Add search input event listener
    const searchInput = document.getElementById('faqSearch');
    if (searchInput) {
        searchInput.addEventListener('input', searchFAQs);
        
        // Add focus enhancement
        searchInput.addEventListener('focus', function() {
            this.parentElement.parentElement.style.transform = 'scale(1.02)';
        });
        
        searchInput.addEventListener('blur', function() {
            this.parentElement.parentElement.style.transform = 'scale(1)';
        });
    }

    // Add category hover enhancements
    const categoryHeaders = document.querySelectorAll('.category-header');
    categoryHeaders.forEach(header => {
        header.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.category-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
        });
        
        header.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.category-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });
});

// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 1000,
    easing: 'ease-out-cubic',
    once: true,
    offset: 100,
    delay: 0
});

// Enhanced Contact Card Interactions
document.querySelectorAll('.contact-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-15px) scale(1.03)';
        this.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.25)';
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
    });
});

// Typing Effect for Hero Text
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';

    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    type();
}

// Initialize typing effect when hero comes into view
const typingObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const typingText = entry.target.querySelector('.typing-text');
            if (typingText && !typingText.classList.contains('typed')) {
                typingText.classList.add('typed');
                const originalText = typingText.textContent;
                typeWriter(typingText, originalText, 80);
            }
        }
    });
}, { threshold: 0.3 });

// Observe hero section for typing effect
const heroSection = document.querySelector('section');
if (heroSection) {
    typingObserver.observe(heroSection);
}

// Enhanced Hero CTA Button
document.querySelectorAll('.hero-cta-primary').forEach(button => {
    button.addEventListener('click', function(e) {
        // Create ripple effect
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        this.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 800);
    });
});

// Add ripple effect CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        transform: scale(0);
        animation: ripple-animation 0.8s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>