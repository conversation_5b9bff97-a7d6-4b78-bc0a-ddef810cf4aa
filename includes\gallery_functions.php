<?php
/**
 * Gallery Functions
 * Database operations for gallery management
 */

/**
 * Get all active gallery items
 * @param string $category Optional category filter ('before-after', 'clinic', or null for all)
 * @return array Array of gallery items
 */
function getGalleryItems($category = null) {
    global $database;
    
    $sql = "SELECT * FROM gallery_items WHERE is_active = 1";
    $params = [];
    
    if ($category) {
        $sql .= " AND category = ?";
        $params[] = $category;
    }
    
    $sql .= " ORDER BY sort_order ASC, created_at DESC";
    
    return $database->fetchAll($sql, $params);
}

/**
 * Get a single gallery item by ID
 * @param int $id Gallery item ID
 * @return array|null Gallery item or null if not found
 */
function getGalleryItemById($id) {
    global $database;
    
    return $database->fetch(
        "SELECT * FROM gallery_items WHERE id = ? AND is_active = 1",
        [$id]
    );
}

/**
 * Get gallery items for admin (including inactive)
 * @param int $page Page number for pagination
 * @param int $limit Items per page
 * @param string $search Search term
 * @param string $category Category filter
 * @return array Array with 'items' and 'total' keys
 */
function getGalleryItemsForAdmin($page = 1, $limit = 20, $search = '', $category = '') {
    global $database;
    
    $offset = ($page - 1) * $limit;
    
    // Build WHERE clause
    $whereConditions = [];
    $params = [];
    
    if (!empty($search)) {
        $whereConditions[] = "(title LIKE ? OR description LIKE ? OR treatment LIKE ?)";
        $searchTerm = '%' . $search . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if (!empty($category)) {
        $whereConditions[] = "category = ?";
        $params[] = $category;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // Get total count
    $countSQL = "SELECT COUNT(*) as total FROM gallery_items $whereClause";
    $total = $database->fetch($countSQL, $params)['total'];
    
    // Get items
    $itemsSQL = "SELECT * FROM gallery_items $whereClause ORDER BY sort_order ASC, created_at DESC LIMIT $limit OFFSET $offset";
    $items = $database->fetchAll($itemsSQL, $params);
    
    return [
        'items' => $items,
        'total' => $total
    ];
}

/**
 * Create a new gallery item
 * @param array $data Gallery item data
 * @return int|false New gallery item ID or false on failure
 */
function createGalleryItem($data) {
    global $database;
    
    $sql = "INSERT INTO gallery_items (title, category, treatment, description, duration, sessions, before_image, after_image, image, sort_order, is_active) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $data['title'],
        $data['category'],
        $data['treatment'] ?? null,
        $data['description'],
        $data['duration'] ?? null,
        $data['sessions'] ?? null,
        $data['before_image'] ?? null,
        $data['after_image'] ?? null,
        $data['image'] ?? null,
        $data['sort_order'] ?? 0,
        $data['is_active'] ?? 1
    ];
    
    if ($database->execute($sql, $params)) {
        return $database->lastInsertId();
    }
    
    return false;
}

/**
 * Update a gallery item
 * @param int $id Gallery item ID
 * @param array $data Updated data
 * @return bool Success status
 */
function updateGalleryItem($id, $data) {
    global $database;
    
    $sql = "UPDATE gallery_items SET 
            title = ?, category = ?, treatment = ?, description = ?, 
            duration = ?, sessions = ?, before_image = ?, after_image = ?, 
            image = ?, sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?";
    
    $params = [
        $data['title'],
        $data['category'],
        $data['treatment'] ?? null,
        $data['description'],
        $data['duration'] ?? null,
        $data['sessions'] ?? null,
        $data['before_image'] ?? null,
        $data['after_image'] ?? null,
        $data['image'] ?? null,
        $data['sort_order'] ?? 0,
        $data['is_active'] ?? 1,
        $id
    ];
    
    return $database->execute($sql, $params);
}

/**
 * Delete a gallery item
 * @param int $id Gallery item ID
 * @return bool Success status
 */
function deleteGalleryItem($id) {
    global $database;
    
    return $database->execute("DELETE FROM gallery_items WHERE id = ?", [$id]);
}

/**
 * Toggle gallery item active status
 * @param int $id Gallery item ID
 * @return bool Success status
 */
function toggleGalleryItemStatus($id) {
    global $database;
    
    return $database->execute(
        "UPDATE gallery_items SET is_active = NOT is_active, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
        [$id]
    );
}

/**
 * Get gallery statistics
 * @return array Statistics array
 */
function getGalleryStats() {
    global $database;
    
    $stats = [];
    
    // Total items
    $stats['total'] = $database->fetch("SELECT COUNT(*) as count FROM gallery_items")['count'];
    
    // Active items
    $stats['active'] = $database->fetch("SELECT COUNT(*) as count FROM gallery_items WHERE is_active = 1")['count'];
    
    // Before/After items
    $stats['before_after'] = $database->fetch("SELECT COUNT(*) as count FROM gallery_items WHERE category = 'before-after' AND is_active = 1")['count'];
    
    // Clinic items
    $stats['clinic'] = $database->fetch("SELECT COUNT(*) as count FROM gallery_items WHERE category = 'clinic' AND is_active = 1")['count'];
    
    return $stats;
}

/**
 * Update sort orders for gallery items
 * @param array $sortData Array of ['id' => sort_order] pairs
 * @return bool Success status
 */
function updateGallerySortOrder($sortData) {
    global $database;
    
    try {
        $database->beginTransaction();
        
        foreach ($sortData as $id => $sortOrder) {
            $database->execute(
                "UPDATE gallery_items SET sort_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                [$sortOrder, $id]
            );
        }
        
        $database->commit();
        return true;
    } catch (Exception $e) {
        $database->rollback();
        return false;
    }
}

/**
 * Handle image upload for gallery
 * @param array $file $_FILES array element
 * @param string $type Image type ('before', 'after', 'image')
 * @return string|false Uploaded file path or false on failure
 */
function uploadGalleryImage($file, $type = 'image') {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }
    
    // Validate file size (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        return false;
    }
    
    // Create upload directory if it doesn't exist
    $uploadDir = __DIR__ . '/../uploads/gallery/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = $type . '_' . uniqid() . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return 'uploads/gallery/' . $filename;
    }
    
    return false;
}

/**
 * Delete gallery image file
 * @param string $imagePath Path to image file
 * @return bool Success status
 */
function deleteGalleryImage($imagePath) {
    if (empty($imagePath)) {
        return true;
    }
    
    $fullPath = __DIR__ . '/../' . $imagePath;
    if (file_exists($fullPath)) {
        return unlink($fullPath);
    }
    
    return true;
}
?>
