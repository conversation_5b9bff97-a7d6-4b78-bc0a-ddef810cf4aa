<?php
/**
 * Contact Page - Revolutionary Redesign
 * Redolence Medi Aesthetics - Advanced Medical Aesthetics Center
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/contact_functions.php';

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Handle form submission
$message = '';
$messageType = '';
$formData = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Verify CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            throw new Exception('Invalid security token. Please try again.');
        }

        // Sanitize input data
        $formData = sanitizeContactData($_POST);

        // Validate data
        $errors = validateContactData($formData);

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // Create contact message
        $contactId = createContactMessage($formData);

        if ($contactId) {
            $message = 'Thank you for contacting Redolence Medi Aesthetics! Our medical specialists will respond within 24 hours to discuss your aesthetic goals.';
            $messageType = 'success';

            // Clear form data on success
            $formData = [];

            // Regenerate CSRF token
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        } else {
            throw new Exception('Failed to send message. Please try again.');
        }

    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

$pageTitle = "Contact Redolence Medi Aesthetics - Schedule Your Consultation";
$pageDescription = "Contact Redolence Medi Aesthetics for advanced medical aesthetics consultations. Schedule your appointment with our board-certified specialists today.";

include __DIR__ . '/includes/header.php';
?>

<!-- AOS (Animate On Scroll) Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<!-- BEGIN OLD INLINE STYLES (commented) -->
<!-- Revolutionary CSS Framework -->
<!-- <style>
/* Advanced Medical Aesthetics Design System */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --accent-gold: #f4d03f;
    --deep-navy: #1a2332;
    --soft-gray: #f8fafc;
    --medical-white: #ffffff;
    --shadow-primary: rgba(73, 167, 92, 0.15);
    --shadow-blue: rgba(88, 148, 210, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    --gradient-contact: linear-gradient(135deg, #49a75c, #5894d2, #6ba3d6, #4db86d);
}

/* Revolutionary Animation Framework */
@keyframes morphingContactBg {
    0%, 100% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 50% 100%; }
    75% { background-position: 0% 100%; }
}

@keyframes floatingContact {
    0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
    33% { transform: translateY(-25px) rotate(8deg) scale(1.05); }
    66% { transform: translateY(-15px) rotate(-5deg) scale(0.95); }
}

@keyframes pulseGlowContact {
    0%, 100% { box-shadow: 0 0 30px rgba(73, 167, 92, 0.3), 0 0 60px rgba(88, 148, 210, 0.2); }
    50% { box-shadow: 0 0 50px rgba(88, 148, 210, 0.4), 0 0 80px rgba(73, 167, 92, 0.3); }
}

/* Ultra Modern Hero Section Animations */
@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes gradient-shift-reverse {
    0%, 100% { background-position: 100% 50%; }
    50% { background-position: 0% 50%; }
}

@keyframes float-slow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float-medium {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-180deg); }
}

@keyframes float-fast {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-25px) rotate(360deg); }
}

@keyframes particle-float {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) translateX(10px) rotate(90deg) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-40px) translateX(-5px) rotate(180deg) scale(0.8);
        opacity: 0.4;
    }
    75% {
        transform: translateY(-15px) translateX(-15px) rotate(270deg) scale(1.1);
        opacity: 0.9;
    }
}

@keyframes text-shimmer {
    0%, 100% { background-position: -200% center; }
    50% { background-position: 200% center; }
}

@keyframes gradient-text {
    0%, 100% {
        background-position: 0% 50%;
        filter: hue-rotate(0deg);
    }
    50% {
        background-position: 100% 50%;
        filter: hue-rotate(20deg);
    }
}

@keyframes grid-pulse {
    0%, 100% { opacity: 0.02; }
    50% { opacity: 0.05; }
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    0%, 50% { border-color: rgba(73, 167, 92, 0.8); }
    51%, 100% { border-color: transparent; }
}

@keyframes slideInContact {
    0% { opacity: 0; transform: translateX(-100px) rotateY(-30deg); }
    100% { opacity: 1; transform: translateX(0) rotateY(0deg); }
}

@keyframes scaleInContact {
    0% { opacity: 0; transform: scale(0.7) rotateZ(-10deg); }
    100% { opacity: 1; transform: scale(1) rotateZ(0deg); }
}

@keyframes formFieldFocus {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Ultra Modern Hero Section Styles */
.animate-gradient-shift {
    background-size: 400% 400%;
    animation: gradient-shift 8s ease-in-out infinite;
}

.animate-gradient-shift-reverse {
    background-size: 400% 400%;
    animation: gradient-shift-reverse 10s ease-in-out infinite;
}

.animate-float-slow {
    animation: float-slow 20s ease-in-out infinite;
}

.animate-float-medium {
    animation: float-medium 15s ease-in-out infinite;
}

.animate-float-fast {
    animation: float-fast 12s ease-in-out infinite;
}

.animate-particle-float {
    animation: particle-float 8s ease-in-out infinite;
}

.animate-text-shimmer {
    background-size: 400% 100%;
    animation: text-shimmer 3s ease-in-out infinite;
}

.animate-gradient-text {
    background-size: 300% 300%;
    animation: gradient-text 4s ease-in-out infinite;
}

.typing-text {
    overflow: hidden;
    border-right: 2px solid transparent;
    animation: typing 3s steps(30, end), blink 1s infinite;
    display: inline-block;
}

/* Contact Cards */
.contact-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 2rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
}

.contact-card:hover::before {
    left: 100%;
}

.contact-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.contact-card-inner {
    position: relative;
    z-index: 2;
    text-align: center;
}

.contact-icon-container {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.contact-card:hover .contact-icon-container {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.contact-cta-button {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.contact-cta-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Hero CTA Button */
.hero-cta-primary {
    background: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    border: none;
    border-radius: 16px;
    padding: 1rem 2rem;
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
    display: inline-flex;
    align-items: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(73, 167, 92, 0.3);
}

.hero-cta-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.4);
    filter: brightness(1.1);
}

.hero-cta-primary:active {
    transform: translateY(-1px) scale(1.02);
}

/* Mobile Responsive Fixes */
@media (max-width: 768px) {
    /* Fix oversized elements on mobile */
    .text-6xl {
        font-size: 2.5rem !important;
        line-height: 1.1 !important;
    }

    .text-8xl {
        font-size: 3rem !important;
        line-height: 1.1 !important;
    }

    .text-2xl {
        font-size: 1.25rem !important;
        line-height: 1.4 !important;
    }

    .text-3xl {
        font-size: 1.5rem !important;
        line-height: 1.3 !important;
    }

    /* Fix premium badge positioning and size */
    .mb-12:first-child {
        margin-bottom: 2rem !important;
    }

    .inline-flex.items-center.bg-white\/10 {
        padding: 0.75rem 1.5rem !important;
        font-size: 0.75rem !important;
    }

    .inline-flex.items-center.bg-white\/10 span {
        font-size: 0.75rem !important;
        letter-spacing: 0.05em !important;
    }

    /* Fix contact cards spacing */
    .contact-card {
        padding: 1.5rem !important;
        margin-bottom: 1rem !important;
    }

    .contact-icon-container {
        width: 3rem !important;
        height: 3rem !important;
        margin-bottom: 0.75rem !important;
    }

    .contact-icon-container svg {
        width: 1.5rem !important;
        height: 1.5rem !important;
    }

    .contact-card h3 {
        font-size: 1.125rem !important;
        margin-bottom: 0.5rem !important;
    }

    .contact-card p {
        font-size: 0.875rem !important;
        margin-bottom: 1rem !important;
    }

    .contact-cta-button {
        padding: 0.5rem 1rem !important;
        font-size: 0.75rem !important;
    }

    /* Fix Call-to-Action section */
    .hero-cta-primary {
        padding: 0.875rem 1.5rem !important;
        font-size: 1rem !important;
        width: 100% !important;
        justify-content: center !important;
        margin-bottom: 1rem !important;
    }

    .inline-flex.items-center.space-x-4 {
        flex-direction: column !important;
        align-items: center !important;
        gap: 1rem !important;
    }

    /* Fix section padding */
    .min-h-screen {
        padding: 2rem 0 !important;
        min-height: auto !important;
    }

    .max-w-7xl {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* Fix grid spacing */
    .grid.grid-cols-1.md\\:grid-cols-3 {
        gap: 1.5rem !important;
        margin-bottom: 2rem !important;
    }

    /* Fix typing text */
    .typing-text {
        font-size: 1.125rem !important;
        line-height: 1.4 !important;
    }

    /* Fix scroll indicator */
    .absolute.bottom-8 {
        bottom: 1rem !important;
    }

    /* Reduce animation intensity on mobile */
    .animate-float-slow,
    .animate-float-medium,
    .animate-float-fast {
        animation-duration: 30s !important;
    }

    .animate-particle-float {
        animation-duration: 15s !important;
    }
}

/* Revolutionary Contact Card System */
.contact-card-revolutionary {
    background: white;
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.contact-card-revolutionary:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(73, 167, 92, 0.3);
}

/* Advanced Form System */
.form-field-revolutionary {
    position: relative;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.form-field-revolutionary input,
.form-field-revolutionary textarea,
.form-field-revolutionary select {
    width: 100%;
    padding: 1rem 1.5rem;
    background: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    color: #374151;
    transition: all 0.3s ease;
    display: block;
    box-sizing: border-box;
}

.form-field-revolutionary input::placeholder,
.form-field-revolutionary textarea::placeholder {
    color: #9ca3af;
    opacity: 1;
}

.form-field-revolutionary input:focus,
.form-field-revolutionary textarea:focus,
.form-field-revolutionary select:focus {
    outline: none;
    border-color: #49a75c;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(73, 167, 92, 0.1);
}

.form-field-revolutionary:focus-within {
    transform: translateY(-2px);
}

/* Revolutionary Button System */
.btn-contact-revolutionary {
    background: var(--gradient-contact);
    background-size: 300% 300%;
    border: none;
    padding: 1.5rem 3rem;
    border-radius: 50px;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;
    animation: morphingContactBg 8s ease infinite;
    cursor: pointer;
}

.btn-contact-revolutionary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.7s ease;
}

.btn-contact-revolutionary:hover::before {
    left: 100%;
}

.btn-contact-revolutionary:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.4), 0 0 30px rgba(88, 148, 210, 0.3);
}

.btn-contact-revolutionary:active {
    transform: translateY(-2px) scale(1.02);
}

/* Contact Info Cards */
.contact-info-revolutionary {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(20px);
    border: 2px solid rgba(73, 167, 92, 0.1);
    border-radius: 24px;
    padding: 2rem;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.contact-info-revolutionary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-soft);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.contact-info-revolutionary:hover::after {
    opacity: 1;
}

.contact-info-revolutionary:hover {
    transform: translateY(-10px) rotateY(8deg) scale(1.05);
    border-color: rgba(73, 167, 92, 0.3);
    box-shadow: 0 25px 50px var(--shadow-primary);
}

/* Morphing Background System */
.morphing-contact-bg {
    background: var(--gradient-contact);
    background-size: 400% 400%;
    animation: morphingContactBg 12s ease infinite;
}

/* Floating Elements */
.floating-contact {
    animation: floatingContact 8s ease-in-out infinite;
}

.floating-contact:nth-child(2) { animation-delay: 2.5s; }
.floating-contact:nth-child(3) { animation-delay: 5s; }
.floating-contact:nth-child(4) { animation-delay: 7.5s; }

/* Advanced Typography */
.text-contact-revolutionary {
    background: var(--gradient-contact);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: morphingContactBg 8s ease infinite;
}

/* Scroll Reveal System */
.contact-reveal {
    opacity: 0;
    transform: translateY(60px) rotateX(-15deg);
    transition: all 1s cubic-bezier(0.23, 1, 0.32, 1);
}

.contact-reveal.revealed {
    opacity: 1;
    transform: translateY(0) rotateX(0deg);
}

/* Interactive Elements */
.contact-interactive {
    transition: all 0.3s ease;
    cursor: pointer;
}

.contact-interactive:hover {
    transform: translateY(-3px);
}

/* Map Container */
.map-revolutionary {
    border-radius: 32px;
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(73, 167, 92, 0.2);
    transition: all 0.5s ease;
}

.map-revolutionary:hover {
    transform: scale(1.02);
    border-color: rgba(73, 167, 92, 0.4);
    box-shadow: 0 20px 40px var(--shadow-primary);
}

/* Remove all gradient hover effects */
.contact-card-revolutionary:hover::before,
.contact-info-revolutionary:hover::after {
    opacity: 0 !important;
}

.contact-card-revolutionary:hover,
.contact-info-revolutionary:hover {
    transform: none !important;
    box-shadow: none !important;
    border-color: initial !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-card-revolutionary {
        margin: 0;
        border-radius: 16px;
        padding: 1.5rem;
    }

    .form-field-revolutionary input,
    .form-field-revolutionary textarea,
    .form-field-revolutionary select {
        padding: 1rem 1.2rem;
        font-size: 1rem;
        border-radius: 12px;
    }

    .btn-contact-revolutionary {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        border-radius: 12px;
    }

    /* Mobile Form Section */
    .py-40 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    .py-32 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }

    /* Mobile Form Header */
    .contact-card-revolutionary h2 {
        font-size: 2rem;
    }

    .contact-card-revolutionary .text-5xl {
        font-size: 2rem;
    }

    .contact-card-revolutionary .w-20 {
        width: 3rem;
        height: 3rem;
    }

    /* Mobile Contact Info */
    .contact-info-revolutionary h3 {
        font-size: 1.5rem;
    }

    .contact-info-revolutionary h4 {
        font-size: 1.2rem;
    }

    .contact-info-revolutionary .text-4xl {
        font-size: 1.5rem;
    }

    .contact-info-revolutionary .w-20 {
        width: 3rem;
        height: 3rem;
    }

    .contact-info-revolutionary .w-16 {
        width: 3rem;
        height: 3rem;
    }

    /* Mobile Map Section */
    .map-revolutionary {
        border-radius: 16px;
        border-width: 2px;
    }

    .map-revolutionary:hover {
        transform: none;
        border-color: initial;
        box-shadow: none;
    }

    /* Mobile Grid Adjustments */
    .grid.grid-cols-1.lg\\:grid-cols-2 {
        gap: 2rem;
    }

    .grid.grid-cols-1.md\\:grid-cols-2 {
        gap: 1rem;
    }

    .grid.grid-cols-1.md\\:grid-cols-3 {
        gap: 1rem;
    }
}

/* Success/Error Messages */
.message-revolutionary {
    border-radius: 20px;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(15px);
    border: 2px solid;
    animation: scaleInContact 0.5s ease;
}

.message-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    border-color: rgba(34, 197, 94, 0.3);
    color: #059669;
}

.message-error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
    border-color: rgba(239, 68, 68, 0.3);
    color: #dc2626;
}
</style> -->
<!-- END OLD INLINE STYLES -->

<!-- Clinical Light UI Styles (Medical/Clinical) -->
<style>
:root {
  --clinic-teal: #0ea5a5;
  --clinic-cyan: #06b6d4;
  --clinic-blue: #0ea5e9;
  --clinic-green: #10b981;
  --ink: #0f172a;
  --muted: #475569;
  --surface: #ffffff;
  --bg: #f8fafc;
  --border: #e2e8f0;
  --ring: rgba(6,182,212,.25);
}

.page-wrap { background: var(--bg); color: var(--ink); }

.hero { padding: clamp(2.5rem, 6vw, 5rem) 1rem; background: linear-gradient(180deg,#ffffff 0%,#f8fafc 100%); border-bottom: 1px solid var(--border); }

.clinical-title {
  background: linear-gradient(90deg, #0ea5a5, #0ea5e9 60%, #06b6d4 100%);
  -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;
}

.panel { background: var(--surface); border-radius: 12px; box-shadow: 0 4px 14px rgba(2,6,23,.06); border: 1px solid var(--border); }

.badge { display:inline-flex; align-items:center; gap:.5rem; padding:.5rem .75rem; border-radius:9999px; background:#ecfeff; color:#036672; border:1px solid #cffafe; font-weight:600; }
.badge svg { color:#06b6d4; }

.stats { display:grid; grid-template-columns: repeat(3,minmax(0,1fr)); gap:.75rem; width:100%; max-width:44rem; }
.stat { background:#ffffff; border:1px solid var(--border); border-radius:10px; padding:.6rem .9rem; text-align:center; }
.stat .label { font-size:.7rem; letter-spacing:.02em; color:#64748b; text-transform:uppercase; }
.stat .value { font-weight:700; color:#0f172a; margin-top:.15rem; }

.field label { display:block; font-size:.85rem; color:#334155; margin:0 0 .4rem .2rem; font-weight:600; }
.field input,.field select,.field textarea { width:100%; border:1px solid #cbd5e1; background:#fff; color:#0f172a; border-radius:10px; padding:0.75rem .9rem; font-size:1rem; outline:none; transition: box-shadow .2s ease, border-color .2s ease; }
.field textarea { resize:vertical; min-height:140px; }
.field input:focus,.field select:focus,.field textarea:focus { border-color: var(--clinic-cyan); box-shadow: 0 0 0 5px var(--ring); }

.btn-primary { appearance:none; border:0; border-radius:10px; background: var(--clinic-teal); color:#fff; font-weight:700; letter-spacing:.01em; padding:.85rem 1rem; display:inline-flex; align-items:center; justify-content:center; gap:.5rem; width:100%; box-shadow: 0 6px 18px rgba(14,165,165,.24); transition: transform .1s ease, filter .2s ease, box-shadow .2s ease; }
.btn-primary:hover { transform: translateY(-1px); filter: brightness(1.03); box-shadow: 0 12px 28px rgba(14,165,165,.28); }
.btn-primary:active { transform: translateY(0); }
.btn-primary[disabled] { opacity:.75; cursor:not-allowed; }

.alert { border-radius:10px; padding: .85rem 1rem; display:flex; gap:.65rem; align-items:flex-start; border:1px solid var(--border); background:#ffffff; }
.alert-success { border-left:4px solid var(--clinic-green); background:#ecfdf5; color:#065f46; }
.alert-error { border-left:4px solid #ef4444; background:#fef2f2; color:#991b1b; }

.contact-info li { display:flex; gap:.85rem; align-items:flex-start; padding:.85rem 1rem; border-radius: 10px; border:1px solid var(--border); background:#fff; }
.contact-info svg { width:20px; height:20px; color: var(--clinic-teal); margin-top:2px; }

.map-frame { border-radius:12px; overflow:hidden; border:1px solid var(--border); }

@media (max-width: 768px){ .stats { grid-template-columns: repeat(3,minmax(0,1fr)); } }
</style>



<!-- Clinical Contact Experience -->
<div class="page-wrap">
  <!-- Hero -->
  <section class="hero relative overflow-hidden">
    <div class="max-w-7xl mx-auto px-4 md:px-6">
      <div class="flex flex-col items-center text-center gap-5">
        <span class="badge" aria-label="Medical consultation availability">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" aria-hidden="true"><path d="M12 6v12m6-6H6" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg>
          Medical consultations
        </span>

        <h1 class="clinical-title text-4xl md:text-6xl font-extrabold tracking-tight leading-tight">
          Contact Redolence
        </h1>
        <p class="text-base md:text-lg text-slate-600 max-w-2xl">
          Speak with our specialists about treatments, pricing, and availability. We respond within 24 hours.
        </p>

        <div class="stats">
          <div class="stat"><div class="label">Average reply</div><div class="value">&lt; 24 hours</div></div>
          <div class="stat"><div class="label">Security</div><div class="value">TLS/CSRF</div></div>
          <div class="stat"><div class="label">Support</div><div class="value">Mon–Sat, 9–19</div></div>
        </div>

        <div class="flex flex-col sm:flex-row items-center gap-3 mt-2">
          <a href="#contact" class="btn-primary" aria-label="Start your consultation">Start consultation</a>
          <a href="tel:+255781985757" class="text-sm text-slate-600 hover:text-slate-800 underline underline-offset-4">Or call +255 781 985 757</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact + Details -->
  <section id="contact" class="py-10 md:py-16">
    <div class="max-w-7xl mx-auto px-4 md:px-6 grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Form panel -->
      <div class="panel p-6 md:p-8">
        <header class="mb-6">
          <h2 id="contact-title" class="text-2xl md:text-3xl font-extrabold text-slate-900">Tell us about your goals</h2>
          <p class="text-slate-600 mt-1">Our team will tailor recommendations and next steps.</p>
        </header>

        <?php if ($message): ?>
          <div class="alert <?= $messageType === 'success' ? 'alert-success' : 'alert-error' ?>" role="status">
            <svg aria-hidden="true" class="mt-0.5" viewBox="0 0 24 24" fill="none">
              <?php if ($messageType === 'success'): ?>
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2" />
              <?php else: ?>
                <path d="M12 8v5m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              <?php endif; ?>
            </svg>
            <div class="font-medium">
              <?= $message ?>
            </div>
          </div>
        <?php endif; ?>

        <form method="POST" id="revolutionaryContactForm" class="space-y-5" aria-labelledby="contact-title" novalidate>
          <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="field">
              <label for="name">Full Name<span class="sr-only"> required</span></label>
              <input type="text" id="name" name="name" required value="<?= htmlspecialchars($formData['name'] ?? '') ?>" placeholder="e.g., Jane Doe" />
            </div>
            <div class="field">
              <label for="email">Email<span class="sr-only"> required</span></label>
              <input type="email" id="email" name="email" required value="<?= htmlspecialchars($formData['email'] ?? '') ?>" placeholder="<EMAIL>" />
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="field">
              <label for="phone">Phone</label>
              <input type="tel" id="phone" name="phone" value="<?= htmlspecialchars($formData['phone'] ?? '') ?>" placeholder="Optional" />
            </div>
            <div class="field">
              <label for="subject">Treatment Interest</label>
              <select id="subject" name="subject">
                <option value="">Select Treatment Interest</option>
                <option value="consultation" <?= ($formData['subject'] ?? '') === 'consultation' ? 'selected' : '' ?>>Initial Consultation</option>
                <option value="facial" <?= ($formData['subject'] ?? '') === 'facial' ? 'selected' : '' ?>>Facial Rejuvenation</option>
                <option value="body" <?= ($formData['subject'] ?? '') === 'body' ? 'selected' : '' ?>>Body Contouring</option>
                <option value="skin" <?= ($formData['subject'] ?? '') === 'skin' ? 'selected' : '' ?>>Skin Treatments</option>
                <option value="injectables" <?= ($formData['subject'] ?? '') === 'injectables' ? 'selected' : '' ?>>Injectable Treatments</option>
                <option value="laser" <?= ($formData['subject'] ?? '') === 'laser' ? 'selected' : '' ?>>Laser Procedures</option>
                <option value="other" <?= ($formData['subject'] ?? '') === 'other' ? 'selected' : '' ?>>Other Treatment</option>
              </select>
            </div>
          </div>

          <div class="field">
            <label for="message">Message<span class="sr-only"> required</span></label>
            <textarea id="message" name="message" rows="6" required placeholder="Tell us about your goals, concerns, or questions... *"><?php echo htmlspecialchars($formData['message'] ?? ''); ?></textarea>
          </div>

          <button type="submit" id="submitBtn" class="btn-primary" aria-live="polite">
            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" aria-hidden="true"><path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
            <span id="submitText">Send message</span>
          </button>
        </form>
      </div>

      <!-- Info panel -->
      <aside class="panel p-6 md:p-8" aria-label="Contact information">
        <h2 class="text-xl md:text-2xl font-extrabold text-slate-900 mb-4">Get in touch</h2>
        <ul class="contact-info space-y-3">
          <li>
            <svg viewBox="0 0 24 24" fill="none" aria-hidden="true"><path d="M3 5a2 2 0 012-2h2.8a1 1 0 01.95.68l1.5 4.5a1 1 0 01-.5 1.2l-2.26 1.13a11 11 0 005.52 5.52l1.13-2.26a1 1 0 011.2-.5l4.5 1.5a1 1 0 01.68.95V19a2 2 0 01-2 2h-1C9.72 21 3 14.28 3 6V5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
            <div>
              <div class="text-sm text-slate-500">Phone</div>
              <a href="tel:+255781985757" class="font-semibold text-slate-800 hover:text-cyan-700">+255 781 985 757</a>
            </div>
          </li>
          <li>
            <svg viewBox="0 0 24 24" fill="none" aria-hidden="true"><path d="M3 8l7.9 4.26a2 2 0 002.2 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
            <div>
              <div class="text-sm text-slate-500">Email</div>
              <a href="mailto:<EMAIL>" class="font-semibold text-slate-800 hover:text-cyan-700"><EMAIL></a>
            </div>
          </li>
          <li>
            <svg viewBox="0 0 24 24" fill="none" aria-hidden="true"><path d="M17.66 16.66L13.41 20.9a2 2 0 01-2.83 0l-4.24-4.24a8 8 0 1111.32 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
            <div>
              <div class="text-sm text-slate-500">Clinic</div>
              <div class="font-semibold text-slate-800">Medical Plaza, Dar es Salaam</div>
            </div>
          </li>
          <li>
            <svg viewBox="0 0 24 24" fill="none" aria-hidden="true"><path d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5A2.25 2.25 0 015.25 5.25h13.5A2.25 2.25 0 0121 7.5v11.25M3 18.75A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
            <div>
              <div class="text-sm text-slate-500">Hours</div>
              <div class="font-semibold text-slate-800">Mon–Sat: 9:00–19:00 · Sun: Closed</div>
            </div>
          </li>
        </ul>

        <div class="mt-6">
          <a href="https://maps.app.goo.gl/9R4GdECM3wykgtRv8" target="_blank" class="btn-primary">Get directions</a>
        </div>

        <div class="mt-8 map-frame">
          <iframe title="Redolence location map" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3961.8269!2d39.2912!3d-6.7977!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x185c4b0c1e5d7f27%3A0x5e0e5e5e5e5e5e5e!2sCity%20Plaza%2C%20Jamhuri%20Street%2C%20Dar%20es%20Salaam%2C%20Tanzania!5e0!3m2!1sen!2stz!4v1703000000000!5m2!1sen!2stz" width="100%" height="100%" style="border:0;" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
        </div>
      </aside>
    </div>
  </section>
</div>

<!-- Hide legacy contact layout below (kept for reference) -->
<div class="legacy-contact" hidden aria-hidden="true">

<!-- Ultra Modern Hero Section - Complete Redesign -->
<section class="relative min-h-screen md:min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 py-8 md:py-0">
    <!-- Advanced Background Effects -->
    <div class="absolute inset-0">
        <!-- Dynamic Gradient Mesh -->
        <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/20 via-transparent to-redolence-blue/20 animate-gradient-shift"></div>
        <div class="absolute inset-0 bg-gradient-to-tl from-purple-500/10 via-transparent to-emerald-500/10 animate-gradient-shift-reverse"></div>

        <!-- Floating Orbs -->
        <div class="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-redolence-green/30 to-emerald-400/30 rounded-full blur-3xl animate-float-slow"></div>
        <div class="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-redolence-blue/30 to-cyan-400/30 rounded-full blur-3xl animate-float-medium"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-float-fast"></div>

        <!-- Animated Particles -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="particle absolute top-1/4 left-1/4 w-2 h-2 bg-redolence-green/60 rounded-full animate-particle-float"></div>
            <div class="particle absolute top-3/4 right-1/4 w-3 h-3 bg-redolence-blue/60 rounded-full animate-particle-float" style="animation-delay: 1s;"></div>
            <div class="particle absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-purple-400/60 rounded-full animate-particle-float" style="animation-delay: 2s;"></div>
            <div class="particle absolute bottom-1/4 left-1/2 w-2.5 h-2.5 bg-emerald-400/60 rounded-full animate-particle-float" style="animation-delay: 3s;"></div>
            <div class="particle absolute top-1/3 right-1/3 w-2 h-2 bg-cyan-400/60 rounded-full animate-particle-float" style="animation-delay: 4s;"></div>
        </div>

        <!-- Grid Pattern -->
        <div class="absolute inset-0 opacity-[0.02]" style="background-image: linear-gradient(rgba(73, 167, 92, 0.5) 1px, transparent 1px), linear-gradient(90deg, rgba(73, 167, 92, 0.5) 1px, transparent 1px); background-size: 80px 80px; animation: grid-pulse 4s ease-in-out infinite;"></div>
    </div>

    <!-- Main Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 md:px-6 text-center">
        <!-- Premium Badge - Mobile Optimized -->
        <div class="mb-8 md:mb-12" data-aos="fade-up" data-aos-duration="1000">
            <div class="inline-flex items-center bg-white/10 backdrop-blur-md px-4 md:px-8 py-2 md:py-4 rounded-full border border-white/20 shadow-2xl hover:scale-105 transition-all duration-500">
                <div class="w-2 md:w-3 h-2 md:h-3 bg-redolence-green rounded-full mr-2 md:mr-3 animate-pulse"></div>
                <span class="font-bold text-white tracking-wider text-xs md:text-sm uppercase">Connect with Specialists</span>
                <div class="w-2 md:w-3 h-2 md:h-3 bg-redolence-blue rounded-full ml-2 md:ml-3 animate-pulse" style="animation-delay: 0.5s;"></div>
            </div>
        </div>

        <!-- Dynamic Title - Mobile Optimized -->
        <div class="mb-8 md:mb-12" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="200">
            <h1 class="text-4xl md:text-6xl lg:text-8xl font-black mb-4 md:mb-6 leading-tight">
                <span class="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent animate-text-shimmer">
                    Contact
                </span>
                <br>
                <span class="bg-gradient-to-r from-redolence-green via-emerald-400 to-redolence-blue bg-clip-text text-transparent animate-gradient-text">
                    Redolence
                </span>
            </h1>

            <div class="relative">
                <p class="text-lg md:text-2xl lg:text-3xl text-gray-300 font-light leading-relaxed mb-6 md:mb-8">
                    Your <span class="text-redolence-green font-semibold">Medical Aesthetics</span> Journey
                    <br class="hidden md:block">
                    <span class="typing-text">Begins with Expert Consultation</span>
                </p>

                <!-- Decorative Elements - Hidden on mobile -->
                <div class="hidden md:block absolute -top-4 -right-8 w-8 h-8 bg-gradient-to-r from-redolence-green/30 to-emerald-400/30 rounded-full blur-sm animate-pulse"></div>
                <div class="hidden md:block absolute -bottom-4 -left-8 w-6 h-6 bg-gradient-to-r from-redolence-blue/30 to-cyan-400/30 rounded-full blur-sm animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <!-- Enhanced Contact Methods -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto mb-16" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="400">
            <!-- Phone Contact -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-redolence-green to-emerald-500">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 group-hover:text-redolence-green transition-colors duration-300">
                        Call Directly
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Speak with our medical consultants for immediate assistance
                    </p>
                    <a href="tel:+255781985757" class="contact-cta-button">
                        <span>+255 781 985 757</span>
                        <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Email Contact -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-redolence-blue to-cyan-500">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 group-hover:text-redolence-blue transition-colors duration-300">
                        Email Consultation
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Send detailed inquiries for comprehensive treatment planning
                    </p>
                    <a href="mailto:<EMAIL>" class="contact-cta-button">
                        <span><EMAIL></span>
                        <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Location -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-purple-500 to-pink-500">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 group-hover:text-purple-400 transition-colors duration-300">
                        Visit Our Clinic
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Experience our state-of-the-art medical aesthetics facility
                    </p>
                    <div class="contact-cta-button cursor-default">
                        <span>Medical Plaza, Dar es Salaam</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call-to-Action - Mobile Optimized -->
        <div class="text-center px-4" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="600">
            <div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4 mb-8 md:mb-0">
                <button onclick="document.getElementById('contact-form').scrollIntoView({behavior: 'smooth'})" class="hero-cta-primary w-full md:w-auto">
                    <span>Schedule Consultation</span>
                    <svg class="w-4 md:w-5 h-4 md:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </button>

                <div class="text-gray-400 text-xs md:text-sm">
                    <div class="flex items-center justify-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-center">Available 24/7 for consultations</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator - Hidden on mobile -->
        <div class="hidden md:block absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div class="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary Contact Form Section -->
<section class="py-40 bg-gradient-to-br from-gray-50 via-white to-redolence-green/5 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 30% 30%, var(--primary-green) 3px, transparent 3px), radial-gradient(circle at 70% 70%, var(--primary-blue) 3px, transparent 3px); background-size: 100px 100px;"></div>
    </div>

    <!-- Floating Background Elements -->
    <div class="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-redolence-green/10 to-transparent rounded-full blur-3xl"></div>
    <div class="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tr from-redolence-blue/10 to-transparent rounded-full blur-3xl"></div>

    <div class="max-w-7xl mx-auto px-6 relative">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-20">
            <!-- Revolutionary Contact Form -->
            <div class="contact-reveal">
                <div class="contact-card-revolutionary p-12">
                    <!-- Form Header -->
                    <div class="text-center mb-12">
                        <div class="w-20 h-20 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <h2 class="text-5xl font-black text-gray-900 mb-6">
                            Schedule Your
                            <span class="text-contact-revolutionary block">Medical Consultation</span>
                        </h2>
                        <p class="text-xl text-gray-600 leading-relaxed">
                            Our board-certified specialists will respond within 24 hours to discuss your aesthetic goals
                        </p>
                    </div>

                    <!-- Success/Error Messages -->
                    <?php if ($message): ?>
                        <div class="message-revolutionary <?= $messageType === 'success' ? 'message-success' : 'message-error' ?>">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <?php if ($messageType === 'success'): ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    <?php else: ?>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                    <?php endif; ?>
                                </svg>
                                <div class="font-semibold"><?= $message ?></div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Revolutionary Form -->
                    <form method="POST" class="space-y-8" id="revolutionaryContactForm_legacy">
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token']) ?>">

                        <!-- Name and Email Row -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-field-revolutionary">
                                <input type="text" id="name" name="name" required
                                       value="<?= htmlspecialchars($formData['name'] ?? '') ?>"
                                       placeholder="Full Name *"
                                       class="contact-interactive">
                            </div>
                            <div class="form-field-revolutionary">
                                <input type="email" id="email" name="email" required
                                       value="<?= htmlspecialchars($formData['email'] ?? '') ?>"
                                       placeholder="Email Address *"
                                       class="contact-interactive">
                            </div>
                        </div>

                        <!-- Phone and Subject Row -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-field-revolutionary">
                                <input type="tel" id="phone" name="phone"
                                       value="<?= htmlspecialchars($formData['phone'] ?? '') ?>"
                                       placeholder="Phone Number (Optional)"
                                       class="contact-interactive">
                            </div>
                            <div class="form-field-revolutionary">
                                <select id="subject" name="subject" class="contact-interactive">
                                    <option value="">Select Treatment Interest</option>
                                    <option value="consultation" <?= ($formData['subject'] ?? '') === 'consultation' ? 'selected' : '' ?>>Initial Consultation</option>
                                    <option value="facial" <?= ($formData['subject'] ?? '') === 'facial' ? 'selected' : '' ?>>Facial Rejuvenation</option>
                                    <option value="body" <?= ($formData['subject'] ?? '') === 'body' ? 'selected' : '' ?>>Body Contouring</option>
                                    <option value="skin" <?= ($formData['subject'] ?? '') === 'skin' ? 'selected' : '' ?>>Skin Treatments</option>
                                    <option value="injectables" <?= ($formData['subject'] ?? '') === 'injectables' ? 'selected' : '' ?>>Injectable Treatments</option>
                                    <option value="laser" <?= ($formData['subject'] ?? '') === 'laser' ? 'selected' : '' ?>>Laser Procedures</option>
                                    <option value="other" <?= ($formData['subject'] ?? '') === 'other' ? 'selected' : '' ?>>Other Treatment</option>
                                </select>
                            </div>
                        </div>

                        <!-- Message Field -->
                        <div class="form-field-revolutionary">
                            <textarea id="message" name="message" rows="6" required
                                      placeholder="Tell us about your aesthetic goals, concerns, and any questions you have... *"
                                      class="contact-interactive resize-none"><?= htmlspecialchars($formData['message'] ?? '') ?></textarea>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" id="submitBtn_legacy" class="btn-contact-revolutionary w-full contact-interactive">
                            <svg class="w-6 h-6 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            <span id="submitText_legacy">Schedule My Consultation</span>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Revolutionary Contact Information -->
            <div class="space-y-10 contact-reveal" style="animation-delay: 0.3s;">
                <!-- Main Contact Info -->
                <div class="contact-info-revolutionary">
                    <div class="relative z-10">
                        <div class="text-center mb-10">
                            <div class="w-20 h-20 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-10 h-10 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-4xl font-bold text-gray-900 mb-6">
                                Get in <span class="text-contact-revolutionary">Touch</span>
                            </h3>
                            <p class="text-xl text-gray-600">Multiple ways to reach our medical specialists</p>
                        </div>

                        <!-- Contact Methods -->
                        <div class="space-y-8">
                            <!-- Location -->
                            <div class="flex items-start space-x-6 p-6 bg-white/50 rounded-2xl backdrop-blur-sm border border-redolence-green/10 contact-interactive">
                                <div class="w-16 h-16 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-8 h-8 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-2xl font-bold text-gray-900 mb-3">Medical Clinic Location</h4>
                                    <p class="text-gray-600 leading-relaxed text-lg">
                                        Medical Plaza, Professional District<br>
                                        Healthcare Avenue, Suite 205<br>
                                        Dar es Salaam, Tanzania
                                    </p>
                                    <a href="https://maps.app.goo.gl/9R4GdECM3wykgtRv8" target="_blank"
                                       class="inline-flex items-center text-redolence-green hover:text-redolence-blue transition-colors mt-4 font-semibold">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                                        </svg>
                                        Get Directions
                                    </a>
                                </div>
                            </div>

                            <!-- Phone -->
                            <div class="flex items-start space-x-6 p-6 bg-white/50 rounded-2xl backdrop-blur-sm border border-redolence-blue/10 contact-interactive">
                                <div class="w-16 h-16 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-8 h-8 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-2xl font-bold text-gray-900 mb-3">Call Our Specialists</h4>
                                    <p class="text-gray-600 mb-2">
                                        <a href="tel:+255781985757" class="text-redolence-blue hover:text-redolence-green transition-colors text-2xl font-bold">
                                            +255 781 985 757
                                        </a>
                                    </p>
                                    <p class="text-gray-500">Monday - Saturday: 9:00 AM - 7:00 PM</p>
                                    <p class="text-gray-500">Sunday: Closed</p>
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="flex items-start space-x-6 p-6 bg-white/50 rounded-2xl backdrop-blur-sm border border-purple-500/10 contact-interactive">
                                <div class="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-2xl font-bold text-gray-900 mb-3">Email Our Medical Team</h4>
                                    <p class="text-gray-600 mb-2">
                                        <a href="mailto:<EMAIL>" class="text-purple-600 hover:text-redolence-green transition-colors text-lg font-semibold">
                                            <EMAIL>
                                        </a>
                                    </p>
                                    <p class="text-gray-600">
                                        <a href="mailto:<EMAIL>" class="text-purple-600 hover:text-redolence-blue transition-colors text-lg font-semibold">
                                            <EMAIL>
                                        </a>
                                    </p>
                                    <p class="text-gray-500 mt-2">24-hour response guarantee</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media & Hours -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Social Media -->
                    <div class="contact-info-revolutionary">
                        <div class="relative z-10 text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-6">Follow Us</h3>

                            <div class="flex justify-center space-x-4 mb-6">
                                <a href="#" class="w-14 h-14 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-full flex items-center justify-center text-redolence-green hover:bg-redolence-green hover:text-white transition-all contact-interactive">
                                    <i class="fab fa-facebook-f text-xl"></i>
                                </a>
                                <a href="#" class="w-14 h-14 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-full flex items-center justify-center text-redolence-blue hover:bg-redolence-blue hover:text-white transition-all contact-interactive">
                                    <i class="fab fa-instagram text-xl"></i>
                                </a>
                                <a href="#" class="w-14 h-14 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-full flex items-center justify-center text-purple-600 hover:bg-purple-600 hover:text-white transition-all contact-interactive">
                                    <i class="fab fa-twitter text-xl"></i>
                                </a>
                                <a href="#" class="w-14 h-14 bg-gradient-to-br from-red-500/20 to-red-500/10 rounded-full flex items-center justify-center text-red-600 hover:bg-red-600 hover:text-white transition-all contact-interactive">
                                    <i class="fab fa-youtube text-xl"></i>
                                </a>
                            </div>

                            <p class="text-gray-600 text-sm">
                                Latest treatments, tips, and special offers
                            </p>
                        </div>
                    </div>

                    <!-- Emergency Contact -->
                    <div class="contact-info-revolutionary">
                        <div class="relative z-10 text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-red-500/20 to-red-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-6">Emergency</h3>

                            <p class="text-gray-600 mb-4">
                                For post-treatment emergencies or urgent medical concerns
                            </p>

                            <a href="tel:+255781985757" class="inline-flex items-center bg-red-600 text-white px-6 py-3 rounded-full font-semibold hover:bg-red-700 transition-all contact-interactive">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                Emergency Line
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary Map Section -->
<section class="py-32 bg-gradient-to-br from-white to-gray-50">
    <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-16 contact-reveal">
            <h2 class="text-5xl md:text-6xl font-black text-gray-900 mb-8">
                Find Our
                <span class="text-contact-revolutionary">Medical Clinic</span>
            </h2>
            <p class="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Conveniently located in the Professional Medical District with state-of-the-art facilities and ample parking
            </p>
        </div>

        <!-- Map Container -->
        <div class="map-revolutionary aspect-video relative contact-reveal" style="animation-delay: 0.2s;">
            <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3961.8269!2d39.2912!3d-6.7977!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x185c4b0c1e5d7f27%3A0x5e0e5e5e5e5e5e5e!2sCity%20Plaza%2C%20Jamhuri%20Street%2C%20Dar%20es%20Salaam%2C%20Tanzania!5e0!3m2!1sen!2stz!4v1703000000000!5m2!1sen!2stz"
                width="100%"
                height="100%"
                style="border:0;"
                allowfullscreen=""
                loading="lazy"
                referrerpolicy="no-referrer-when-downgrade"
                class="absolute inset-0"
                title="Redolence Medi Aesthetics Location Map">
            </iframe>

            <!-- Map Overlay Info -->
            <div class="absolute bottom-8 left-8 bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-redolence-green/20 max-w-sm">
                <h3 class="text-xl font-bold text-gray-900 mb-2">Redolence Medi Aesthetics</h3>
                <p class="text-gray-600 text-sm mb-4">
                    Medical Plaza, Professional District<br>
                    Healthcare Avenue, Suite 205<br>
                    Dar es Salaam, Tanzania
                </p>
                <div class="flex space-x-3">
                    <a href="https://maps.app.goo.gl/9R4GdECM3wykgtRv8" target="_blank"
                       class="inline-flex items-center bg-redolence-green text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-redolence-green/90 transition-all contact-interactive">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                        </svg>
                        Directions
                    </a>
                    <button onclick="openInGoogleMaps()"
                            class="inline-flex items-center bg-white border border-redolence-green text-redolence-green px-4 py-2 rounded-lg text-sm font-semibold hover:bg-redolence-green/10 transition-all contact-interactive">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        Open
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Minimal JS to preserve behavior and enhance UX -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('revolutionaryContactForm');
  const submitBtn = document.getElementById('submitBtn');
  const submitText = document.getElementById('submitText');
  const messageTextarea = document.getElementById('message');

  if (messageTextarea) {
    const auto = () => { messageTextarea.style.height = 'auto'; messageTextarea.style.height = messageTextarea.scrollHeight + 'px'; };
    auto(); messageTextarea.addEventListener('input', auto);
  }

  if (form && submitBtn && submitText) {
    form.addEventListener('submit', function() {
      submitBtn.disabled = true;
      submitText.textContent = 'Sending...';
    });

    window.addEventListener('pageshow', function() {
      submitBtn.disabled = false;
      submitText.textContent = 'Send message';
    });
  }

  // Smooth scrolling for in-page anchors
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      const target = document.querySelector(this.getAttribute('href'));
      if (target) { e.preventDefault(); target.scrollIntoView({ behavior:'smooth', block:'start' }); }
    });
  });

  // Minor hover/accessibility niceties can be added here if needed
});
</script>


<script>
// Map helper
function openInGoogleMaps() {
  const url = 'https://www.google.com/maps/search/?api=1&query=Medical+Plaza+Professional+District+Healthcare+Avenue+Dar+es+Salaam+Tanzania';
  window.open(url, '_blank');
}
</script>



<?php include __DIR__ . '/includes/footer.php'; ?>