<?php
/**
 * Enhanced Offers Page - Medical Aesthetics Special Promotions
 * Modern design with countdown timers, promotional cards, and clear pricing
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/offer_functions.php';

// Get active offers from database
$dbOffers = getPublicOffers();

// Transform database offers to match the expected format
$offers = [];
foreach ($dbOffers as $dbOffer) {
    // Calculate pricing based on discount percentage - vary base price by offer type
    $basePrices = [
        'NEWPATIENT30' => 200000,
        'ANTIAGE25' => 500000,
        'BODYCONTOUR20' => 800000,
        'SKINREJUV35' => 300000,
        'COUPLES15' => 400000,
        'LASERHAIR40' => 600000
    ];

    $samplePrice = $basePrices[$dbOffer['code']] ?? 150000;
    $discountedPrice = $samplePrice * (1 - ($dbOffer['discount'] / 100));

    // Determine treatments based on offer code
    $treatmentMap = [
        'NEWPATIENT30' => ['Medical Consultation', 'Skin Analysis', 'Treatment Planning'],
        'ANTIAGE25' => ['Botox Treatment', 'Dermal Fillers', 'Skin Rejuvenation'],
        'BODYCONTOUR20' => ['CoolSculpting', 'RF Body Tightening', 'Body Analysis'],
        'SKINREJUV35' => ['HydraFacial', 'Chemical Peel', 'LED Light Therapy'],
        'COUPLES15' => ['Couples Massage', 'Facial Treatment', 'Wellness Consultation'],
        'LASERHAIR40' => ['Laser Hair Removal', 'Skin Preparation', 'Aftercare Kit']
    ];

    $offers[] = [
        'id' => $dbOffer['id'],
        'title' => htmlspecialchars_decode($dbOffer['title'], ENT_QUOTES),
        'subtitle' => 'Medical Aesthetic Special',
        'description' => htmlspecialchars_decode($dbOffer['description'], ENT_QUOTES),
        'original_price' => $samplePrice,
        'discounted_price' => (int)$discountedPrice,
        'discount_percentage' => $dbOffer['discount'],
        'valid_until' => date('Y-m-d', strtotime($dbOffer['valid_to'])),
        'image' => $dbOffer['image'] ?: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp',
        'featured' => in_array($dbOffer['code'], ['NEWPATIENT30', 'SKINREJUV35']), // Feature new patient and skin rejuvenation offers
        'treatments_included' => $treatmentMap[$dbOffer['code']] ?? ['Medical Consultation', 'Treatment Session', 'Follow-up Care'],
        'terms' => 'Valid until ' . date('F j, Y', strtotime($dbOffer['valid_to'])) . '. Use code: ' . $dbOffer['code'] . '. Terms and conditions apply.',
        'code' => $dbOffer['code'],
        'usage_count' => $dbOffer['usage_count'] ?? 0
    ];
}

// If no offers in database, show a default message
if (empty($offers)) {
    $offers = [
    [
        'id' => 1,
        'title' => 'New Client Special',
        'subtitle' => 'First Treatment Discount',
        'description' => 'Get 30% off your first medical aesthetics treatment. Perfect for new clients looking to experience our premium services.',
        'original_price' => 150000,
        'discounted_price' => 105000,
        'discount_percentage' => 30,
        'valid_until' => '2024-12-31',
        'image' => 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp',
        'featured' => true,
        'treatments_included' => ['Facial Rejuvenation', 'Skin Analysis', 'Consultation'],
        'terms' => 'Valid for new clients only. Cannot be combined with other offers.'
    ],
    [
        'id' => 2,
        'title' => 'Holiday Glow Package',
        'subtitle' => 'Complete Skin Transformation',
        'description' => 'A comprehensive 3-session package including advanced facial treatments, skin rejuvenation, and personalized skincare consultation.',
        'original_price' => 450000,
        'discounted_price' => 315000,
        'discount_percentage' => 30,
        'valid_until' => '2024-11-30',
        'image' => 'https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp',
        'featured' => false,
        'treatments_included' => ['HydraFacial', 'Chemical Peel', 'LED Light Therapy', 'Skincare Kit'],
        'terms' => 'Package must be used within 6 months. Advance booking required.'
    ],
    [
        'id' => 3,
        'title' => 'Couples Retreat',
        'subtitle' => 'Relaxation for Two',
        'description' => 'Enjoy a romantic spa experience with your partner. Includes side-by-side treatments and complimentary refreshments.',
        'original_price' => 300000,
        'discounted_price' => 240000,
        'discount_percentage' => 20,
        'valid_until' => '2024-12-15',
        'image' => 'https://images.unsplash.com/photo-**********-4ab6ce6db874?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp',
        'featured' => false,
        'treatments_included' => ['Couples Facial', 'Relaxation Massage', 'Refreshments'],
        'terms' => 'Advance booking required. Subject to availability.'
    ],
        [
            'id' => 'default-1',
            'title' => 'Coming Soon',
            'subtitle' => 'Exciting Offers Ahead',
            'description' => 'We are preparing amazing offers for our medical aesthetics treatments. Stay tuned for exclusive deals and promotions.',
            'original_price' => 150000,
            'discounted_price' => 150000,
            'discount_percentage' => 0,
            'valid_until' => date('Y-m-d', strtotime('+30 days')),
            'image' => 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=85&fm=webp',
            'featured' => true,
            'treatments_included' => ['Medical Consultation', 'Treatment Planning'],
            'terms' => 'Contact us for more information about upcoming offers.',
            'code' => 'COMING-SOON',
            'usage_count' => 0
        ]
    ];
}

$pageTitle = "Special Offers & Promotions - Redolence Medi Aesthetics";
$pageDescription = "Discover exclusive offers and promotions on premium medical aesthetics treatments at Redolence. Limited-time deals on facial rejuvenation, skin treatments, and wellness packages.";

include __DIR__ . '/includes/header.php';
?>

<!-- AOS (Animate On Scroll) Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<!-- Enhanced Offers Page Styles -->
<style>
/* Modern Typography */
.text-luxury {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
}

.text-medical {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
}

.text-display {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
}

/* Enhanced Gradient Effects */
.gradient-text {
    background: linear-gradient(135deg, #49a75c 0%, #5894d2 50%, #d4af37 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Offer Card Animations */
.offer-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.offer-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.offer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 1.5rem;
}

.offer-card:hover::before {
    opacity: 1;
}

/* Countdown Timer Styles */
.countdown-timer {
    background: linear-gradient(135deg, #49a75c, #5894d2);
    border-radius: 1rem;
    padding: 1rem;
    color: white;
    text-align: center;
}

.countdown-number {
    font-size: 1.5rem;
    font-weight: 700;
    display: block;
}

.countdown-label {
    font-size: 0.75rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Price Display */
.price-original {
    position: relative;
}

.price-original::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #ef4444;
    transform: translateY(-50%);
}

/* Floating Elements */
.floating-element {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Enhanced Glass Effect */
.glass-card {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Pulse Animation for Featured Offers */
.pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(73, 167, 92, 0.3); }
    50% { box-shadow: 0 0 40px rgba(73, 167, 92, 0.6); }
}

/* Ultra Modern Hero Section Animations */
@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes gradient-shift-reverse {
    0%, 100% { background-position: 100% 50%; }
    50% { background-position: 0% 50%; }
}

@keyframes float-slow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float-medium {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-180deg); }
}

@keyframes float-fast {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-25px) rotate(360deg); }
}

@keyframes particle-float {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) translateX(10px) rotate(90deg) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-40px) translateX(-5px) rotate(180deg) scale(0.8);
        opacity: 0.4;
    }
    75% {
        transform: translateY(-15px) translateX(-15px) rotate(270deg) scale(1.1);
        opacity: 0.9;
    }
}

@keyframes text-shimmer {
    0%, 100% { background-position: -200% center; }
    50% { background-position: 200% center; }
}

@keyframes gradient-text {
    0%, 100% {
        background-position: 0% 50%;
        filter: hue-rotate(0deg);
    }
    50% {
        background-position: 100% 50%;
        filter: hue-rotate(20deg);
    }
}

@keyframes grid-pulse {
    0%, 100% { opacity: 0.02; }
    50% { opacity: 0.05; }
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    0%, 50% { border-color: rgba(73, 167, 92, 0.8); }
    51%, 100% { border-color: transparent; }
}

/* Ultra Modern Hero Section Styles */
.animate-gradient-shift {
    background-size: 400% 400%;
    animation: gradient-shift 8s ease-in-out infinite;
}

.animate-gradient-shift-reverse {
    background-size: 400% 400%;
    animation: gradient-shift-reverse 10s ease-in-out infinite;
}

.animate-float-slow {
    animation: float-slow 20s ease-in-out infinite;
}

.animate-float-medium {
    animation: float-medium 15s ease-in-out infinite;
}

.animate-float-fast {
    animation: float-fast 12s ease-in-out infinite;
}

.animate-particle-float {
    animation: particle-float 8s ease-in-out infinite;
}

.animate-text-shimmer {
    background-size: 400% 100%;
    animation: text-shimmer 3s ease-in-out infinite;
}

.animate-gradient-text {
    background-size: 300% 300%;
    animation: gradient-text 4s ease-in-out infinite;
}

.typing-text {
    overflow: hidden;
    border-right: 2px solid transparent;
    animation: typing 3s steps(30, end), blink 1s infinite;
    display: inline-block;
}

/* Contact Cards */
.contact-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 2rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
}

.contact-card:hover::before {
    left: 100%;
}

.contact-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.contact-card-inner {
    position: relative;
    z-index: 2;
    text-align: center;
}

.contact-icon-container {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.contact-card:hover .contact-icon-container {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.hero-cta-primary {
    background: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    border: none;
    border-radius: 16px;
    padding: 1rem 2rem;
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
    display: inline-flex;
    align-items: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(73, 167, 92, 0.3);
}

.hero-cta-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.4);
    filter: brightness(1.1);
}

.hero-cta-primary:active {
    transform: translateY(-1px) scale(1.02);
}

/* Mobile Responsive Fixes */
@media (max-width: 768px) {
    .text-6xl {
        font-size: 2.5rem !important;
        line-height: 1.1 !important;
    }

    .text-8xl {
        font-size: 3rem !important;
        line-height: 1.1 !important;
    }

    .text-2xl {
        font-size: 1.25rem !important;
        line-height: 1.4 !important;
    }

    .text-3xl {
        font-size: 1.5rem !important;
        line-height: 1.3 !important;
    }

    .contact-card {
        padding: 1.5rem !important;
        margin-bottom: 1rem !important;
    }

    .contact-icon-container {
        width: 3rem !important;
        height: 3rem !important;
        margin-bottom: 0.75rem !important;
    }

    .hero-cta-primary {
        padding: 0.875rem 1.5rem !important;
        font-size: 1rem !important;
        width: 100% !important;
        justify-content: center !important;
        margin-bottom: 1rem !important;
    }

    .min-h-screen {
        padding: 2rem 0 !important;
        min-height: auto !important;
    }

    .max-w-7xl {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}
</style>

<!-- Ultra Modern Hero Section - Offers Page -->
<section class="relative min-h-screen md:min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 py-8 md:py-0">
    <!-- Advanced Background Effects -->
    <div class="absolute inset-0">
        <!-- Dynamic Gradient Mesh -->
        <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/20 via-transparent to-redolence-blue/20 animate-gradient-shift"></div>
        <div class="absolute inset-0 bg-gradient-to-tl from-purple-500/10 via-transparent to-emerald-500/10 animate-gradient-shift-reverse"></div>

        <!-- Floating Orbs -->
        <div class="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-redolence-green/30 to-emerald-400/30 rounded-full blur-3xl animate-float-slow"></div>
        <div class="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-redolence-blue/30 to-cyan-400/30 rounded-full blur-3xl animate-float-medium"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-float-fast"></div>

        <!-- Animated Particles -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="particle absolute top-1/4 left-1/4 w-2 h-2 bg-redolence-green/60 rounded-full animate-particle-float"></div>
            <div class="particle absolute top-3/4 right-1/4 w-3 h-3 bg-redolence-blue/60 rounded-full animate-particle-float" style="animation-delay: 1s;"></div>
            <div class="particle absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-purple-400/60 rounded-full animate-particle-float" style="animation-delay: 2s;"></div>
            <div class="particle absolute bottom-1/4 left-1/2 w-2.5 h-2.5 bg-emerald-400/60 rounded-full animate-particle-float" style="animation-delay: 3s;"></div>
            <div class="particle absolute top-1/3 right-1/3 w-2 h-2 bg-cyan-400/60 rounded-full animate-particle-float" style="animation-delay: 4s;"></div>
        </div>

        <!-- Grid Pattern -->
        <div class="absolute inset-0 opacity-[0.02]" style="background-image: linear-gradient(rgba(73, 167, 92, 0.5) 1px, transparent 1px), linear-gradient(90deg, rgba(73, 167, 92, 0.5) 1px, transparent 1px); background-size: 80px 80px; animation: grid-pulse 4s ease-in-out infinite;"></div>
    </div>

    <!-- Main Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 md:px-6 text-center">
        <!-- Premium Badge -->
        <div class="mb-8 md:mb-12" data-aos="fade-up" data-aos-duration="1000">
            <div class="inline-flex items-center bg-white/10 backdrop-blur-md px-4 md:px-8 py-2 md:py-4 rounded-full border border-white/20 shadow-2xl hover:scale-105 transition-all duration-500">
                <div class="w-2 md:w-3 h-2 md:h-3 bg-redolence-green rounded-full mr-2 md:mr-3 animate-pulse"></div>
                <span class="font-bold text-white tracking-wider text-xs md:text-sm uppercase">Exclusive Medical Offers</span>
                <div class="w-2 md:w-3 h-2 md:h-3 bg-redolence-blue rounded-full ml-2 md:ml-3 animate-pulse" style="animation-delay: 0.5s;"></div>
            </div>
        </div>

        <!-- Dynamic Title -->
        <div class="mb-8 md:mb-12" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="200">
            <h1 class="text-4xl md:text-6xl lg:text-8xl font-black mb-4 md:mb-6 leading-tight">
                <span class="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent animate-text-shimmer">
                    Premium
                </span>
                <br>
                <span class="bg-gradient-to-r from-redolence-green via-emerald-400 to-redolence-blue bg-clip-text text-transparent animate-gradient-text">
                    Offers
                </span>
            </h1>

            <div class="relative">
                <p class="text-lg md:text-2xl lg:text-3xl text-gray-300 font-light leading-relaxed mb-6 md:mb-8">
                    Discover <span class="text-redolence-green font-semibold">Exclusive Treatments</span> & Packages
                    <br class="hidden md:block">
                    <span class="typing-text">Designed to Enhance Your Natural Beauty</span>
                </p>

                <!-- Decorative Elements - Hidden on mobile -->
                <div class="hidden md:block absolute -top-4 -right-8 w-8 h-8 bg-gradient-to-r from-redolence-green/30 to-emerald-400/30 rounded-full blur-sm animate-pulse"></div>
                <div class="hidden md:block absolute -bottom-4 -left-8 w-6 h-6 bg-gradient-to-r from-redolence-blue/30 to-cyan-400/30 rounded-full blur-sm animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <!-- Enhanced Offer Highlights -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto mb-12 md:mb-16" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="400">
            <!-- Offer 1 -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-redolence-green to-emerald-500">
                        <svg class="w-6 md:w-8 h-6 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg md:text-xl font-bold text-white mb-2 group-hover:text-redolence-green transition-colors duration-300">
                        Up to 30% Off
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Premium treatment packages with significant savings
                    </p>
                    <div class="text-redolence-green font-bold text-lg">
                        Limited Time
                    </div>
                </div>
            </div>

            <!-- Offer 2 -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-redolence-blue to-cyan-500">
                        <svg class="w-6 md:w-8 h-6 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg md:text-xl font-bold text-white mb-2 group-hover:text-redolence-blue transition-colors duration-300">
                        Free Consultation
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Complimentary expert consultation with every package
                    </p>
                    <div class="text-redolence-blue font-bold text-lg">
                        Always Included
                    </div>
                </div>
            </div>

            <!-- Offer 3 -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-purple-500 to-pink-500">
                        <svg class="w-6 md:w-8 h-6 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg md:text-xl font-bold text-white mb-2 group-hover:text-purple-400 transition-colors duration-300">
                        VIP Treatment
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Exclusive access to premium medical aesthetics services
                    </p>
                    <div class="text-purple-400 font-bold text-lg">
                        Members Only
                    </div>
                </div>
            </div>
        </div>

        <!-- Call-to-Action -->
        <div class="text-center px-4" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="600">
            <div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4 mb-8 md:mb-0">
                <button onclick="document.getElementById('offers').scrollIntoView({behavior: 'smooth'})" class="hero-cta-primary w-full md:w-auto">
                    <span>View All Offers</span>
                    <svg class="w-4 md:w-5 h-4 md:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <div class="text-gray-400 text-xs md:text-sm">
                    <div class="flex items-center justify-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-center">Limited time exclusive offers</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator - Hidden on mobile -->
        <div class="hidden md:block absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div class="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>
    </div>
</section>

<!-- Offers Section -->
<section id="offers" class="py-20 bg-gradient-to-br from-redolence-gray to-white">
    <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-display font-bold text-gray-900 mb-6">
                <span class="gradient-text">Exclusive Offers</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Take advantage of our limited-time promotions on premium medical aesthetics treatments. Each offer is carefully crafted to provide exceptional value while maintaining our high standards of care.
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-redolence-green to-redolence-blue mx-auto rounded-full mt-6"></div>
        </div>

        <!-- Offers Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            <?php foreach ($offers as $index => $offer): ?>
                <div class="offer-card relative bg-white rounded-3xl shadow-xl overflow-hidden <?= $offer['featured'] ? 'pulse-glow' : '' ?> <?= $index === 0 ? 'lg:col-span-2' : '' ?>">
                    <?php if ($offer['featured']): ?>
                        <div class="absolute top-6 left-6 z-20">
                            <span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                                ⭐ Featured Offer
                            </span>
                        </div>
                    <?php endif; ?>

                    <div class="<?= $index === 0 ? 'md:flex' : '' ?>">
                        <!-- Image Section -->
                        <div class="<?= $index === 0 ? 'md:w-1/2' : '' ?> relative h-64 <?= $index === 0 ? 'md:h-auto' : '' ?>">
                            <img src="<?= $offer['image'] ?>"
                                 alt="<?= htmlspecialchars($offer['title']) ?>"
                                 class="w-full h-full object-cover">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

                            <!-- Discount Badge -->
                            <div class="absolute bottom-4 right-4">
                                <div class="bg-red-500 text-white px-4 py-2 rounded-full font-bold text-lg shadow-lg">
                                    <?= $offer['discount_percentage'] ?>% OFF
                                </div>
                            </div>
                        </div>

                        <!-- Content Section -->
                        <div class="<?= $index === 0 ? 'md:w-1/2' : '' ?> p-8">
                            <div class="mb-4">
                                <h3 class="text-2xl font-display font-bold text-gray-900 mb-2">
                                    <?= htmlspecialchars($offer['title']) ?>
                                </h3>
                                <p class="text-redolence-blue font-semibold text-lg">
                                    <?= htmlspecialchars($offer['subtitle']) ?>
                                </p>
                            </div>

                            <p class="text-gray-600 leading-relaxed mb-6">
                                <?= htmlspecialchars($offer['description']) ?>
                            </p>

                            <!-- Treatments Included -->
                            <div class="mb-6">
                                <h4 class="font-semibold text-gray-900 mb-3">Treatments Included:</h4>
                                <div class="flex flex-wrap gap-2">
                                    <?php foreach ($offer['treatments_included'] as $treatment): ?>
                                        <span class="bg-redolence-green/10 text-redolence-green px-3 py-1 rounded-full text-sm font-medium">
                                            <?= htmlspecialchars($treatment) ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <!-- Pricing -->
                            <div class="mb-6">
                                <div class="flex items-center space-x-4 mb-2">
                                    <span class="price-original text-xl text-gray-500 font-medium">
                                        TSH <?= number_format($offer['original_price']) ?>
                                    </span>
                                    <span class="text-3xl font-bold text-redolence-green">
                                        TSH <?= number_format($offer['discounted_price']) ?>
                                    </span>
                                </div>
                                <p class="text-sm text-gray-500">
                                    You save TSH <?= number_format($offer['original_price'] - $offer['discounted_price']) ?>
                                </p>
                            </div>

                            <!-- Countdown Timer -->
                            <div class="countdown-timer mb-6" data-end-date="<?= $offer['valid_until'] ?>">
                                <div class="text-sm font-semibold mb-2">Offer expires in:</div>
                                <div class="grid grid-cols-4 gap-2 text-center">
                                    <div>
                                        <span class="countdown-number days">00</span>
                                        <div class="countdown-label">Days</div>
                                    </div>
                                    <div>
                                        <span class="countdown-number hours">00</span>
                                        <div class="countdown-label">Hours</div>
                                    </div>
                                    <div>
                                        <span class="countdown-number minutes">00</span>
                                        <div class="countdown-label">Minutes</div>
                                    </div>
                                    <div>
                                        <span class="countdown-number seconds">00</span>
                                        <div class="countdown-label">Seconds</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-3">
                                <a href="<?= getBasePath() ?>/customer/book?offer=<?= $offer['id'] ?>"
                                   class="flex-1 bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                                    Book This Offer
                                </a>
                                <button onclick="showOfferDetails(<?= $offer['id'] ?>)"
                                        class="bg-white hover:bg-gray-50 text-redolence-blue border-2 border-redolence-blue py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                                    Details
                                </button>
                            </div>

                            <!-- Terms -->
                            <div class="mt-4 text-xs text-gray-500">
                                <?= htmlspecialchars($offer['terms']) ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
            <div class="glass-card rounded-3xl p-8 max-w-4xl mx-auto">
                <h3 class="text-3xl font-display font-bold text-gray-900 mb-4">
                    Ready to Transform Your Beauty?
                </h3>
                <p class="text-xl text-gray-600 mb-8">
                    Don't miss out on these exclusive offers. Book your consultation today and discover the perfect treatment for your needs.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?= getBasePath() ?>/customer/book"
                       class="inline-flex items-center justify-center bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                        </svg>
                        Book Consultation
                    </a>
                    <a href="<?= getBasePath() ?>/contact"
                       class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-blue border-2 border-redolence-blue px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        Ask Questions
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Offer Details Modal -->
<div id="offerDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-3xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center rounded-t-3xl">
            <h2 id="modalOfferTitle" class="text-2xl font-display font-bold text-gray-900"></h2>
            <button onclick="closeOfferModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <div id="modalOfferContent">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<script>
// Countdown Timer Functionality
function initCountdownTimers() {
    const timers = document.querySelectorAll('.countdown-timer');

    timers.forEach(timer => {
        const endDate = new Date(timer.dataset.endDate + 'T23:59:59').getTime();

        function updateTimer() {
            const now = new Date().getTime();
            const distance = endDate - now;

            if (distance < 0) {
                timer.innerHTML = '<div class="text-center text-red-500 font-bold">Offer Expired</div>';
                return;
            }

            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            timer.querySelector('.days').textContent = days.toString().padStart(2, '0');
            timer.querySelector('.hours').textContent = hours.toString().padStart(2, '0');
            timer.querySelector('.minutes').textContent = minutes.toString().padStart(2, '0');
            timer.querySelector('.seconds').textContent = seconds.toString().padStart(2, '0');
        }

        updateTimer();
        setInterval(updateTimer, 1000);
    });
}

// Offer Details Modal
const offers = <?= json_encode($offers) ?>;

function showOfferDetails(offerId) {
    const offer = offers.find(o => o.id === offerId);
    if (!offer) return;

    document.getElementById('modalOfferTitle').textContent = offer.title;

    const content = `
        <div class="space-y-6">
            <img src="${offer.image}" alt="${offer.title}" class="w-full h-64 object-cover rounded-2xl">

            <div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">${offer.subtitle}</h3>
                <p class="text-gray-600 leading-relaxed">${offer.description}</p>
            </div>

            <div>
                <h4 class="font-semibold text-gray-900 mb-3">What's Included:</h4>
                <ul class="space-y-2">
                    ${offer.treatments_included.map(treatment => `
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-redolence-green mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            ${treatment}
                        </li>
                    `).join('')}
                </ul>
            </div>

            <div class="bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 rounded-2xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <div class="text-sm text-gray-500">Original Price</div>
                        <div class="text-xl text-gray-500 line-through">TSH ${offer.original_price.toLocaleString()}</div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-500">Special Price</div>
                        <div class="text-3xl font-bold text-redolence-green">TSH ${offer.discounted_price.toLocaleString()}</div>
                    </div>
                </div>
                <div class="text-center">
                    <span class="bg-red-500 text-white px-4 py-2 rounded-full font-bold">
                        Save ${offer.discount_percentage}% - TSH ${(offer.original_price - offer.discounted_price).toLocaleString()}
                    </span>
                </div>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-2xl p-4">
                <h4 class="font-semibold text-yellow-800 mb-2">Terms & Conditions:</h4>
                <p class="text-yellow-700 text-sm">${offer.terms}</p>
            </div>

            <div class="flex gap-3 pt-4">
                <a href="<?= getBasePath() ?>/customer/book?offer=${offer.id}"
                   class="flex-1 bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                    Book This Offer Now
                </a>
                <button onclick="closeOfferModal()"
                        class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-6 rounded-xl font-semibold transition-all duration-300">
                    Close
                </button>
            </div>
        </div>
    `;

    document.getElementById('modalOfferContent').innerHTML = content;
    document.getElementById('offerDetailsModal').classList.remove('hidden');
}

function closeOfferModal() {
    document.getElementById('offerDetailsModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('offerDetailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeOfferModal();
    }
});

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initCountdownTimers();

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Add intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe offer cards for scroll animations
    document.querySelectorAll('.offer-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});

// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 1000,
    easing: 'ease-out-cubic',
    once: true,
    offset: 100,
    delay: 0
});

// Enhanced Contact Card Interactions
document.querySelectorAll('.contact-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-15px) scale(1.03)';
        this.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.25)';
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
    });
});

// Typing Effect for Hero Text
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';

    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    type();
}

// Initialize typing effect when hero comes into view
const typingObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const typingText = entry.target.querySelector('.typing-text');
            if (typingText && !typingText.classList.contains('typed')) {
                typingText.classList.add('typed');
                const originalText = typingText.textContent;
                typeWriter(typingText, originalText, 80);
            }
        }
    });
}, { threshold: 0.3 });

// Observe hero section for typing effect
const heroSection = document.querySelector('section');
if (heroSection) {
    typingObserver.observe(heroSection);
}

// Enhanced Hero CTA Button
document.querySelectorAll('.hero-cta-primary').forEach(button => {
    button.addEventListener('click', function(e) {
        // Create ripple effect
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        this.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 800);
    });
});

// Add ripple effect CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        transform: scale(0);
        animation: ripple-animation 0.8s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
