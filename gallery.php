<?php
/**
 * Enhanced Gallery Page - Medical Aesthetics Results & Clinic Gallery
 * Modern grid layout with lightbox functionality and before/after comparisons
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/gallery_functions.php';

// Get gallery data from database
$galleryItems = getGalleryItems();

$pageTitle = "Gallery - Treatment Results & Clinic Tour - Redolence Medi Aesthetics";
$pageDescription = "View our impressive before and after treatment results and take a virtual tour of our modern medical aesthetics clinic. See the transformations achieved with our professional treatments.";

include __DIR__ . '/includes/header.php';
?>

<!-- AOS (Animate On Scroll) Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<!-- Enhanced Gallery Styles -->
<style>
/* Modern Typography */
.text-luxury {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
}

.text-medical {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
}

.text-display {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
}

/* Enhanced Gradient Effects */
.gradient-text {
    background: linear-gradient(135deg, #49a75c 0%, #5894d2 50%, #d4af37 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Gallery Grid Animations */
.gallery-item {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.gallery-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 1.5rem;
}

.gallery-item:hover::before {
    opacity: 1;
}

/* Before/After Slider */
.before-after-container {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
}

.before-after-slider {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
    cursor: ew-resize;
}

.before-image, .after-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.after-image {
    clip-path: polygon(50% 0%, 100% 0%, 100% 100%, 50% 100%);
    transition: clip-path 0.3s ease;
}

.slider-handle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: white;
    border: 3px solid #49a75c;
    border-radius: 50%;
    cursor: ew-resize;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.slider-handle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: #49a75c;
    border-radius: 50%;
}

.slider-line {
    position: absolute;
    top: 0;
    left: 50%;
    width: 2px;
    height: 100%;
    background: white;
    transform: translateX(-50%);
    z-index: 5;
}

/* Lightbox Styles */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    max-width: 90vw;
    max-height: 90vh;
    position: relative;
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.lightbox.active .lightbox-content {
    transform: scale(1);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.lightbox-close:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* Filter Buttons */
.filter-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.filter-btn:hover::before {
    left: 100%;
}

.filter-btn.active {
    background: linear-gradient(135deg, #49a75c, #5894d2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
}

/* Floating Elements */
.floating-element {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Enhanced Glass Effect */
.glass-card {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Ultra Modern Hero Section Animations */
@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes gradient-shift-reverse {
    0%, 100% { background-position: 100% 50%; }
    50% { background-position: 0% 50%; }
}

@keyframes float-slow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float-medium {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-180deg); }
}

@keyframes float-fast {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-25px) rotate(360deg); }
}

@keyframes particle-float {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) translateX(10px) rotate(90deg) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-40px) translateX(-5px) rotate(180deg) scale(0.8);
        opacity: 0.4;
    }
    75% {
        transform: translateY(-15px) translateX(-15px) rotate(270deg) scale(1.1);
        opacity: 0.9;
    }
}

@keyframes text-shimmer {
    0%, 100% { background-position: -200% center; }
    50% { background-position: 200% center; }
}

@keyframes gradient-text {
    0%, 100% {
        background-position: 0% 50%;
        filter: hue-rotate(0deg);
    }
    50% {
        background-position: 100% 50%;
        filter: hue-rotate(20deg);
    }
}

@keyframes grid-pulse {
    0%, 100% { opacity: 0.02; }
    50% { opacity: 0.05; }
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    0%, 50% { border-color: rgba(73, 167, 92, 0.8); }
    51%, 100% { border-color: transparent; }
}

/* Ultra Modern Hero Section Styles */
.animate-gradient-shift {
    background-size: 400% 400%;
    animation: gradient-shift 8s ease-in-out infinite;
}

.animate-gradient-shift-reverse {
    background-size: 400% 400%;
    animation: gradient-shift-reverse 10s ease-in-out infinite;
}

.animate-float-slow {
    animation: float-slow 20s ease-in-out infinite;
}

.animate-float-medium {
    animation: float-medium 15s ease-in-out infinite;
}

.animate-float-fast {
    animation: float-fast 12s ease-in-out infinite;
}

.animate-particle-float {
    animation: particle-float 8s ease-in-out infinite;
}

.animate-text-shimmer {
    background-size: 400% 100%;
    animation: text-shimmer 3s ease-in-out infinite;
}

.animate-gradient-text {
    background-size: 300% 300%;
    animation: gradient-text 4s ease-in-out infinite;
}

.typing-text {
    overflow: hidden;
    border-right: 2px solid transparent;
    animation: typing 3s steps(30, end), blink 1s infinite;
    display: inline-block;
}

/* Contact Cards */
.contact-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 2rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
}

.contact-card:hover::before {
    left: 100%;
}

.contact-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.contact-card-inner {
    position: relative;
    z-index: 2;
    text-align: center;
}

.contact-icon-container {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.contact-card:hover .contact-icon-container {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.hero-cta-primary {
    background: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    border: none;
    border-radius: 16px;
    padding: 1rem 2rem;
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
    display: inline-flex;
    align-items: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(73, 167, 92, 0.3);
}

.hero-cta-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 20px 40px rgba(73, 167, 92, 0.4);
    filter: brightness(1.1);
}

.hero-cta-primary:active {
    transform: translateY(-1px) scale(1.02);
}

/* Mobile Responsive Fixes */
@media (max-width: 768px) {
    .text-6xl {
        font-size: 2.5rem !important;
        line-height: 1.1 !important;
    }

    .text-8xl {
        font-size: 3rem !important;
        line-height: 1.1 !important;
    }

    .text-2xl {
        font-size: 1.25rem !important;
        line-height: 1.4 !important;
    }

    .text-3xl {
        font-size: 1.5rem !important;
        line-height: 1.3 !important;
    }

    .contact-card {
        padding: 1.5rem !important;
        margin-bottom: 1rem !important;
    }

    .contact-icon-container {
        width: 3rem !important;
        height: 3rem !important;
        margin-bottom: 0.75rem !important;
    }

    .hero-cta-primary {
        padding: 0.875rem 1.5rem !important;
        font-size: 1rem !important;
        width: 100% !important;
        justify-content: center !important;
        margin-bottom: 1rem !important;
    }

    .min-h-screen {
        padding: 2rem 0 !important;
        min-height: auto !important;
    }

    .max-w-7xl {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
}
</style>

<!-- Ultra Modern Hero Section - Gallery Page -->
<section class="relative min-h-screen md:min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 py-8 md:py-0">
    <!-- Advanced Background Effects -->
    <div class="absolute inset-0">
        <!-- Dynamic Gradient Mesh -->
        <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/20 via-transparent to-redolence-blue/20 animate-gradient-shift"></div>
        <div class="absolute inset-0 bg-gradient-to-tl from-purple-500/10 via-transparent to-emerald-500/10 animate-gradient-shift-reverse"></div>

        <!-- Floating Orbs -->
        <div class="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-redolence-green/30 to-emerald-400/30 rounded-full blur-3xl animate-float-slow"></div>
        <div class="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-redolence-blue/30 to-cyan-400/30 rounded-full blur-3xl animate-float-medium"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-float-fast"></div>

        <!-- Animated Particles -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="particle absolute top-1/4 left-1/4 w-2 h-2 bg-redolence-green/60 rounded-full animate-particle-float"></div>
            <div class="particle absolute top-3/4 right-1/4 w-3 h-3 bg-redolence-blue/60 rounded-full animate-particle-float" style="animation-delay: 1s;"></div>
            <div class="particle absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-purple-400/60 rounded-full animate-particle-float" style="animation-delay: 2s;"></div>
            <div class="particle absolute bottom-1/4 left-1/2 w-2.5 h-2.5 bg-emerald-400/60 rounded-full animate-particle-float" style="animation-delay: 3s;"></div>
            <div class="particle absolute top-1/3 right-1/3 w-2 h-2 bg-cyan-400/60 rounded-full animate-particle-float" style="animation-delay: 4s;"></div>
        </div>

        <!-- Grid Pattern -->
        <div class="absolute inset-0 opacity-[0.02]" style="background-image: linear-gradient(rgba(73, 167, 92, 0.5) 1px, transparent 1px), linear-gradient(90deg, rgba(73, 167, 92, 0.5) 1px, transparent 1px); background-size: 80px 80px; animation: grid-pulse 4s ease-in-out infinite;"></div>
    </div>

    <!-- Main Content -->
    <div class="relative z-10 max-w-7xl mx-auto px-4 md:px-6 text-center">
        <!-- Premium Badge -->
        <div class="mb-8 md:mb-12" data-aos="fade-up" data-aos-duration="1000">
            <div class="inline-flex items-center bg-white/10 backdrop-blur-md px-4 md:px-8 py-2 md:py-4 rounded-full border border-white/20 shadow-2xl hover:scale-105 transition-all duration-500">
                <div class="w-2 md:w-3 h-2 md:h-3 bg-redolence-green rounded-full mr-2 md:mr-3 animate-pulse"></div>
                <span class="font-bold text-white tracking-wider text-xs md:text-sm uppercase">Medical Aesthetics Gallery</span>
                <div class="w-2 md:w-3 h-2 md:h-3 bg-redolence-blue rounded-full ml-2 md:ml-3 animate-pulse" style="animation-delay: 0.5s;"></div>
            </div>
        </div>

        <!-- Dynamic Title -->
        <div class="mb-8 md:mb-12" data-aos="fade-up" data-aos-duration="1200" data-aos-delay="200">
            <h1 class="text-4xl md:text-6xl lg:text-8xl font-black mb-4 md:mb-6 leading-tight">
                <span class="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent animate-text-shimmer">
                    Visual
                </span>
                <br>
                <span class="bg-gradient-to-r from-redolence-green via-emerald-400 to-redolence-blue bg-clip-text text-transparent animate-gradient-text">
                    Results
                </span>
            </h1>

            <div class="relative">
                <p class="text-lg md:text-2xl lg:text-3xl text-gray-300 font-light leading-relaxed mb-6 md:mb-8">
                    Witness <span class="text-redolence-green font-semibold">Real Transformations</span> & Our Clinic
                    <br class="hidden md:block">
                    <span class="typing-text">Before & After Success Stories</span>
                </p>

                <!-- Decorative Elements - Hidden on mobile -->
                <div class="hidden md:block absolute -top-4 -right-8 w-8 h-8 bg-gradient-to-r from-redolence-green/30 to-emerald-400/30 rounded-full blur-sm animate-pulse"></div>
                <div class="hidden md:block absolute -bottom-4 -left-8 w-6 h-6 bg-gradient-to-r from-redolence-blue/30 to-cyan-400/30 rounded-full blur-sm animate-pulse" style="animation-delay: 1s;"></div>
            </div>
        </div>

        <!-- Enhanced Gallery Highlights -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto mb-12 md:mb-16" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="400">
            <!-- Real Results -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-redolence-green to-emerald-500">
                        <svg class="w-6 md:w-8 h-6 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg md:text-xl font-bold text-white mb-2 group-hover:text-redolence-green transition-colors duration-300">
                        Real Results
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Authentic before & after transformations from our patients
                    </p>
                    <div class="text-redolence-green font-bold text-lg">
                        500+ Cases
                    </div>
                </div>
            </div>

            <!-- Expert Care -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-redolence-blue to-cyan-500">
                        <svg class="w-6 md:w-8 h-6 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg md:text-xl font-bold text-white mb-2 group-hover:text-redolence-blue transition-colors duration-300">
                        Expert Care
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        Board-certified specialists with years of experience
                    </p>
                    <div class="text-redolence-blue font-bold text-lg">
                        100% Safe
                    </div>
                </div>
            </div>

            <!-- Premium Facility -->
            <div class="contact-card group">
                <div class="contact-card-inner">
                    <div class="contact-icon-container bg-gradient-to-br from-purple-500 to-pink-500">
                        <svg class="w-6 md:w-8 h-6 md:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg md:text-xl font-bold text-white mb-2 group-hover:text-purple-400 transition-colors duration-300">
                        Premium Facility
                    </h3>
                    <p class="text-gray-300 mb-4 text-sm leading-relaxed">
                        State-of-the-art medical aesthetics clinic environment
                    </p>
                    <div class="text-purple-400 font-bold text-lg">
                        Luxury Setting
                    </div>
                </div>
            </div>
        </div>

        <!-- Call-to-Action -->
        <div class="text-center px-4" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="600">
            <div class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4 mb-8 md:mb-0">
                <button onclick="document.getElementById('gallery').scrollIntoView({behavior: 'smooth'})" class="hero-cta-primary w-full md:w-auto">
                    <span>View Gallery</span>
                    <svg class="w-4 md:w-5 h-4 md:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <div class="text-gray-400 text-xs md:text-sm">
                    <div class="flex items-center justify-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-center">Real patient transformations</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator - Hidden on mobile -->
        <div class="hidden md:block absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div class="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Section -->
<section id="gallery" class="py-20 bg-gradient-to-br from-redolence-gray to-white">
    <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-display font-bold text-gray-900 mb-6">
                <span class="gradient-text">Results Gallery</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Witness the remarkable transformations achieved through our advanced medical aesthetics treatments. Each image tells a story of renewed confidence and enhanced natural beauty.
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-redolence-green to-redolence-blue mx-auto rounded-full mt-6"></div>
        </div>

        <!-- Filter Buttons -->
        <div class="flex flex-wrap justify-center gap-4 mb-12">
            <button class="filter-btn active px-6 py-3 rounded-xl font-semibold transition-all duration-300 bg-white border-2 border-redolence-green text-redolence-green hover:bg-redolence-green hover:text-white relative overflow-hidden" data-filter="all">
                All Gallery
            </button>
            <button class="filter-btn px-6 py-3 rounded-xl font-semibold transition-all duration-300 bg-white border-2 border-redolence-green text-redolence-green hover:bg-redolence-green hover:text-white relative overflow-hidden" data-filter="before-after">
                Before & After
            </button>
            <button class="filter-btn px-6 py-3 rounded-xl font-semibold transition-all duration-300 bg-white border-2 border-redolence-green text-redolence-green hover:bg-redolence-green hover:text-white relative overflow-hidden" data-filter="clinic">
                Clinic Tour
            </button>
        </div>

        <!-- Gallery Grid -->
        <div class="gallery-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($galleryItems as $item): ?>
                <div class="gallery-item relative bg-white rounded-3xl shadow-xl overflow-hidden" data-category="<?= $item['category'] ?>" onclick="openLightbox(<?= $item['id'] ?>)">
                    <?php if ($item['category'] === 'before-after'): ?>
                        <!-- Before/After Slider -->
                        <div class="before-after-container">
                            <div class="before-after-slider" data-item-id="<?= $item['id'] ?>">
                                <img src="<?= $item['before_image'] ?>" alt="Before" class="before-image">
                                <img src="<?= $item['after_image'] ?>" alt="After" class="after-image">
                                <div class="slider-line"></div>
                                <div class="slider-handle"></div>

                                <!-- Before/After Labels -->
                                <div class="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                    Before
                                </div>
                                <div class="absolute top-4 right-4 bg-redolence-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                                    After
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Regular Image -->
                        <div class="relative h-64">
                            <img src="<?= $item['image'] ?>" alt="<?= htmlspecialchars($item['title']) ?>" class="w-full h-full object-cover">
                            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        </div>
                    <?php endif; ?>

                    <!-- Content -->
                    <div class="p-6">
                        <div class="mb-4">
                            <h3 class="text-xl font-display font-bold text-gray-900 mb-2">
                                <?= htmlspecialchars($item['title']) ?>
                            </h3>
                            <?php if (isset($item['treatment'])): ?>
                                <p class="text-redolence-blue font-semibold">
                                    <?= htmlspecialchars($item['treatment']) ?>
                                </p>
                            <?php endif; ?>
                        </div>

                        <p class="text-gray-600 leading-relaxed mb-4">
                            <?= htmlspecialchars($item['description']) ?>
                        </p>

                        <?php if ($item['category'] === 'before-after'): ?>
                            <!-- Treatment Details -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                    </svg>
                                    <?= htmlspecialchars($item['duration']) ?>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <?= $item['sessions'] ?> sessions
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- View Button -->
                        <button class="w-full bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            View Details
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-16">
            <div class="glass-card rounded-3xl p-8 max-w-4xl mx-auto">
                <h3 class="text-3xl font-display font-bold text-gray-900 mb-4">
                    Ready for Your Transformation?
                </h3>
                <p class="text-xl text-gray-600 mb-8">
                    Join hundreds of satisfied clients who have achieved their beauty goals with our professional medical aesthetics treatments.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?= getBasePath() ?>/customer/book"
                       class="inline-flex items-center justify-center bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                        </svg>
                        Book Your Consultation
                    </a>
                    <a href="<?= getBasePath() ?>/services"
                       class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-blue border-2 border-redolence-blue px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                        </svg>
                        View All Services
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Lightbox Modal -->
<div id="lightbox" class="lightbox">
    <div class="lightbox-content">
        <button class="lightbox-close" onclick="closeLightbox()">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
        <div id="lightbox-content-inner">
            <!-- Content will be populated by JavaScript -->
        </div>
    </div>
</div>

<script>
// Gallery data for JavaScript
const galleryData = <?= json_encode($galleryItems) ?>;

// Before/After Slider Functionality
function initBeforeAfterSliders() {
    const sliders = document.querySelectorAll('.before-after-slider');

    sliders.forEach(slider => {
        const afterImage = slider.querySelector('.after-image');
        const handle = slider.querySelector('.slider-handle');
        const line = slider.querySelector('.slider-line');
        let isDragging = false;

        function updateSlider(x) {
            const rect = slider.getBoundingClientRect();
            const percentage = Math.max(0, Math.min(100, ((x - rect.left) / rect.width) * 100));

            afterImage.style.clipPath = `polygon(${percentage}% 0%, 100% 0%, 100% 100%, ${percentage}% 100%)`;
            handle.style.left = `${percentage}%`;
            line.style.left = `${percentage}%`;
        }

        // Mouse events
        slider.addEventListener('mousedown', (e) => {
            isDragging = true;
            updateSlider(e.clientX);
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                updateSlider(e.clientX);
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events for mobile
        slider.addEventListener('touchstart', (e) => {
            isDragging = true;
            updateSlider(e.touches[0].clientX);
        });

        document.addEventListener('touchmove', (e) => {
            if (isDragging) {
                e.preventDefault();
                updateSlider(e.touches[0].clientX);
            }
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });

        // Prevent click event from bubbling when dragging
        slider.addEventListener('click', (e) => {
            if (!isDragging) {
                updateSlider(e.clientX);
            }
        });
    });
}

// Gallery Filter Functionality
function initGalleryFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const galleryItems = document.querySelectorAll('.gallery-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const filter = button.dataset.filter;

            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // Filter gallery items
            galleryItems.forEach(item => {
                const category = item.dataset.category;

                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, 100);
                } else {
                    item.style.opacity = '0';
                    item.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
        });
    });
}

// Lightbox Functionality
function openLightbox(itemId) {
    const item = galleryData.find(i => i.id === itemId);
    if (!item) return;

    const lightbox = document.getElementById('lightbox');
    const contentInner = document.getElementById('lightbox-content-inner');

    let content = '';

    if (item.category === 'before-after') {
        content = `
            <div class="p-8">
                <div class="mb-6">
                    <h2 class="text-3xl font-display font-bold text-gray-900 mb-2">${item.title}</h2>
                    <p class="text-xl text-redolence-blue font-semibold">${item.treatment}</p>
                </div>

                <div class="before-after-container mb-6">
                    <div class="before-after-slider" style="height: 400px;">
                        <img src="${item.before_image}" alt="Before" class="before-image">
                        <img src="${item.after_image}" alt="After" class="after-image">
                        <div class="slider-line"></div>
                        <div class="slider-handle"></div>

                        <div class="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            Before
                        </div>
                        <div class="absolute top-4 right-4 bg-redolence-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                            After
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 rounded-2xl p-6">
                        <h3 class="font-semibold text-gray-900 mb-4">Treatment Details</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-redolence-green mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-gray-700">Duration: ${item.duration}</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-redolence-green mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-gray-700">Sessions: ${item.sessions}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6">
                        <h3 class="font-semibold text-gray-900 mb-4">Results</h3>
                        <p class="text-gray-700 leading-relaxed">${item.description}</p>
                    </div>
                </div>

                <div class="text-center">
                    <a href="<?= getBasePath() ?>/customer/book"
                       class="inline-flex items-center bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                        </svg>
                        Book Similar Treatment
                    </a>
                </div>
            </div>
        `;
    } else {
        content = `
            <div class="p-8">
                <div class="mb-6">
                    <h2 class="text-3xl font-display font-bold text-gray-900 mb-2">${item.title}</h2>
                </div>

                <div class="mb-6">
                    <img src="${item.image}" alt="${item.title}" class="w-full h-96 object-cover rounded-2xl">
                </div>

                <div class="mb-6">
                    <p class="text-xl text-gray-600 leading-relaxed">${item.description}</p>
                </div>

                <div class="text-center">
                    <a href="<?= getBasePath() ?>/contact"
                       class="inline-flex items-center bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Visit Our Clinic
                    </a>
                </div>
            </div>
        `;
    }

    contentInner.innerHTML = content;
    lightbox.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Re-initialize before/after sliders in lightbox
    if (item.category === 'before-after') {
        setTimeout(() => {
            initBeforeAfterSliders();
        }, 100);
    }
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.classList.remove('active');
    document.body.style.overflow = '';
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initBeforeAfterSliders();
    initGalleryFilters();

    // Close lightbox when clicking outside
    document.getElementById('lightbox').addEventListener('click', function(e) {
        if (e.target === this) {
            closeLightbox();
        }
    });

    // Close lightbox with escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    });

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Add intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe gallery items for scroll animations
    document.querySelectorAll('.gallery-item').forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(item);
    });
});

// Initialize AOS (Animate On Scroll)
AOS.init({
    duration: 1000,
    easing: 'ease-out-cubic',
    once: true,
    offset: 100,
    delay: 0
});

// Enhanced Contact Card Interactions
document.querySelectorAll('.contact-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-15px) scale(1.03)';
        this.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.25)';
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
    });
});

// Typing Effect for Hero Text
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';

    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    type();
}

// Initialize typing effect when hero comes into view
const typingObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const typingText = entry.target.querySelector('.typing-text');
            if (typingText && !typingText.classList.contains('typed')) {
                typingText.classList.add('typed');
                const originalText = typingText.textContent;
                typeWriter(typingText, originalText, 80);
            }
        }
    });
}, { threshold: 0.3 });

// Observe hero section for typing effect
const heroSection = document.querySelector('section');
if (heroSection) {
    typingObserver.observe(heroSection);
}

// Enhanced Hero CTA Button
document.querySelectorAll('.hero-cta-primary').forEach(button => {
    button.addEventListener('click', function(e) {
        // Create ripple effect
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        this.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 800);
    });
});

// Add ripple effect CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        transform: scale(0);
        animation: ripple-animation 0.8s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
