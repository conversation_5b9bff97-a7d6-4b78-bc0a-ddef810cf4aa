# Gallery Management System Documentation

## Overview

The Gallery Management System has been successfully converted from a hard-coded system to a dynamic database-driven solution. This system manages both before/after treatment results and clinic photos for Redolence Medi Aesthetics.

## System Components

### 1. Database Structure

**Table: `gallery_items`**
```sql
- id (int, primary key, auto increment)
- title (varchar 255, required)
- category (enum: 'before-after', 'clinic', required)
- treatment (varchar 255, optional - for before/after items)
- description (text, required)
- duration (varchar 100, optional - for before/after items)
- sessions (int, optional - for before/after items)
- before_image (varchar 500, optional - for before/after items)
- after_image (varchar 500, optional - for before/after items)
- image (varchar 500, optional - for clinic items)
- sort_order (int, default 0)
- is_active (tinyint 1, default 1)
- created_at (timestamp, auto)
- updated_at (timestamp, auto update)
```

### 2. File Structure

```
/admin/gallery/
├── index.php              # Main admin gallery management
├── get_gallery_item.php   # AJAX endpoint for item data
└── migrate.php            # Web interface for migration

/database/
├── create_gallery_table.sql    # Table creation SQL
└── migrate_gallery_data.php    # Migration script

/includes/
└── gallery_functions.php       # Gallery database functions

/uploads/gallery/               # Image upload directory

/docs/
└── GALLERY_SYSTEM.md          # This documentation
```

### 3. Core Functions

**Gallery Functions (`includes/gallery_functions.php`):**
- `getGalleryItems($category = null)` - Get active gallery items
- `getGalleryItemById($id)` - Get single gallery item
- `getGalleryItemsForAdmin($page, $limit, $search, $category)` - Admin pagination
- `createGalleryItem($data)` - Create new gallery item
- `updateGalleryItem($id, $data)` - Update existing item
- `deleteGalleryItem($id)` - Delete gallery item
- `toggleGalleryItemStatus($id)` - Toggle active/inactive
- `getGalleryStats()` - Get statistics
- `uploadGalleryImage($file, $type)` - Handle image uploads
- `deleteGalleryImage($imagePath)` - Delete image files

## Features

### Frontend (gallery.php)
- ✅ Dynamic content from database
- ✅ Category filtering (Before/After, Clinic Photos)
- ✅ Responsive design preserved
- ✅ Lightbox functionality maintained
- ✅ Search and filter capabilities
- ✅ Optimized image loading

### Admin Panel (/admin/gallery/)
- ✅ Complete CRUD operations
- ✅ Image upload with validation
- ✅ Before/After image management
- ✅ Category-specific fields
- ✅ Search and filtering
- ✅ Pagination
- ✅ Status management (Active/Inactive)
- ✅ Sort order management
- ✅ Statistics dashboard
- ✅ Responsive admin interface

### Image Management
- ✅ File upload validation (JPEG, PNG, WebP)
- ✅ File size limits (5MB max)
- ✅ Automatic file naming
- ✅ Image preview in admin
- ✅ Secure file handling
- ✅ Automatic cleanup on deletion

## Installation & Setup

### 1. Run Migration
```bash
# Command line
php database/migrate_gallery_data.php

# Or via web interface
http://localhost/redolence/admin/gallery/migrate.php
```

### 2. Set Permissions
```bash
chmod 755 uploads/gallery/
```

### 3. Verify Installation
1. Check database table exists: `gallery_items`
2. Verify demo data inserted (6 items)
3. Test frontend: `/gallery.php`
4. Test admin: `/admin/gallery/`

## Usage Guide

### Adding Gallery Items

**Before/After Results:**
1. Go to Admin → Gallery Management
2. Click "Add Gallery Item"
3. Select "Before & After" category
4. Fill in:
   - Title (required)
   - Treatment name
   - Description (required)
   - Duration (e.g., "6 weeks")
   - Number of sessions
   - Upload before image
   - Upload after image
   - Set sort order
5. Save

**Clinic Photos:**
1. Select "Clinic Photo" category
2. Fill in:
   - Title (required)
   - Description (required)
   - Upload clinic image
   - Set sort order
3. Save

### Managing Existing Items
- **Edit:** Click edit icon in actions column
- **Toggle Status:** Click eye icon to activate/deactivate
- **Delete:** Click trash icon (with confirmation)
- **Search:** Use search box to find specific items
- **Filter:** Filter by category (Before/After or Clinic)

### Image Requirements
- **Formats:** JPEG, PNG, WebP
- **Size:** Maximum 5MB per image
- **Dimensions:** Recommended 800x600 or higher
- **Quality:** High resolution for best results

## Technical Details

### Database Relationships
- No foreign key relationships (standalone table)
- Indexed on: category, is_active, sort_order
- Composite indexes for performance

### Security Features
- Admin authentication required
- File upload validation
- SQL injection protection
- XSS protection via htmlspecialchars()
- CSRF protection via form tokens

### Performance Optimizations
- Database indexes on frequently queried columns
- Pagination for large datasets
- Lazy loading for images
- Optimized SQL queries
- Image compression recommendations

## API Endpoints

### AJAX Endpoints
- `GET /admin/gallery/get_gallery_item.php?id={id}` - Get item data for editing

### Form Actions
- `POST /admin/gallery/index.php` with `action=create` - Create item
- `POST /admin/gallery/index.php` with `action=update` - Update item
- `POST /admin/gallery/index.php` with `action=delete` - Delete item
- `POST /admin/gallery/index.php` with `action=toggle_status` - Toggle status

## Troubleshooting

### Common Issues

**Images not uploading:**
- Check uploads/gallery/ directory permissions
- Verify file size under 5MB
- Ensure correct file format (JPEG, PNG, WebP)

**Gallery not displaying:**
- Verify database connection
- Check if migration ran successfully
- Ensure gallery_functions.php is included

**Admin access denied:**
- Verify admin authentication
- Check user role permissions

### Error Messages
- "Failed to create gallery item" - Check database connection/permissions
- "Image upload failed" - Check file permissions and format
- "Gallery item not found" - Item may have been deleted

## Future Enhancements

### Planned Features
- [ ] Bulk image upload
- [ ] Image resizing/optimization
- [ ] Advanced filtering options
- [ ] Gallery categories management
- [ ] Image watermarking
- [ ] SEO optimization
- [ ] Analytics tracking

### Customization Options
- Modify image upload limits in `gallery_functions.php`
- Add new categories by updating enum in database
- Customize admin interface styling
- Add additional metadata fields

## Support

For technical support or questions about the Gallery Management System:
1. Check this documentation
2. Review error logs in `/logs/`
3. Verify database connectivity
4. Check file permissions

## Version History

- **v1.0** - Initial database-driven implementation
- **v1.1** - Added image upload functionality
- **v1.2** - Enhanced admin interface
- **v1.3** - Added migration tools and documentation
