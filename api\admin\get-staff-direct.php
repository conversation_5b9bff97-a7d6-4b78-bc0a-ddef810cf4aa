<?php
/**
 * Direct Staff Data Retrieval
 * Alternative endpoint for getting staff data without complex routing
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get staff ID from query parameter
$staffId = $_GET['staff_id'] ?? $_GET['id'] ?? null;

if (!$staffId) {
    http_response_code(400);
    echo json_encode(['error' => 'Staff ID is required']);
    exit;
}

try {
    // Get staff member from users table
    $staff = $database->fetch(
        "SELECT u.*, ss.role as staff_role, ss.hourly_rate, ss.bio, ss.experience, ss.is_active as schedule_active
         FROM users u
         LEFT JOIN staff_schedules ss ON u.id = ss.user_id
         WHERE u.id = ? AND u.role = 'STAFF'",
        [$staffId]
    );
    
    if (!$staff) {
        http_response_code(404);
        echo json_encode(['error' => 'Staff member not found']);
        exit;
    }
    
    // Get staff specialties (services they can perform) from staff_specialties table
    $specialties = [];
    $specialtyRecords = $database->fetchAll(
        "SELECT service_id, proficiency_level 
         FROM staff_specialties 
         WHERE user_id = ?",
        [$staffId]
    );
    
    foreach ($specialtyRecords as $specialty) {
        $specialties[] = $specialty['service_id'];
    }
    
    // Get staff schedule
    $schedule = null;
    $scheduleRecord = $database->fetch(
        "SELECT schedule FROM staff_schedules WHERE user_id = ?",
        [$staffId]
    );
    
    if ($scheduleRecord && !empty($scheduleRecord['schedule'])) {
        $schedule = json_decode($scheduleRecord['schedule'], true);
    }
    
    // Get services for reference
    $allServices = $database->fetchAll("SELECT id, name FROM services WHERE is_active = 1 ORDER BY name ASC");
    
    // Format response
    $response = [
        'id' => $staff['id'],
        'name' => html_entity_decode($staff['name'], ENT_QUOTES, 'UTF-8'),
        'email' => $staff['email'],
        'phone' => $staff['phone'],
        'is_active' => (bool)$staff['is_active'],
        'staff_role' => html_entity_decode($staff['staff_role'] ?? 'Staff Member', ENT_QUOTES, 'UTF-8'),
        'hourly_rate' => (int)($staff['hourly_rate'] ?? 500000),
        'specialties' => $specialties,
        'bio' => html_entity_decode($staff['bio'] ?? '', ENT_QUOTES, 'UTF-8'),
        'experience' => (int)($staff['experience'] ?? 0),
        'schedule' => $schedule,
        'schedule_active' => (bool)($staff['schedule_active'] ?? true),
        'created_at' => $staff['created_at'],
        'available_services' => $allServices,
        'specialty_details' => $specialtyRecords
    ];
    
    echo json_encode([
        'success' => true,
        'data' => $response,
        'debug' => [
            'staff_id' => $staffId,
            'specialties_count' => count($specialties),
            'schedule_exists' => !empty($schedule),
            'query_method' => 'direct'
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Failed to fetch staff member: ' . $e->getMessage(),
        'debug' => [
            'staff_id' => $staffId,
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
