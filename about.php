<?php
/**
 * About Page - Radical Redesign
 * Redolence Medi Aesthetics - Advanced Medical Aesthetics Center
 */

require_once __DIR__ . '/config/app.php';

$pageTitle = "About Redolence Medi Aesthetics - Advanced Medical Beauty";
$pageDescription = "Discover Redolence Medi Aesthetics, where cutting-edge medical science meets aesthetic artistry. Our board-certified specialists deliver transformative results through advanced medical treatments.";

include __DIR__ . '/includes/header.php';
?>

<!-- AOS (Animate On Scroll) Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<!-- Critical CSS for immediate rendering -->
<style>
/* Revolutionary Medical Aesthetics Design System */
:root {
    --primary-green: #49a75c;
    --primary-blue: #5894d2;
    --accent-gold: #f4d03f;
    --deep-navy: #1a2332;
    --soft-gray: #f8fafc;
    --medical-white: #ffffff;
    --shadow-primary: rgba(73, 167, 92, 0.15);
    --shadow-blue: rgba(88, 148, 210, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
    --gradient-soft: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
}

/* Advanced Animation Framework */
@keyframes morphingGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes floatingElement {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(5deg); }
    66% { transform: translateY(-10px) rotate(-3deg); }
}

@keyframes pulseGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(73, 167, 92, 0.3); }
    50% { box-shadow: 0 0 40px rgba(88, 148, 210, 0.5); }
}

@keyframes slideInFromLeft {
    0% { opacity: 0; transform: translateX(-100px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes slideInFromRight {
    0% { opacity: 0; transform: translateX(100px); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

/* Revolutionary Card System */
.medical-card-revolutionary {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
    backdrop-filter: blur(20px);
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

.medical-card-revolutionary::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: var(--gradient-primary);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    opacity: 0;
    transition: opacity 0.6s ease;
}

.medical-card-revolutionary:hover::before {
    opacity: 0;
}

.medical-card-revolutionary:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Morphing Background System */
.morphing-bg {
    background: linear-gradient(-45deg, #49a75c, #5894d2, #6ba3d6, #4db86d);
    background-size: 400% 400%;
    animation: morphingGradient 8s ease infinite;
}

/* Advanced Typography */
.text-revolutionary {
    background: var(--gradient-primary);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: morphingGradient 6s ease infinite;
}

/* Floating Elements System */
.floating-medical {
    animation: floatingElement 6s ease-in-out infinite;
}

.floating-medical:nth-child(2) { animation-delay: 2s; }
.floating-medical:nth-child(3) { animation-delay: 4s; }

/* Interactive Hover States */
.interactive-element {
    transition: all 0.3s ease;
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-3px);
}

/* Advanced Grid System */
.medical-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* Scroll-triggered Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Medical Professional Styling */
.medical-professional {
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(15px);
    border-radius: 24px;
    padding: 2rem;
    transition: all 0.5s ease;
}

.medical-professional:hover {
    transform: translateY(-10px) rotateY(5deg);
    box-shadow: 0 25px 50px rgba(73, 167, 92, 0.2);
}

/* Revolutionary Button System */
.btn-revolutionary {
    background: var(--gradient-primary);
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
}

.btn-revolutionary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.btn-revolutionary:hover::before {
    left: 100%;
}

.btn-revolutionary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(73, 167, 92, 0.4);
}

/* Ultra Enhanced Revolutionary Story Section Styles */

/* Advanced Background Animations */
.morphing-orb {
    animation: morphingOrb 8s ease-in-out infinite;
}

@keyframes morphingOrb {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        border-radius: 50%;
        opacity: 0.6;
    }
    25% {
        transform: scale(1.1) rotate(90deg);
        border-radius: 60% 40% 60% 40%;
        opacity: 0.8;
    }
    50% {
        transform: scale(0.9) rotate(180deg);
        border-radius: 40% 60% 40% 60%;
        opacity: 0.4;
    }
    75% {
        transform: scale(1.05) rotate(270deg);
        border-radius: 70% 30% 70% 30%;
        opacity: 0.7;
    }
}

.floating-particle-enhanced {
    animation: floatParticleEnhanced 8s ease-in-out infinite;
}

@keyframes floatParticleEnhanced {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-15px) translateX(10px) rotate(90deg) scale(1.1);
        opacity: 0.9;
    }
    50% {
        transform: translateY(-30px) translateX(-5px) rotate(180deg) scale(0.9);
        opacity: 0.4;
    }
    75% {
        transform: translateY(-10px) translateX(-15px) rotate(270deg) scale(1.05);
        opacity: 0.8;
    }
}

.animated-grid {
    animation: gridPulse 4s ease-in-out infinite;
}

@keyframes gridPulse {
    0%, 100% { opacity: 0.03; }
    50% { opacity: 0.06; }
}

/* Premium Badge Animations */
.premium-badge {
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.animate-gradient-x {
    background-size: 200% 200%;
    animation: gradientX 6s ease infinite;
}

@keyframes gradientX {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Ultra Gradient Text */
.ultra-gradient-text {
    background-size: 300% 300%;
    animation: ultraGradient 4s ease infinite;
}

@keyframes ultraGradient {
    0%, 100% {
        background-position: 0% 50%;
        filter: hue-rotate(0deg);
    }
    50% {
        background-position: 100% 50%;
        filter: hue-rotate(15deg);
    }
}

/* Typing Effect */
.typing-text {
    overflow: hidden;
    border-right: 2px solid transparent;
    animation: typing 3s steps(60, end), blink 1s infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    0%, 50% { border-color: rgba(73, 167, 92, 0.5); }
    51%, 100% { border-color: transparent; }
}

/* Premium Story Cards */
.premium-story-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 32px;
    padding: 3rem;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    z-index: 2;
}

.premium-story-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(73, 167, 92, 0.08), transparent);
    transition: left 0.8s ease;
}

.premium-story-card:hover::before {
    left: 100%;
}

.premium-story-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
    border-color: rgba(73, 167, 92, 0.4);
}

.card-glow {
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, transparent, rgba(73, 167, 92, 0.1), transparent, rgba(88, 148, 210, 0.1), transparent);
    border-radius: 32px;
    opacity: 0;
    transition: opacity 0.6s ease;
    z-index: -1;
}

.premium-story-card:hover .card-glow {
    opacity: 1;
}

.icon-container {
    position: relative;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-container:hover {
    transform: translateY(-3px) rotate(5deg);
}

/* Premium Achievement Stats */
.premium-achievement-stat {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 32px;
    padding: 2.5rem 1.5rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.premium-achievement-stat::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.premium-achievement-stat:hover::before {
    opacity: 1;
}

.premium-achievement-stat:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.progress-bar {
    transition: width 2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Legacy achievement stat for compatibility */
.achievement-stat {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem 1rem;
    transition: all 0.3s ease;
}

.achievement-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.visual-container {
    position: relative;
    z-index: 1;
}

.main-image-container {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.main-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.main-image-container:hover::before {
    opacity: 1;
}

.secondary-visual {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    z-index: 3;
}

.secondary-visual:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.floating-element {
    animation: floatElement 4s ease-in-out infinite;
}

@keyframes floatElement {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Counter Animation */
.counter {
    transition: all 0.3s ease;
}

/* Desktop Layout - Proper Z-Index Stacking */
@media (min-width: 1025px) {
    .visual-container-desktop {
        z-index: 1;
        position: relative;
    }

    .main-image-container {
        z-index: 1;
        position: relative;
    }

    .secondary-visual-desktop {
        z-index: 4;
        position: relative;
    }

    .premium-story-card-desktop {
        z-index: 5;
        position: relative;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 20px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05);
    }

    .premium-story-card-desktop:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    .btn-revolutionary-desktop {
        background: linear-gradient(135deg, var(--redolence-green), var(--redolence-blue));
        color: white;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-revolutionary-desktop:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(73, 167, 92, 0.3);
    }
}

/* Responsive Design - Mobile First */
@media (max-width: 1024px) {
    /* Tablet adjustments */
    .lg\\:grid-cols-3 {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .lg\\:col-span-2,
    .lg\\:col-span-1 {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .medical-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .medical-card-revolutionary {
        margin: 0;
        padding: 1rem;
    }

    .premium-story-card {
        padding: 2rem;
    }

    .premium-story-card .flex {
        flex-direction: column;
        text-align: center;
    }

    .premium-story-card .flex-shrink-0 {
        margin-bottom: 1rem;
    }

    .story-card {
        padding: 1.5rem;
    }

    .story-card .flex {
        flex-direction: column;
        text-align: center;
    }

    .story-card .flex-shrink-0 {
        margin-bottom: 1rem;
    }

    /* Mobile Grid Adjustments */
    .grid.grid-cols-1.lg\\:grid-cols-3 {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .main-image-container img {
        height: 200px;
    }

    .premium-achievement-stat {
        padding: 1.5rem 1rem;
    }

    /* Mobile Story Cards */
    .premium-story-card {
        padding: 1.5rem;
        margin: 0 1rem;
    }

    .premium-story-card .flex {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .premium-story-card .flex-shrink-0 {
        margin-bottom: 0;
        align-self: center;
    }

    .premium-story-card .icon-container {
        width: 4rem;
        height: 4rem;
    }

    .premium-story-card h3 {
        font-size: 1.5rem;
        line-height: 1.3;
    }

    .premium-story-card p {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    /* Mobile Achievement Stats */
    .premium-achievement-stat .text-5xl {
        font-size: 2.5rem;
    }

    .premium-achievement-stat .w-16 {
        width: 3rem;
        height: 3rem;
    }

    .premium-achievement-stat .w-8 {
        width: 1.5rem;
        height: 1.5rem;
    }

    /* Mobile Section Spacing */
    .py-32 {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }

    .mb-24 {
        margin-bottom: 3rem;
    }

    .mt-16 {
        margin-top: 2rem;
    }

    /* Mobile Typography */
    .text-6xl {
        font-size: 2.5rem;
        line-height: 1.1;
    }

    .text-8xl {
        font-size: 3rem;
        line-height: 1.1;
    }

    .text-2xl {
        font-size: 1.25rem;
        line-height: 1.4;
    }

    .text-3xl {
        font-size: 1.5rem;
        line-height: 1.3;
    }

    /* Mobile Visual Section - Fix Interference */
    .visual-container {
        margin-bottom: 2rem;
        z-index: 1;
        position: relative;
    }

    .main-image-container {
        z-index: 1;
        position: relative;
        margin-bottom: 1rem;
    }

    .secondary-visual {
        padding: 1rem;
        z-index: 2;
        position: relative;
    }

    .secondary-visual h5 {
        font-size: 0.875rem;
    }

    .secondary-visual p {
        font-size: 0.75rem;
    }

    /* Fix Technology Showcase interference on mobile */
    .visual-container-mobile {
        z-index: 1;
        position: relative;
        margin-bottom: 2rem;
    }

    .secondary-visual-mobile {
        z-index: 2;
        position: relative;
        transition: all 0.3s ease;
    }

    .secondary-visual-mobile:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }

    .premium-story-card-mobile {
        z-index: 3;
        position: relative;
        margin-top: 2rem;
        clear: both;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 24px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    }

    .premium-story-card-mobile:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .premium-story-card {
        z-index: 3;
        position: relative;
        margin-top: 1.5rem;
        clear: both;
    }

    /* Mobile Treatment Process - Fix Spacing */
    .bg-gradient-to-br.from-slate-50 {
        padding: 1.5rem;
        margin: 2rem 0;
        z-index: 4;
        position: relative;
        clear: both;
    }

    .bg-gradient-to-br.from-slate-50 h4 {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .bg-gradient-to-br.from-slate-50 h5 {
        font-size: 0.875rem;
    }

    .bg-gradient-to-br.from-slate-50 p {
        font-size: 0.75rem;
    }

    /* Mobile Button */
    .btn-revolutionary {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    /* Mobile Premium Badge */
    .premium-badge {
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
    }

    .premium-badge span {
        font-size: 0.75rem;
    }

    /* Mobile Trust Indicators */
    .flex.justify-center.items-center.space-x-8 {
        flex-direction: column;
        gap: 0.5rem;
    }

    .flex.justify-center.items-center.space-x-8 > div {
        margin: 0;
    }

    /* Keep Progress Indicators Horizontal on Mobile */
    .flex.items-center.space-x-3 {
        flex-direction: row !important;
        align-items: center !important;
        gap: 0.75rem !important;
    }

    .flex.items-center.space-x-3 .flex.space-x-1 {
        flex-direction: row !important;
        gap: 0.25rem !important;
    }

    .flex.justify-center.items-center.space-x-2 {
        flex-direction: row !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

    /* Force single column on mobile */
    .lg\\:grid-cols-3 {
        grid-template-columns: 1fr !important;
    }

    .lg\\:col-span-2,
    .lg\\:col-span-1 {
        grid-column: span 1 !important;
    }

    /* Mobile container padding */
    .max-w-7xl {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Mobile section padding */
    .py-32.relative.overflow-hidden {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    /* Mobile Hero Section */
    .relative.min-h-screen h1 {
        font-size: 3rem !important;
        line-height: 1.1;
    }
    
    .relative.min-h-screen h1 span {
        font-size: 2.5rem !important;
    }
    
    .relative.min-h-screen p {
        font-size: 1.1rem !important;
    }
    
    .relative.min-h-screen p span {
        font-size: 1rem !important;
    }
    
    /* Mobile Section Headings */
    h2 {
        font-size: 2.5rem !important;
    }
    
    .text-6xl, .text-7xl {
        font-size: 2.5rem !important;
    }
    
    /* Mobile Cards */
    .medical-professional {
        padding: 1.5rem;
    }
    
    .medical-professional .w-40 {
        width: 6rem;
        height: 6rem;
    }
    
    .medical-professional .text-3xl {
        font-size: 1.5rem;
    }
    
    /* Mobile Values Cards */
    .medical-card-revolutionary.rounded-3xl {
        padding: 1.5rem;
    }
    
    .medical-card-revolutionary h3 {
        font-size: 1.5rem;
    }
    
    .medical-card-revolutionary .w-24 {
        width: 4rem;
        height: 4rem;
    }
    
    /* Mobile Sections Padding */
    .py-32 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .py-16 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    .py-24 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
}
</style>

<!-- Revolutionary Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Dynamic Background -->
    <div class="absolute inset-0 morphing-bg"></div>
    
    <!-- Floating Medical Elements -->
    <div class="absolute inset-0 pointer-events-none">
        <div class="floating-medical absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full backdrop-blur-sm"></div>
        <div class="floating-medical absolute top-40 right-20 w-24 h-24 bg-white/15 rounded-full backdrop-blur-sm"></div>
        <div class="floating-medical absolute bottom-32 left-1/4 w-40 h-40 bg-white/8 rounded-full backdrop-blur-sm"></div>
        <div class="floating-medical absolute bottom-20 right-10 w-28 h-28 bg-white/12 rounded-full backdrop-blur-sm"></div>
    </div>
    
    <!-- Hero Content -->
    <div class="relative z-10 text-center text-white px-6 max-w-6xl mx-auto">
        <div class="scroll-reveal">
            <div class="inline-flex items-center bg-white/20 backdrop-blur-sm px-8 py-4 rounded-full mb-8 border border-white/30">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="font-semibold text-lg">CERTIFIED MEDICAL AESTHETICS CENTER</span>
            </div>
        </div>
        
        <h1 class="text-7xl md:text-9xl font-black mb-8 leading-none scroll-reveal" style="animation-delay: 0.2s;">
            REDOLENCE
            <span class="block text-6xl md:text-8xl font-light opacity-90">Medi Aesthetics</span>
        </h1>
        
        <p class="text-2xl md:text-4xl font-light mb-12 leading-relaxed scroll-reveal" style="animation-delay: 0.4s;">
            Where <strong>Medical Science</strong> meets <strong>Aesthetic Artistry</strong>
            <span class="block mt-4 text-xl md:text-2xl opacity-80">Transforming lives through advanced medical treatments</span>
        </p>
        
        <div class="flex flex-col md:flex-row gap-6 justify-center items-center scroll-reveal" style="animation-delay: 0.6s;">
            <button class="btn-revolutionary interactive-element">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Schedule Consultation
            </button>
            <button class="bg-white/20 backdrop-blur-sm border-2 border-white/50 text-white px-8 py-4 rounded-full font-semibold hover:bg-white/30 transition-all interactive-element">
                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Virtual Tour
            </button>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</section>

<!-- Revolutionary Story Section - Ultra Enhanced -->
<section class="py-32 relative overflow-hidden bg-gradient-to-br from-slate-50 via-white to-blue-50 min-h-screen flex items-center">
    <!-- Ultra Advanced Background Effects -->
    <div class="absolute inset-0">
        <!-- Dynamic Gradient Orbs with Morphing -->
        <div class="morphing-orb absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-redolence-green/25 to-emerald-400/25 rounded-full blur-3xl"></div>
        <div class="morphing-orb absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-redolence-blue/25 to-cyan-400/25 rounded-full blur-3xl" style="animation-delay: 1.5s;"></div>
        <div class="morphing-orb absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-purple-400/15 to-pink-400/15 rounded-full blur-3xl" style="animation-delay: 3s;"></div>

        <!-- Enhanced Floating Particles System -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="floating-particle-enhanced absolute top-1/4 left-1/4 w-3 h-3 bg-gradient-to-r from-redolence-green/60 to-emerald-400/60 rounded-full shadow-lg"></div>
            <div class="floating-particle-enhanced absolute top-3/4 right-1/4 w-4 h-4 bg-gradient-to-r from-redolence-blue/60 to-cyan-400/60 rounded-full shadow-lg" style="animation-delay: 1.2s;"></div>
            <div class="floating-particle-enhanced absolute top-1/2 left-3/4 w-2 h-2 bg-gradient-to-r from-purple-400/60 to-pink-400/60 rounded-full shadow-lg" style="animation-delay: 2.4s;"></div>
            <div class="floating-particle-enhanced absolute bottom-1/4 left-1/2 w-3.5 h-3.5 bg-gradient-to-r from-emerald-400/60 to-teal-400/60 rounded-full shadow-lg" style="animation-delay: 3.6s;"></div>
            <div class="floating-particle-enhanced absolute top-1/3 right-1/3 w-2.5 h-2.5 bg-gradient-to-r from-indigo-400/60 to-blue-400/60 rounded-full shadow-lg" style="animation-delay: 4.8s;"></div>
            <div class="floating-particle-enhanced absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-gradient-to-r from-rose-400/60 to-pink-400/60 rounded-full shadow-lg" style="animation-delay: 6s;"></div>
        </div>

        <!-- Animated Grid Pattern -->
        <div class="absolute inset-0 opacity-[0.03] animated-grid" style="background-image: linear-gradient(rgba(73, 167, 92, 0.6) 1px, transparent 1px), linear-gradient(90deg, rgba(73, 167, 92, 0.6) 1px, transparent 1px); background-size: 60px 60px;"></div>

        <!-- Subtle Noise Texture -->
        <div class="absolute inset-0 opacity-[0.015] mix-blend-multiply" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48ZmlsdGVyIGlkPSJub2lzZSI+PGZlVHVyYnVsZW5jZSBiYXNlRnJlcXVlbmN5PSIwLjkiIG51bU9jdGF2ZXM9IjQiIHNlZWQ9IjIiLz48L2ZpbHRlcj48L2RlZnM+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsdGVyPSJ1cmwoI25vaXNlKSIgb3BhY2l0eT0iMC4xIi8+PC9zdmc+');"></div>
    </div>

    <div class="max-w-7xl mx-auto px-6 relative z-10">
        <!-- Ultra Enhanced Section Header -->
        <div class="text-center mb-24" data-aos="fade-up" data-aos-duration="1200">
            <!-- Premium Badge with Advanced Effects -->
            <div class="premium-badge inline-flex items-center bg-white/90 backdrop-blur-md px-10 py-5 rounded-full mb-10 border border-redolence-green/30 shadow-2xl relative overflow-hidden">
                <!-- Animated Background Gradient -->
                <div class="absolute inset-0 bg-gradient-to-r from-redolence-green/5 via-transparent to-redolence-blue/5 animate-gradient-x"></div>

                <!-- Pulsing Indicators -->
                <div class="relative flex items-center">
                    <div class="w-4 h-4 bg-gradient-to-r from-redolence-green to-emerald-400 rounded-full mr-4 animate-pulse shadow-lg">
                        <div class="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                    </div>
                    <span class="font-bold text-redolence-green tracking-wider text-sm uppercase relative">
                        Our Revolutionary Journey
                        <div class="absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-redolence-green to-redolence-blue rounded-full"></div>
                    </span>
                    <div class="w-4 h-4 bg-gradient-to-r from-redolence-blue to-cyan-400 rounded-full ml-4 animate-pulse shadow-lg" style="animation-delay: 0.5s;">
                        <div class="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                    </div>
                </div>
            </div>

            <!-- Ultra Dynamic Title -->
            <h2 class="text-6xl md:text-8xl font-black mb-8 leading-tight relative">
                <span class="ultra-gradient-text bg-gradient-to-r from-redolence-green via-emerald-500 via-cyan-400 to-redolence-blue bg-clip-text text-transparent">
                    Redefining Beauty
                </span>
                <br>
                <span class="text-slate-800 relative">
                    Through Science
                    <!-- Decorative Elements -->
                    <div class="absolute -top-4 -right-8 w-8 h-8 bg-gradient-to-r from-redolence-green/20 to-emerald-400/20 rounded-full blur-sm animate-pulse"></div>
                    <div class="absolute -bottom-4 -left-8 w-6 h-6 bg-gradient-to-r from-redolence-blue/20 to-cyan-400/20 rounded-full blur-sm animate-pulse" style="animation-delay: 1s;"></div>
                </span>
            </h2>

            <!-- Enhanced Subtitle with Typing Effect -->
            <div class="relative max-w-5xl mx-auto">
                <p class="text-2xl md:text-3xl text-slate-600 leading-relaxed font-light mb-6 typing-text">
                    Where cutting-edge medical technology meets artistic vision to create transformative experiences
                </p>

                <!-- Progress Indicator -->
                <div class="flex justify-center items-center space-x-2 mb-8">
                    <div class="w-2 h-2 bg-redolence-green rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-redolence-blue rounded-full animate-bounce" style="animation-delay: 0.1s;"></div>
                    <div class="w-2 h-2 bg-emerald-400 rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
                </div>

                <!-- Trust Indicators -->
                <div class="flex justify-center items-center space-x-8 text-sm text-slate-500">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">FDA Approved</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-redolence-blue" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">Board Certified</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-emerald-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="font-medium">15,000+ Procedures</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid - Mobile-First Responsive -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 items-start">
            <!-- Left Content -->
            <div class="lg:col-span-2 space-y-6 lg:space-y-8">
                <!-- Ultra Enhanced Story Cards -->
                <div class="space-y-8">
                    <!-- Premium Card 1 - Innovation -->
                    <div class="premium-story-card group" data-aos="fade-right" data-aos-duration="1000" data-aos-delay="100">
                        <div class="card-glow"></div>
                        <div class="flex items-start space-x-8">
                            <div class="flex-shrink-0 relative">
                                <!-- Enhanced Icon Container -->
                                <div class="icon-container w-20 h-20 bg-gradient-to-br from-redolence-green via-emerald-500 to-emerald-600 rounded-3xl flex items-center justify-center shadow-2xl relative overflow-hidden">
                                    <!-- Animated Background -->
                                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                                    <!-- Icon with Animation -->
                                    <svg class="w-10 h-10 text-white relative z-10 transform group-hover:scale-110 group-hover:rotate-12 transition-all duration-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>

                                    <!-- Pulse Ring -->
                                    <div class="absolute inset-0 rounded-3xl border-2 border-redolence-green/30 animate-ping"></div>
                                </div>

                                <!-- Number Badge -->
                                <div class="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg border-2 border-redolence-green/20">
                                    <span class="text-sm font-bold text-redolence-green">01</span>
                                </div>
                            </div>

                            <div class="flex-1 space-y-4">
                                <!-- Enhanced Title -->
                                <div class="relative">
                                    <h3 class="text-3xl font-black text-slate-800 mb-2 group-hover:text-redolence-green transition-all duration-500 relative">
                                        Innovation at Our Core
                                        <!-- Underline Animation -->
                                        <div class="absolute -bottom-1 left-0 w-0 h-1 bg-gradient-to-r from-redolence-green to-emerald-400 rounded-full group-hover:w-full transition-all duration-700"></div>
                                    </h3>
                                    <div class="text-sm font-semibold text-redolence-green/70 uppercase tracking-wider">Leading Medical Technology</div>
                                </div>

                                <!-- Enhanced Description -->
                                <p class="text-lg text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors duration-300">
                                    We pioneered the integration of <strong class="text-redolence-green font-bold bg-redolence-green/10 px-2 py-1 rounded-lg">FDA-approved medical technologies</strong>
                                    with personalized aesthetic treatments, setting new industry standards for safety and efficacy.
                                </p>

                                <!-- Progress Indicator -->
                                <div class="flex items-center space-x-3 pt-2">
                                    <div class="flex space-x-1">
                                        <div class="w-2 h-2 bg-redolence-green rounded-full"></div>
                                        <div class="w-2 h-2 bg-redolence-green/60 rounded-full"></div>
                                        <div class="w-2 h-2 bg-redolence-green/30 rounded-full"></div>
                                    </div>
                                    <span class="text-xs font-medium text-slate-500 uppercase tracking-wide">Advanced Technology</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Premium Card 2 - Scientific Excellence -->
                    <div class="premium-story-card group" data-aos="fade-right" data-aos-duration="1000" data-aos-delay="200">
                        <div class="card-glow"></div>
                        <div class="flex items-start space-x-8">
                            <div class="flex-shrink-0 relative">
                                <!-- Enhanced Icon Container -->
                                <div class="icon-container w-20 h-20 bg-gradient-to-br from-redolence-blue via-blue-500 to-cyan-600 rounded-3xl flex items-center justify-center shadow-2xl relative overflow-hidden">
                                    <!-- Animated Background -->
                                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                                    <!-- Icon with Animation -->
                                    <svg class="w-10 h-10 text-white relative z-10 transform group-hover:scale-110 group-hover:rotate-12 transition-all duration-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                    </svg>

                                    <!-- Pulse Ring -->
                                    <div class="absolute inset-0 rounded-3xl border-2 border-redolence-blue/30 animate-ping"></div>
                                </div>

                                <!-- Number Badge -->
                                <div class="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg border-2 border-redolence-blue/20">
                                    <span class="text-sm font-bold text-redolence-blue">02</span>
                                </div>
                            </div>

                            <div class="flex-1 space-y-4">
                                <!-- Enhanced Title -->
                                <div class="relative">
                                    <h3 class="text-3xl font-black text-slate-800 mb-2 group-hover:text-redolence-blue transition-all duration-500 relative">
                                        Scientific Excellence
                                        <!-- Underline Animation -->
                                        <div class="absolute -bottom-1 left-0 w-0 h-1 bg-gradient-to-r from-redolence-blue to-cyan-400 rounded-full group-hover:w-full transition-all duration-700"></div>
                                    </h3>
                                    <div class="text-sm font-semibold text-redolence-blue/70 uppercase tracking-wider">Board-Certified Expertise</div>
                                </div>

                                <!-- Enhanced Description -->
                                <p class="text-lg text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors duration-300">
                                    Our <strong class="text-redolence-blue font-bold bg-redolence-blue/10 px-2 py-1 rounded-lg">board-certified physicians</strong> and licensed medical professionals
                                    combine decades of experience with continuous education in the latest aesthetic medicine advances.
                                </p>

                                <!-- Progress Indicator -->
                                <div class="flex items-center space-x-3 pt-2">
                                    <div class="flex space-x-1">
                                        <div class="w-2 h-2 bg-redolence-blue rounded-full"></div>
                                        <div class="w-2 h-2 bg-redolence-blue/60 rounded-full"></div>
                                        <div class="w-2 h-2 bg-redolence-blue/30 rounded-full"></div>
                                    </div>
                                    <span class="text-xs font-medium text-slate-500 uppercase tracking-wide">Medical Expertise</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Premium Card 3 - Patient Care -->
                    <div class="premium-story-card group" data-aos="fade-right" data-aos-duration="1000" data-aos-delay="300">
                        <div class="card-glow"></div>
                        <div class="flex items-start space-x-8">
                            <div class="flex-shrink-0 relative">
                                <!-- Enhanced Icon Container -->
                                <div class="icon-container w-20 h-20 bg-gradient-to-br from-purple-500 via-pink-500 to-rose-600 rounded-3xl flex items-center justify-center shadow-2xl relative overflow-hidden">
                                    <!-- Animated Background -->
                                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                                    <!-- Icon with Animation -->
                                    <svg class="w-10 h-10 text-white relative z-10 transform group-hover:scale-110 group-hover:rotate-12 transition-all duration-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>

                                    <!-- Pulse Ring -->
                                    <div class="absolute inset-0 rounded-3xl border-2 border-purple-500/30 animate-ping"></div>
                                </div>

                                <!-- Number Badge -->
                                <div class="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg border-2 border-purple-500/20">
                                    <span class="text-sm font-bold text-purple-500">03</span>
                                </div>
                            </div>

                            <div class="flex-1 space-y-4">
                                <!-- Enhanced Title -->
                                <div class="relative">
                                    <h3 class="text-3xl font-black text-slate-800 mb-2 group-hover:text-purple-500 transition-all duration-500 relative">
                                        Patient-Centered Care
                                        <!-- Underline Animation -->
                                        <div class="absolute -bottom-1 left-0 w-0 h-1 bg-gradient-to-r from-purple-500 to-pink-400 rounded-full group-hover:w-full transition-all duration-700"></div>
                                    </h3>
                                    <div class="text-sm font-semibold text-purple-500/70 uppercase tracking-wider">Personalized Treatment</div>
                                </div>

                                <!-- Enhanced Description -->
                                <p class="text-lg text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors duration-300">
                                    Every treatment journey begins with <strong class="text-purple-500 font-bold bg-purple-500/10 px-2 py-1 rounded-lg">comprehensive consultation</strong>
                                    and 3D imaging analysis, ensuring personalized results that enhance your natural beauty.
                                </p>

                                <!-- Progress Indicator -->
                                <div class="flex items-center space-x-3 pt-2">
                                    <div class="flex space-x-1">
                                        <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                                        <div class="w-2 h-2 bg-purple-500/60 rounded-full"></div>
                                        <div class="w-2 h-2 bg-purple-500/30 rounded-full"></div>
                                    </div>
                                    <span class="text-xs font-medium text-slate-500 uppercase tracking-wide">Personalized Care</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ultra Enhanced Achievement Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8 mt-8 lg:mt-16" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="400">
                    <!-- Stat 1 - Procedures -->
                    <div class="premium-achievement-stat group text-center relative overflow-hidden">
                        <!-- Background Gradient -->
                        <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/5 to-emerald-400/5 rounded-3xl"></div>

                        <!-- Icon -->
                        <div class="w-16 h-16 bg-gradient-to-br from-redolence-green to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>

                        <!-- Counter -->
                        <div class="text-5xl font-black text-redolence-green mb-3 counter relative" data-target="15000">0
                            <span class="text-2xl">+</span>
                        </div>

                        <!-- Label -->
                        <div class="text-sm font-bold text-slate-600 uppercase tracking-wider mb-2">Successful Procedures</div>
                        <div class="text-xs text-slate-500">Medical Treatments Completed</div>

                        <!-- Progress Bar -->
                        <div class="w-full bg-gray-200 rounded-full h-1 mt-3">
                            <div class="bg-gradient-to-r from-redolence-green to-emerald-400 h-1 rounded-full progress-bar" style="width: 0%" data-width="100%"></div>
                        </div>
                    </div>

                    <!-- Stat 2 - Satisfaction -->
                    <div class="premium-achievement-stat group text-center relative overflow-hidden">
                        <!-- Background Gradient -->
                        <div class="absolute inset-0 bg-gradient-to-br from-redolence-blue/5 to-cyan-400/5 rounded-3xl"></div>

                        <!-- Icon -->
                        <div class="w-16 h-16 bg-gradient-to-br from-redolence-blue to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>

                        <!-- Counter -->
                        <div class="text-5xl font-black text-redolence-blue mb-3 counter relative" data-target="98.7">0
                            <span class="text-2xl">%</span>
                        </div>

                        <!-- Label -->
                        <div class="text-sm font-bold text-slate-600 uppercase tracking-wider mb-2">Patient Satisfaction</div>
                        <div class="text-xs text-slate-500">Exceptional Results Rating</div>

                        <!-- Progress Bar -->
                        <div class="w-full bg-gray-200 rounded-full h-1 mt-3">
                            <div class="bg-gradient-to-r from-redolence-blue to-cyan-400 h-1 rounded-full progress-bar" style="width: 0%" data-width="98.7%"></div>
                        </div>
                    </div>

                    <!-- Stat 3 - Experience -->
                    <div class="premium-achievement-stat group text-center relative overflow-hidden">
                        <!-- Background Gradient -->
                        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-400/5 rounded-3xl"></div>

                        <!-- Icon -->
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>

                        <!-- Counter -->
                        <div class="text-5xl font-black text-purple-500 mb-3 counter relative" data-target="12">0
                            <span class="text-2xl">+</span>
                        </div>

                        <!-- Label -->
                        <div class="text-sm font-bold text-slate-600 uppercase tracking-wider mb-2">Years Experience</div>
                        <div class="text-xs text-slate-500">Medical Aesthetics Expertise</div>

                        <!-- Progress Bar -->
                        <div class="w-full bg-gray-200 rounded-full h-1 mt-3">
                            <div class="bg-gradient-to-r from-purple-500 to-pink-400 h-1 rounded-full progress-bar" style="width: 0%" data-width="85%"></div>
                        </div>
                    </div>

                    <!-- Stat 4 - Awards -->
                    <div class="premium-achievement-stat group text-center relative overflow-hidden">
                        <!-- Background Gradient -->
                        <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-teal-400/5 rounded-3xl"></div>

                        <!-- Icon -->
                        <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>

                        <!-- Counter -->
                        <div class="text-5xl font-black text-emerald-500 mb-3 counter relative" data-target="50">0
                            <span class="text-2xl">+</span>
                        </div>

                        <!-- Label -->
                        <div class="text-sm font-bold text-slate-600 uppercase tracking-wider mb-2">Awards & Recognition</div>
                        <div class="text-xs text-slate-500">Industry Excellence</div>

                        <!-- Progress Bar -->
                        <div class="w-full bg-gray-200 rounded-full h-1 mt-3">
                            <div class="bg-gradient-to-r from-emerald-500 to-teal-400 h-1 rounded-full progress-bar" style="width: 0%" data-width="95%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fixed Right Visual - Separate Desktop/Mobile -->
            <div class="lg:col-span-1 mt-8 lg:mt-0" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                <!-- Desktop Layout - Larger Content -->
                <div class="hidden lg:block space-y-6">
                    <!-- Main Visual Container -->
                    <div class="visual-container-desktop relative">
                        <!-- Primary Image -->
                        <div class="main-image-container relative overflow-hidden rounded-3xl shadow-2xl">
                            <img src="https://images.unsplash.com/photo-1597143722151-6c041d7b2901?q=80&w=764&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                                 alt="Advanced Medical Aesthetics Facility"
                                 class="w-full h-64 object-cover transition-transform duration-700 hover:scale-105">

                            <!-- Overlay Content -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex items-end">
                                <div class="p-5 text-white">
                                    <h4 class="text-lg font-bold mb-2">State-of-the-Art Facility</h4>
                                    <p class="text-white/90 text-sm">Advanced medical technology meets luxury comfort</p>
                                </div>
                            </div>

                            <!-- Logo Badge - Large Desktop -->
                            <div class="absolute top-6 left-6 w-20 h-20 bg-white/98 backdrop-blur-md rounded-full flex items-center justify-center shadow-xl border-2 border-white/30 hover:scale-105 transition-all duration-300">
                                <div class="w-16 h-16 rounded-full overflow-hidden bg-gradient-to-br from-redolence-green/5 to-redolence-blue/5 flex items-center justify-center p-1">
                                    <img src="<?= getBasePath() ?>/assets/images/red.jpg"
                                         alt="Redolence Logo"
                                         class="w-full h-full object-contain rounded-full">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Feature Grid -->
                    <div class="grid grid-cols-2 gap-4">
                        <div class="secondary-visual-desktop bg-gradient-to-br from-redolence-green/10 to-emerald-100 rounded-2xl p-5 text-center hover:scale-105 transition-transform duration-300">
                            <div class="w-12 h-12 bg-redolence-green rounded-xl flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                            <h5 class="font-bold text-slate-800 mb-1 text-sm">FDA Approved</h5>
                            <p class="text-xs text-slate-600">Medical Grade</p>
                        </div>

                        <div class="secondary-visual-desktop bg-gradient-to-br from-redolence-blue/10 to-cyan-100 rounded-2xl p-5 text-center hover:scale-105 transition-transform duration-300">
                            <div class="w-12 h-12 bg-redolence-blue rounded-xl flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <h5 class="font-bold text-slate-800 mb-1 text-sm">Board Certified</h5>
                            <p class="text-xs text-slate-600">Medical Team</p>
                        </div>
                    </div>

                    <!-- Technology Showcase -->
                    <div class="premium-story-card-desktop group p-6">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <h4 class="text-lg font-bold text-slate-800 mb-3 group-hover:text-purple-500 transition-colors duration-300">
                                Advanced Technology
                            </h4>
                            <p class="text-sm text-slate-600 leading-relaxed">
                                Cutting-edge medical equipment and 3D imaging systems for precise, safe treatments
                            </p>
                        </div>
                    </div>

                    <!-- Call to Action -->
                    <div class="text-center">
                        <button class="btn-revolutionary-desktop w-full text-sm py-3 px-6">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Schedule Consultation
                        </button>
                    </div>
                </div>

                <!-- Mobile Layout -->
                <div class="lg:hidden space-y-6">
                    <!-- Main Visual Container -->
                    <div class="visual-container-mobile relative">
                        <!-- Primary Image -->
                        <div class="main-image-container relative overflow-hidden rounded-3xl shadow-2xl">
                            <img src="https://images.unsplash.com/photo-1597143722151-6c041d7b2901?q=80&w=764&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                                 alt="Advanced Medical Aesthetics Facility"
                                 class="w-full h-48 object-cover transition-transform duration-700 hover:scale-105">

                            <!-- Overlay Content -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent flex items-end">
                                <div class="p-4 text-white">
                                    <h4 class="text-lg font-bold mb-1">State-of-the-Art Facility</h4>
                                    <p class="text-white/90 text-sm">Advanced medical technology</p>
                                </div>
                            </div>

                            <!-- Logo Badge - Large Mobile Design -->
                            <div class="absolute top-6 left-6 w-20 h-20 bg-white/98 backdrop-blur-md rounded-full flex items-center justify-center shadow-xl border-2 border-white/30 hover:scale-105 transition-all duration-300">
                                <div class="w-16 h-16 rounded-full overflow-hidden bg-gradient-to-br from-redolence-green/5 to-redolence-blue/5 flex items-center justify-center p-1">
                                    <img src="<?= getBasePath() ?>/assets/images/red.jpg"
                                         alt="Redolence Logo"
                                         class="w-full h-full object-contain rounded-full">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Feature Grid -->
                    <div class="grid grid-cols-2 gap-4">
                        <div class="secondary-visual-mobile bg-gradient-to-br from-redolence-green/10 to-emerald-100 rounded-2xl p-4 text-center">
                            <div class="w-10 h-10 bg-redolence-green rounded-xl flex items-center justify-center mx-auto mb-3">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                            <h5 class="font-bold text-slate-800 mb-1 text-sm">FDA Approved</h5>
                            <p class="text-xs text-slate-600">Medical Grade</p>
                        </div>

                        <div class="secondary-visual-mobile bg-gradient-to-br from-redolence-blue/10 to-cyan-100 rounded-2xl p-4 text-center">
                            <div class="w-10 h-10 bg-redolence-blue rounded-xl flex items-center justify-center mx-auto mb-3">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <h5 class="font-bold text-slate-800 mb-1 text-sm">Board Certified</h5>
                            <p class="text-xs text-slate-600">Medical Team</p>
                        </div>
                    </div>

                    <!-- Technology Showcase -->
                    <div class="premium-story-card-mobile group p-5">
                        <div class="text-center">
                            <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <h4 class="text-lg font-bold text-slate-800 mb-2 group-hover:text-purple-500 transition-colors duration-300">
                                Advanced Technology
                            </h4>
                            <p class="text-sm text-slate-600 leading-relaxed">
                                Cutting-edge medical equipment for precise, safe treatments
                            </p>
                        </div>
                    </div>

                    <!-- Call to Action -->
                    <div class="text-center">
                        <button class="btn-revolutionary w-full">
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Schedule Your Consultation
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary Values Section -->
<section class="py-32 bg-gradient-to-br from-redolence-green/5 via-white to-redolence-blue/5 relative">
    <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-20 scroll-reveal">
            <div class="inline-flex items-center bg-white/80 backdrop-blur-sm px-8 py-4 rounded-full mb-8 border border-redolence-green/20">
                <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                <span class="font-bold text-lg">OUR CORE VALUES</span>
            </div>
            
            <h2 class="text-6xl md:text-7xl font-black mb-8 leading-tight">
                What Drives
                <span class="text-revolutionary block">Our Excellence</span>
            </h2>
            
            <p class="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Every decision, every treatment, every interaction is guided by our unwavering commitment to these fundamental principles
            </p>
        </div>
        
        <!-- Values Grid -->
        <div class="medical-grid">
            <!-- Value 1 -->
            <div class="medical-card-revolutionary rounded-3xl p-10 text-center scroll-reveal interactive-element" style="animation-delay: 0.1s;">
                <div class="w-24 h-24 bg-gradient-to-br from-redolence-green/20 to-redolence-green/10 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                    <svg class="w-12 h-12 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-redolence-green/30 rounded-full animate-pulse"></div>
                </div>
                <h3 class="text-3xl font-bold text-gray-900 mb-6">Medical Safety First</h3>
                <p class="text-gray-600 leading-relaxed text-lg">
                    We prioritize patient safety above all else, using only FDA-approved treatments, 
                    medical-grade products, and following the strictest clinical protocols in the industry.
                </p>
            </div>
            
            <!-- Value 2 -->
            <div class="medical-card-revolutionary rounded-3xl p-10 text-center scroll-reveal interactive-element" style="animation-delay: 0.2s;">
                <div class="w-24 h-24 bg-gradient-to-br from-redolence-blue/20 to-redolence-blue/10 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                    <svg class="w-12 h-12 text-redolence-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-redolence-blue/30 rounded-full animate-pulse"></div>
                </div>
                <h3 class="text-3xl font-bold text-gray-900 mb-6">Scientific Innovation</h3>
                <p class="text-gray-600 leading-relaxed text-lg">
                    We stay at the forefront of medical aesthetics through continuous research, 
                    advanced training, and investment in cutting-edge technology and techniques.
                </p>
            </div>
            
            <!-- Value 3 -->
            <div class="medical-card-revolutionary rounded-3xl p-10 text-center scroll-reveal interactive-element" style="animation-delay: 0.3s;">
                <div class="w-24 h-24 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-8 relative">
                    <svg class="w-12 h-12 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    <div class="absolute -top-2 -right-2 w-8 h-8 bg-purple-500/30 rounded-full animate-pulse"></div>
                </div>
                <h3 class="text-3xl font-bold text-gray-900 mb-6">Personalized Care</h3>
                <p class="text-gray-600 leading-relaxed text-lg">
                    Every patient receives a customized treatment plan based on their unique anatomy, 
                    aesthetic goals, and medical history for optimal, natural-looking results.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Revolutionary Team Section -->
<section class="py-32 bg-white relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-redolence-green/10 to-transparent rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-redolence-blue/10 to-transparent rounded-full blur-3xl"></div>
    
    <div class="max-w-7xl mx-auto px-6 relative">
        <!-- Section Header -->
        <div class="text-center mb-20 scroll-reveal">
            <div class="inline-flex items-center bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 px-8 py-4 rounded-full mb-8 border border-redolence-green/20">
                <svg class="w-6 h-6 mr-3 text-redolence-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <span class="font-bold text-lg">MEET OUR SPECIALISTS</span>
            </div>
            
            <h2 class="text-6xl md:text-7xl font-black mb-8 leading-tight">
                World-Class
                <span class="text-revolutionary block">Medical Team</span>
            </h2>
            
            <p class="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Board-certified physicians and licensed medical professionals dedicated to your transformation
            </p>
        </div>
        
        <!-- Team Grid -->
        <div class="medical-grid">
            <?php
            // Include staff functions
            require_once __DIR__ . '/includes/staff_functions.php';
            
            // Get active staff members
            $staffMembers = getActiveStaff();
            
            foreach ($staffMembers as $index => $staff): 
                $delay = ($index * 0.1) + 0.1;
            ?>
            <div class="medical-professional scroll-reveal interactive-element" style="animation-delay: <?= $delay ?>s;">
                <!-- Profile Image -->
                <div class="relative mb-8">
                    <div class="w-40 h-40 bg-gradient-to-br from-redolence-green/30 via-redolence-blue/20 to-purple-500/20 rounded-full flex items-center justify-center mx-auto relative overflow-hidden">
                        <!-- Background Pattern -->
                        <div class="absolute inset-0 opacity-30">
                            <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                                 alt="Medical Professional" class="w-full h-full object-cover">
                        </div>
                        
                        <!-- Initials -->
                        <span class="text-white font-black text-3xl relative z-10">
                            <?php 
                            $nameParts = explode(' ', $staff['name']);
                            echo strtoupper(substr($nameParts[0], 0, 1)) . (isset($nameParts[1]) ? strtoupper(substr($nameParts[1], 0, 1)) : '');
                            ?>
                        </span>
                        
                        <!-- Decorative Ring -->
                        <div class="absolute inset-0 border-4 border-white/50 rounded-full"></div>
                    </div>
                    
                    <!-- Medical Badge -->
                    <div class="absolute -top-2 -right-2 w-12 h-12 bg-redolence-green rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                
                <!-- Professional Info -->
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Dr. <?= $staff['name'] ?></h3>
                    <p class="text-redolence-blue font-semibold text-lg mb-4">Board-Certified Medical Specialist</p>
                    
                    <!-- Credentials -->
                    <div class="flex flex-wrap justify-center gap-2 mb-6">
                        <span class="bg-redolence-green/15 text-redolence-green px-3 py-1 rounded-full text-sm font-medium">MD</span>
                        <span class="bg-redolence-blue/15 text-redolence-blue px-3 py-1 rounded-full text-sm font-medium">Aesthetic Medicine</span>
                        <span class="bg-purple-500/15 text-purple-600 px-3 py-1 rounded-full text-sm font-medium">15+ Years</span>
                    </div>
                    
                    <!-- Bio -->
                    <p class="text-gray-600 leading-relaxed mb-6">
                        Specialized in advanced medical aesthetics with extensive training in facial anatomy, 
                        non-surgical procedures, and patient safety protocols.
                    </p>
                    
                    <!-- Action Button -->
                    <button class="btn-revolutionary w-full">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        Book with Dr. <?= explode(' ', $staff['name'])[0] ?>
                    </button>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 md:py-24 bg-redolence-green">
    <div class="max-w-4xl mx-auto px-4 md:px-6 text-center text-white">
        <h2 class="text-3xl md:text-5xl font-bold mb-6 leading-tight">
            Ready to Transform Your Beauty Journey?
        </h2>
        
        <p class="text-lg md:text-xl mb-8 opacity-90">
            Experience the future of medical aesthetics with our revolutionary treatments
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-2xl mx-auto">
            <a href="contact.php" class="w-full sm:w-auto bg-white text-redolence-green px-6 py-3 rounded-lg font-semibold text-center hover:bg-gray-100 transition-colors">
                Schedule Your Consultation
            </a>
            
            <a href="tel:+255781985757" class="w-full sm:w-auto border-2 border-white text-white px-6 py-3 rounded-lg font-semibold text-center hover:bg-white hover:text-redolence-green transition-colors">
                Call: +255 781 985 757
            </a>
        </div>
    </div>
</section>

<!-- Enhanced Revolutionary JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS (Animate On Scroll)
    AOS.init({
        duration: 1000,
        easing: 'ease-out-cubic',
        once: true,
        offset: 100,
        delay: 0
    });

    // Advanced Counter Animation
    function animateCounter(element) {
        const target = parseInt(element.getAttribute('data-target'));
        const isDecimal = element.getAttribute('data-target').includes('.');
        const increment = isDecimal ? target / 100 : target / 50;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }

            if (isDecimal) {
                element.textContent = current.toFixed(1);
            } else {
                element.textContent = Math.floor(current).toLocaleString();
            }
        }, 40);
    }

    // Counter Animation Observer
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                if (!counter.classList.contains('animated')) {
                    counter.classList.add('animated');
                    setTimeout(() => animateCounter(counter), 200);
                }
            }
        });
    }, { threshold: 0.5 });

    // Observe all counters
    document.querySelectorAll('.counter').forEach(counter => {
        counterObserver.observe(counter);
    });

    // Ultra Enhanced Story Card Interactions
    document.querySelectorAll('.premium-story-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-15px) scale(1.03)';
            this.style.boxShadow = '0 35px 70px rgba(0, 0, 0, 0.2)';

            // Animate icon
            const icon = this.querySelector('.icon-container');
            if (icon) {
                icon.style.transform = 'translateY(-5px) rotate(10deg) scale(1.1)';
            }

            // Animate progress dots
            const dots = this.querySelectorAll('.w-2.h-2');
            dots.forEach((dot, index) => {
                setTimeout(() => {
                    dot.style.transform = 'scale(1.2)';
                }, index * 100);
            });
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.1)';

            // Reset icon
            const icon = this.querySelector('.icon-container');
            if (icon) {
                icon.style.transform = 'translateY(0) rotate(0deg) scale(1)';
            }

            // Reset progress dots
            const dots = this.querySelectorAll('.w-2.h-2');
            dots.forEach(dot => {
                dot.style.transform = 'scale(1)';
            });
        });
    });

    // Legacy story card support
    document.querySelectorAll('.story-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-12px) scale(1.02)';
            this.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.1)';
        });
    });

    // Ultra Enhanced Achievement Stats Interactions
    document.querySelectorAll('.premium-achievement-stat').forEach(stat => {
        stat.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-12px) scale(1.08)';
            this.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.2)';

            // Animate counter
            const counter = this.querySelector('.counter');
            if (counter) {
                counter.style.transform = 'scale(1.1)';
                counter.style.color = getComputedStyle(counter).color;
            }

            // Animate progress bar
            const progressBar = this.querySelector('.progress-bar');
            if (progressBar) {
                const targetWidth = progressBar.getAttribute('data-width');
                progressBar.style.width = targetWidth;
            }

            // Animate icon
            const icon = this.querySelector('.w-16.h-16');
            if (icon) {
                icon.style.transform = 'scale(1.15) rotate(5deg)';
            }
        });

        stat.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.1)';

            // Reset counter
            const counter = this.querySelector('.counter');
            if (counter) {
                counter.style.transform = 'scale(1)';
            }

            // Reset icon
            const icon = this.querySelector('.w-16.h-16');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });

    // Legacy achievement stats support
    document.querySelectorAll('.achievement-stat').forEach(stat => {
        stat.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.05)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        });

        stat.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.1)';
        });
    });

    // Visual Container Parallax Effect
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const visualContainers = document.querySelectorAll('.visual-container');

        visualContainers.forEach(container => {
            const speed = 0.1;
            container.style.transform = `translateY(${scrolled * speed}px)`;
        });

        // Floating elements parallax
        const floatingElements = document.querySelectorAll('.floating-element');
        floatingElements.forEach((element, index) => {
            const speed = 0.05 + (index * 0.02);
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });

    // Advanced Scroll Reveal System (keeping existing functionality)
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');

                // Add staggered animation for grid items
                if (entry.target.classList.contains('medical-grid')) {
                    const children = entry.target.children;
                    Array.from(children).forEach((child, index) => {
                        setTimeout(() => {
                            child.classList.add('revealed');
                        }, index * 100);
                    });
                }
            }
        });
    }, observerOptions);

    // Observe all scroll reveal elements
    document.querySelectorAll('.scroll-reveal').forEach(el => {
        observer.observe(el);
    });

    // Interactive Elements (keeping existing functionality)
    document.querySelectorAll('.interactive-element').forEach(element => {
        const isInHero = element.closest('.relative.min-h-screen');

        if (isInHero) {
            element.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.05)';
                this.style.filter = 'brightness(1.1)';
            });

            element.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.filter = 'brightness(1)';
            });
        } else {
            element.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
            });

            element.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        }
    });

    // Enhanced Card Hover Effects
    document.querySelectorAll('.medical-card-revolutionary').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.1)';
        });
    });

    // Enhanced Medical Professional Hover Effects
    document.querySelectorAll('.medical-professional').forEach(professional => {
        professional.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        });

        professional.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.1)';
        });
    });

    // Secondary Visual Hover Effects
    document.querySelectorAll('.secondary-visual').forEach(visual => {
        visual.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.05)';
            this.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.15)';
        });

        visual.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.1)';
        });
    });

    // Enhanced Button Interaction Effects
    document.querySelectorAll('.btn-revolutionary').forEach(button => {
        button.addEventListener('click', function(e) {
            // Create enhanced ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 800);
        });
    });

    // Smooth Scrolling for Internal Links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Enhanced Page Load Animation
    setTimeout(() => {
        document.body.style.opacity = '1';
        document.body.style.transform = 'translateY(0)';
    }, 100);

    // Enhanced Parallax Effect for Hero Section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.morphing-bg');

        parallaxElements.forEach(element => {
            const speed = 0.3;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });

    // Advanced Progress Bar Animations
    const progressObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBars = entry.target.querySelectorAll('.progress-bar');
                progressBars.forEach((bar, index) => {
                    setTimeout(() => {
                        const targetWidth = bar.getAttribute('data-width');
                        bar.style.width = targetWidth;
                    }, index * 200);
                });
            }
        });
    }, { threshold: 0.5 });

    // Observe achievement stats for progress animations
    document.querySelectorAll('.premium-achievement-stat').forEach(stat => {
        progressObserver.observe(stat);
    });

    // Typing Effect for Subtitle
    function typeWriter(element, text, speed = 50) {
        let i = 0;
        element.innerHTML = '';

        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        type();
    }

    // Initialize typing effect when subtitle comes into view
    const typingObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const typingText = entry.target.querySelector('.typing-text');
                if (typingText && !typingText.classList.contains('typed')) {
                    typingText.classList.add('typed');
                    const originalText = typingText.textContent;
                    typeWriter(typingText, originalText, 30);
                }
            }
        });
    }, { threshold: 0.3 });

    // Observe section header for typing effect
    const sectionHeader = document.querySelector('.text-center.mb-24');
    if (sectionHeader) {
        typingObserver.observe(sectionHeader);
    }

    // Enhanced Intersection Observer for Advanced Animations
    const advancedObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');

                // Add staggered animations for child elements
                const children = entry.target.querySelectorAll('.w-2.h-2, .counter, .progress-bar');
                children.forEach((child, index) => {
                    setTimeout(() => {
                        child.style.animation = `slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards`;
                    }, index * 100);
                });
            }
        });
    }, { threshold: 0.1 });

    // Observe elements for advanced animations
    document.querySelectorAll('.premium-story-card, .premium-achievement-stat, .secondary-visual, .story-card, .achievement-stat').forEach(el => {
        advancedObserver.observe(el);
    });
});

// Add Enhanced CSS for animations and effects
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(45deg, rgba(73, 167, 92, 0.4), rgba(88, 148, 210, 0.4));
        transform: scale(0);
        animation: enhanced-ripple-animation 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        pointer-events: none;
    }

    @keyframes enhanced-ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .animate-in {
        animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    body {
        opacity: 0;
        transform: translateY(20px);
        transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Enhanced hover states */
    .story-card:hover .w-16 {
        animation: iconBounce 0.6s ease-in-out;
    }

    @keyframes iconBounce {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .achievement-stat:hover .counter {
        animation: numberPulse 0.4s ease-in-out;
    }

    @keyframes numberPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    /* Smooth transitions for all interactive elements */
    .story-card, .achievement-stat, .secondary-visual, .main-image-container {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Enhanced gradient animation */
    @keyframes gradientShift {
        0%, 100% {
            background-position: 0% 50%;
            filter: hue-rotate(0deg);
        }
        50% {
            background-position: 100% 50%;
            filter: hue-rotate(10deg);
        }
    }
`;
document.head.appendChild(style);
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>