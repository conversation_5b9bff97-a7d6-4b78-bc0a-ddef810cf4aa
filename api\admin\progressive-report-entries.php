<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/ProgressiveReportEntry.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

// Initialize progressive report entry handler
$progressiveReportEntry = new ProgressiveReportEntry();

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            // Get progressive report entries
            if (isset($_GET['report_id'])) {
                $reportId = $_GET['report_id'];
                $orderBy = $_GET['order_by'] ?? 'entry_date DESC';
                
                $result = $progressiveReportEntry->getByReportId($reportId, $orderBy);
                
                if ($result === false) {
                    throw new Exception('Failed to fetch report entries');
                }
                
                echo json_encode([
                    'success' => true,
                    'data' => $result
                ]);
            } elseif (isset($_GET['id'])) {
                $entryId = $_GET['id'];

                $result = $progressiveReportEntry->getById($entryId);

                if ($result === false) {
                    throw new Exception('Report entry not found');
                }

                // Decode HTML entities for admin editing
                if ($result) {
                    $result['treatment'] = html_entity_decode($result['treatment'] ?? '', ENT_QUOTES, 'UTF-8');
                    $result['description'] = html_entity_decode($result['description'] ?? '', ENT_QUOTES, 'UTF-8');
                    $result['notes'] = html_entity_decode($result['notes'] ?? '', ENT_QUOTES, 'UTF-8');
                }

                echo json_encode([
                    'success' => true,
                    'data' => $result
                ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            } else {
                throw new Exception('Report ID or Entry ID is required');
            }
            break;
            
        case 'POST':
            // Create new progressive report entry
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                throw new Exception('Invalid JSON input');
            }
            
            // Validate required fields
            if (empty($input['report_id'])) {
                throw new Exception('Report ID is required');
            }
            
            $input['created_by'] = $_SESSION['user_id'];
            
            $result = $progressiveReportEntry->create($input['report_id'], $input);
            
            if ($result === false) {
                throw new Exception('Failed to create report entry');
            }
            
            echo json_encode([
                'success' => true,
                'data' => $result,
                'message' => 'Report entry created successfully'
            ]);
            break;
            
        case 'PUT':
            // Update progressive report entry
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || empty($input['id'])) {
                throw new Exception('Entry ID is required');
            }
            
            // Check if entry exists and user can edit it
            $existingEntry = $progressiveReportEntry->getById($input['id']);
            if (!$existingEntry) {
                throw new Exception('Report entry not found');
            }
            
            if (!$progressiveReportEntry->canEdit($input['id'], $_SESSION['user_id'], $_SESSION['user_role'])) {
                throw new Exception('You do not have permission to edit this entry');
            }
            
            $result = $progressiveReportEntry->update($input['id'], $input);
            
            if ($result === false) {
                throw new Exception('Failed to update report entry');
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Report entry updated successfully'
            ]);
            break;
            
        case 'DELETE':
            // Delete progressive report entry
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || empty($input['id'])) {
                throw new Exception('Entry ID is required');
            }
            
            // Check if entry exists and user can edit it
            $existingEntry = $progressiveReportEntry->getById($input['id']);
            if (!$existingEntry) {
                throw new Exception('Report entry not found');
            }
            
            if (!$progressiveReportEntry->canEdit($input['id'], $_SESSION['user_id'], $_SESSION['user_role'])) {
                throw new Exception('You do not have permission to delete this entry');
            }
            
            $result = $progressiveReportEntry->delete($input['id']);
            
            if ($result === false) {
                throw new Exception('Failed to delete report entry');
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Report entry deleted successfully'
            ]);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
