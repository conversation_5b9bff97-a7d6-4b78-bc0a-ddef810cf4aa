-- Gallery Items Table Creation
-- This table stores all gallery items including before/after results and clinic photos

CREATE TABLE IF NOT EXISTS `gallery_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `category` enum('before-after','clinic') NOT NULL DEFAULT 'clinic',
    `treatment` varchar(255) DEFAULT NULL,
    `description` text NOT NULL,
    `duration` varchar(100) DEFAULT NULL,
    `sessions` int(11) DEFAULT NULL,
    `before_image` varchar(500) DEFAULT NULL,
    `after_image` varchar(500) DEFAULT NULL,
    `image` varchar(500) DEFAULT NULL,
    `sort_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    <PERSON>EY `idx_category` (`category`),
    <PERSON>EY `idx_active` (`is_active`),
    <PERSON>EY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add indexes for better performance
CREATE INDEX idx_gallery_category_active ON gallery_items(category, is_active);
CREATE INDEX idx_gallery_sort_active ON gallery_items(sort_order, is_active);
