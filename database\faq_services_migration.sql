-- FAQ to Services Relationship Migration
-- Redolence Medi Aesthetics - FAQ Enhancement System
-- This migration adds support for linking FAQ items to services

USE flix_salonce;

-- Create FAQ-Services junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS faq_services (
    id VARCHAR(36) PRIMARY KEY,
    faq_id INT NOT NULL,
    service_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (faq_id) REFERENCES faqs(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE KEY unique_faq_service (faq_id, service_id),
    INDEX idx_faq_services_faq_id (faq_id),
    INDEX idx_faq_services_service_id (service_id)
);

-- Add service_link_text field to FAQs table for custom link text
ALTER TABLE faqs 
ADD COLUMN IF NOT EXISTS service_link_text VARCHAR(255) DEFAULT NULL COMMENT 'Custom text for service links (e.g., "Learn more about this treatment")';

-- Add indexes for better performance
ALTER TABLE faqs 
ADD INDEX IF NOT EXISTS idx_faqs_category (category),
ADD INDEX IF NOT EXISTS idx_faqs_active (is_active),
ADD INDEX IF NOT EXISTS idx_faqs_display_order (display_order);

-- Create a view for easy FAQ with services retrieval
CREATE OR REPLACE VIEW faq_with_services AS
SELECT 
    f.*,
    GROUP_CONCAT(
        CONCAT(s.id, ':', s.name, ':', COALESCE(s.price, 0), ':', COALESCE(s.duration, 0))
        SEPARATOR '||'
    ) as linked_services
FROM faqs f
LEFT JOIN faq_services fs ON f.id = fs.faq_id
LEFT JOIN services s ON fs.service_id = s.id AND s.is_active = 1
GROUP BY f.id, f.category, f.question, f.answer, f.display_order, f.is_active, f.created_at, f.updated_at, f.service_link_text
ORDER BY f.category, f.display_order, f.id;

-- Insert sample FAQ-Service relationships (will be populated after content creation)
-- This is a placeholder for the relationships that will be created in Phase 3

-- Note: Stored procedures are optional and can be created separately if needed
-- They are commented out here to avoid syntax issues during migration

-- Example stored procedure for getting services linked to a specific FAQ:
-- DELIMITER //
-- CREATE PROCEDURE GetFAQServices(IN faq_id_param INT)
-- BEGIN
--     SELECT s.id, s.name, s.description, s.price, s.duration, s.category, s.image
--     FROM services s
--     INNER JOIN faq_services fs ON s.id = fs.service_id
--     WHERE fs.faq_id = faq_id_param AND s.is_active = 1
--     ORDER BY s.name;
-- END //
-- DELIMITER ;

-- Example stored procedure for getting FAQs linked to a specific service:
-- DELIMITER //
-- CREATE PROCEDURE GetServiceFAQs(IN service_id_param VARCHAR(36))
-- BEGIN
--     SELECT f.id, f.category, f.question, f.answer, f.service_link_text, f.display_order, f.is_active
--     FROM faqs f
--     INNER JOIN faq_services fs ON f.id = fs.faq_id
--     WHERE fs.service_id = service_id_param AND f.is_active = 1
--     ORDER BY f.category, f.display_order, f.id;
-- END //
-- DELIMITER ;

-- Note: UUID generation is handled by MySQL's built-in UUID() function
-- No custom function needed

-- Add some sample data for testing (will be replaced in Phase 3)
-- This ensures the system works before we add real content

-- Note: The actual FAQ-Service relationships will be created in Phase 3
-- after we clean the existing content and create new medical aesthetics FAQs

-- Verification queries (uncomment to test after migration)
-- SELECT 'FAQ Services Migration Completed Successfully' as status;
-- SELECT COUNT(*) as faq_count FROM faqs;
-- SELECT COUNT(*) as service_count FROM services WHERE is_active = 1;
-- SELECT COUNT(*) as faq_service_relationships FROM faq_services;
