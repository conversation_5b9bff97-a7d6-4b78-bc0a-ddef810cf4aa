<?php
/**
 * Get Customer API
 * Redolence Medi Aesthetics - Advanced Medical Beauty Management System
 */

require_once __DIR__ . '/../../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Get customer ID from query parameter
$customerId = $_GET['id'] ?? '';

if (empty($customerId)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Customer ID is required']);
    exit;
}

try {
    $customer = getCustomerById($customerId);
    
    if (!$customer) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Customer not found']);
        exit;
    }
    
    // Return customer data
    echo json_encode([
        'success' => true,
        'customer' => $customer
    ]);

} catch (Exception $e) {
    error_log("Get customer API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
?>
