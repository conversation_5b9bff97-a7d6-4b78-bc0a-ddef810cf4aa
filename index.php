<?php
/**
 * Main Landing Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/blog_functions.php';

// Get featured services (with fallback for missing fields)
try {
    // Try with new medical fields
    $featuredServices = $database->fetchAll(
        "SELECT *,
                COALESCE(sort_order, 0) as sort_order,
                COALESCE(featured, 0) as featured,
                COALESCE(popular, 0) as popular,
                COALESCE(new_treatment, 0) as new_treatment,
                COALESCE(session_frequency, '') as session_frequency,
                COALESCE(technology_used, '') as technology_used
         FROM services WHERE is_active = 1 ORDER BY featured DESC, popular DESC, sort_order ASC, RAND() LIMIT 6"
    );
} catch (Exception $e) {
    // Fallback for when medical fields don't exist yet
    error_log("Medical fields not found in index.php, using fallback: " . $e->getMessage());
    $featuredServices = $database->fetchAll(
        "SELECT *,
                0 as sort_order,
                0 as featured,
                0 as popular,
                0 as new_treatment,
                '' as session_frequency,
                '' as technology_used
         FROM services WHERE is_active = 1 ORDER BY RAND() LIMIT 6"
    );
}

// Ensure we have an array
$featuredServices = $featuredServices ?: [];

// Get featured packages
$featuredPackages = $database->fetchAll(
    "SELECT * FROM packages WHERE is_active = 1 ORDER BY created_at DESC LIMIT 3"
);

// Get active offers
$activeOffers = $database->fetchAll(
    "SELECT * FROM offers WHERE is_active = 1 AND valid_from <= NOW() AND valid_to >= NOW() ORDER BY created_at DESC LIMIT 3"
);

// Get gallery images
$galleryImages = $database->fetchAll(
    "SELECT * FROM gallery WHERE is_active = 1 ORDER BY created_at DESC LIMIT 8"
);

// Get recent blog posts
$blogPosts = $database->fetchAll(
    "SELECT * FROM blog_posts WHERE status = 'published' ORDER BY publish_date DESC LIMIT 3"
);

$pageTitle = "Advanced Medical Aesthetics";
$pageDescription = "Experience advanced medical aesthetics at Redolence Medi Aesthetics. Professional treatments with cutting-edge technology and personalized care.";

include __DIR__ . '/includes/header.php';
?>

<!-- Preload critical ultra-high-quality hero images for better performance -->
<link rel="preload" as="image" href="https://images.unsplash.com/photo-**********-c5ae44394b79?q=80&w=725&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" media="(min-width: 768px)">
<link rel="preload" as="image" href="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=100" media="(min-width: 768px)">
<link rel="preload" as="image" href="https://images.unsplash.com/photo-1540555700478-4be289fbecef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=100" media="(min-width: 768px)">

<style>
/* Lazy loading styles */
.lazy-image {
    opacity: 0;
    transition: opacity 0.4s ease-in-out;
}

.lazy-image.loaded {
    opacity: 1;
}

/* Treatment description styles */
.treatment-description h3 {
    color: #1f2937;
    font-weight: 700;
    font-size: 1.125rem;
    margin: 1rem 0 0.5rem 0;
}

.treatment-description h4 {
    color: #374151;
    font-weight: 600;
    font-size: 1rem;
    margin: 0.75rem 0 0.5rem 0;
}

.treatment-description ul {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0;
}

.treatment-description li {
    padding: 0.25rem 0;
    color: #4b5563;
}

.treatment-description p {
    color: #6b7280;
    line-height: 1.6;
    margin: 0.5rem 0;
}

.treatment-description strong {
    color: #1f2937;
    font-weight: 600;
}

/* Prose styling for modal */
.prose-redolence {
    color: #374151;
    line-height: 1.7;
}

.prose-redolence h1,
.prose-redolence h2,
.prose-redolence h3 {
    color: #49a75c;
    font-weight: 700;
    font-size: 1.25rem;
    margin: 1.5rem 0 0.75rem 0;
    line-height: 1.4;
}

.prose-redolence h4,
.prose-redolence h5,
.prose-redolence h6 {
    color: #2563eb;
    font-weight: 600;
    font-size: 1.125rem;
    margin: 1rem 0 0.5rem 0;
    line-height: 1.4;
}

.prose-redolence ul,
.prose-redolence ol {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.prose-redolence li {
    padding: 0.375rem 0;
    color: #374151;
    font-size: 0.95rem;
    line-height: 1.6;
}

.prose-redolence p {
    color: #4b5563;
    line-height: 1.7;
    margin: 0.75rem 0;
}

.prose-redolence strong,
.prose-redolence b {
    color: #1f2937;
    font-weight: 600;
}

.prose-redolence em,
.prose-redolence i {
    font-style: italic;
    color: #6b7280;
}

/* First paragraph styling */
.prose-redolence p:first-child {
    margin-top: 0;
}

.prose-redolence p:last-child {
    margin-bottom: 0;
}

.lazy-placeholder {
    background: linear-gradient(90deg, #141414 25%, #1a1a1a 50%, #141414 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Loading skeleton for images */
.image-skeleton {
    background: linear-gradient(90deg, #0a0a0a 25%, #141414 50%, #0a0a0a 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    position: relative;
}

.image-skeleton::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.3;
}

/* Fade-in animation for loaded images */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.fade-in-loaded {
    animation: fadeIn 0.5s ease-out;
}

/* Back to Top Button Styles */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, #49a75c, #3d8b4e);
    border: 2px solid rgba(73, 167, 92, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 25px rgba(73, 167, 92, 0.3);
    backdrop-filter: blur(10px);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: linear-gradient(135deg, #5bb36a, #49a75c);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 15px 35px rgba(73, 167, 92, 0.4);
    border-color: rgba(73, 167, 92, 0.6);
}

.back-to-top:active {
    transform: translateY(0) scale(0.95);
}

.back-to-top svg {
    width: 1.25rem;
    height: 1.25rem;
    color: #000000;
    transition: transform 0.2s ease;
}

.back-to-top:hover svg {
    transform: translateY(-1px);
}

/* Responsive adjustments for back to top button */
@media (max-width: 768px) {
    .back-to-top {
        bottom: 1.5rem;
        right: 1.5rem;
        width: 3rem;
        height: 3rem;
    }

    .back-to-top svg {
        width: 1rem;
        height: 1rem;
    }

    /* Hide navigation arrows on mobile - use swipe gestures instead */
    .hero-prev, .hero-next {
        display: none;
    }

    /* Show dots indicator more prominently on mobile */
    .hero-carousel .absolute.bottom-8 {
        bottom: 1.5rem;
    }

    .hero-dot {
        width: 0.75rem !important;
        height: 0.75rem !important;
    }
}

/* Hero Section Parallax Styles */
.hero-section {
    position: relative;
    overflow: hidden;
}

.parallax-bg {
    position: absolute;
    top: -20%;
    left: 0;
    width: 100%;
    height: 120%;
    will-change: transform;
    z-index: 1;
}

.parallax-element {
    will-change: transform;
    transition: transform 0.1s ease-out;
}

/* Enhanced Card Hover Effects */
.hero-main-card, .hero-side-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    cursor: pointer;
}

.hero-main-card:hover, .hero-side-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

/* Smooth animations for interactive elements */
.hero-main-card img, .hero-side-card img {
    transition: transform 0.6s ease;
}

.hero-main-card:hover img, .hero-side-card:hover img {
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-main-card {
        margin-bottom: 2rem;
    }

    .hero-side-card {
        max-width: 300px;
        margin: 0 auto 1.5rem;
    }
}

@media (max-width: 768px) {
    /* Disable parallax on mobile for better performance */
    .parallax-element, .parallax-bg {
        transform: none !important;
    }

    .hero-section {
        min-height: auto;
        padding: 2rem 0;
    }

    .hero-main-card, .hero-side-card {
        transform: none !important;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
    }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #49a75c;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #3d8b4e;
}
</style>

<!-- Hero Section - Reference Design Match -->
<section class="hero-section relative min-h-screen overflow-hidden">
  <!-- Full-width background image with accessibility overlay -->
  <div class="absolute inset-0 parallax-bg" data-speed="0.3" aria-hidden="true">
    <img src="https://images.unsplash.com/photo-**********-c5ae44394b79?q=80&w=1920&auto=format&fit=crop&ixlib=rb-4.1.0" alt="" class="w-full h-full object-cover" loading="eager" decoding="async">
    <div class="absolute inset-0 bg-black/60"></div>
  </div>

  <!-- Floating Decorative Elements -->
  <div class="absolute top-20 right-20 w-32 h-32 bg-redolence-green/10 rounded-full blur-3xl animate-pulse"></div>
  <div class="absolute bottom-20 left-20 w-24 h-24 bg-redolence-blue/10 rounded-full blur-2xl animate-pulse" style="animation-delay: 2s;"></div>

  <div class="relative z-10 max-w-7xl mx-auto px-6 py-16">
    <div class="grid grid-cols-12 gap-8 items-stretch min-h-[85vh]">

      <!-- Left Content - "Heal. Enhance. Transform." -->
      <div class="col-span-12 lg:col-span-4 space-y-8">
        <div class="space-y-4">
          <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight">
            <div class="block">Heal Skin.</div>
            <div class="block">Enhance.</div>
            <div class="block">
              <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                Transform.
              </span>
            </div>
          </h1>
        </div>

        <!-- Call to Action Slideshow Section -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-gray-100/50 overflow-hidden">
          <div class="cta-slideshow relative h-16">
            <!-- CTA Slide 1 - Contact Us -->
            <div class="cta-slide absolute inset-0 flex items-center justify-between opacity-100 transition-all duration-500" data-slide="0">
              <div class="flex-1">
                <h4 class="text-lg font-bold text-gray-900 mb-1">Ready to Transform?</h4>
                <p class="text-sm text-gray-600">Book your consultation today</p>
              </div>
              <a href="<?= getBasePath() ?>/contact" class="bg-redolence-green hover:bg-green-600 text-white px-6 py-3 rounded-xl font-semibold transition-all hover:scale-105 shadow-lg">
                Contact Us
              </a>
            </div>

            <!-- CTA Slide 2 - Services -->
            <div class="cta-slide absolute inset-0 flex items-center justify-between opacity-0 transition-all duration-500" data-slide="1">
              <div class="flex-1">
                <h4 class="text-lg font-bold text-gray-900 mb-1">Explore Treatments</h4>
                <p class="text-sm text-gray-600">Discover our premium services</p>
              </div>
              <a href="<?= getBasePath() ?>/services" class="bg-redolence-blue hover:bg-blue-600 text-white px-6 py-3 rounded-xl font-semibold transition-all hover:scale-105 shadow-lg">
                View Services
              </a>
            </div>

            <!-- CTA Slide 3 - About -->
            <div class="cta-slide absolute inset-0 flex items-center justify-between opacity-0 transition-all duration-500" data-slide="2">
              <div class="flex-1">
                <h4 class="text-lg font-bold text-gray-900 mb-1">Learn About Us</h4>
                <p class="text-sm text-gray-600">Meet our expert team</p>
              </div>
              <a href="<?= getBasePath() ?>/about" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-xl font-semibold transition-all hover:scale-105 shadow-lg">
                About Us
              </a>
            </div>

            <!-- CTA Slide 4 - Testimonials -->
            <div class="cta-slide absolute inset-0 flex items-center justify-between opacity-0 transition-all duration-500" data-slide="3">
              <div class="flex-1">
                <h4 class="text-lg font-bold text-gray-900 mb-1">Client Stories</h4>
                <p class="text-sm text-gray-600">Read success testimonials</p>
              </div>
              <a href="<?= getBasePath() ?>/testimonials" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-xl font-semibold transition-all hover:scale-105 shadow-lg">
                Testimonials
              </a>
            </div>
          </div>

          <!-- Slideshow Indicators -->
          <div class="flex justify-center mt-2 space-x-2">
            <button class="cta-indicator w-2 h-2 rounded-full bg-redolence-green transition-all duration-300" data-slide="0"></button>
            <button class="cta-indicator w-2 h-2 rounded-full bg-gray-300 hover:bg-gray-400 transition-all duration-300" data-slide="1"></button>
            <button class="cta-indicator w-2 h-2 rounded-full bg-gray-300 hover:bg-gray-400 transition-all duration-300" data-slide="2"></button>
            <button class="cta-indicator w-2 h-2 rounded-full bg-gray-300 hover:bg-gray-400 transition-all duration-300" data-slide="3"></button>
          </div>
        </div>
      </div>

      <!-- Right Content - Interactive Hover Cards Layout -->
      <div class="col-span-12 lg:col-span-8">
        <div class="flex gap-6 h-[600px]" id="hero-cards-container">

          <!-- Card 1 - Medical Aesthetics -->
          <div class="hero-hover-card bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 relative transition-all duration-500 ease-out cursor-pointer" data-card="1">
            <div class="relative h-full overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-**********-c5ae44394b79?q=80&w=725&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                alt="Advanced Medical Aesthetics"
                class="w-full h-full object-cover transition-transform duration-700 ease-out"
                loading="eager"
                decoding="async"
                style="image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;"
              >
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>

              <!-- Card Content Overlay - High Quality -->
              <div class="absolute bottom-8 left-8 text-white card-content transition-all duration-500 z-10">
                <div class="mb-4 transform transition-transform duration-500">
                  <span class="inline-block bg-redolence-green text-white text-xs font-bold px-4 py-2 rounded-full shadow-lg border border-white/20">
                    PREMIUM
                  </span>
                </div>
                <h3 class="card-title text-2xl font-bold mb-4 transition-all duration-500 drop-shadow-lg">Medical Aesthetics</h3>
                <h4 class="card-subtitle text-base mb-4 transition-all duration-500 opacity-90 drop-shadow-md">Advanced Treatments</h4>
                <div class="card-number text-4xl font-bold transition-all duration-500 drop-shadow-xl">
                  <span class="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent">100+</span>
                </div>
                <p class="text-sm opacity-80 mt-3 drop-shadow-sm font-medium">Professional Procedures</p>
                <div class="card-mobile-text hidden">Medical Aesthetics</div>
              </div>
            </div>
          </div>

          <!-- Card 2 - Injectable Treatments -->
          <div class="hero-hover-card bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 relative transition-all duration-500 ease-out cursor-pointer" data-card="2">
            <div class="relative h-full overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1692464897435-ec117f40438f?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                alt="Injectable Treatments"
                class="w-full h-full object-cover transition-transform duration-700 ease-out"
                loading="eager"
                decoding="async"
                style="image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;"
              >
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>

              <!-- Card Content Overlay - High Quality -->
              <div class="absolute bottom-8 left-8 text-white card-content transition-all duration-500 z-10">
                <div class="mb-4 transform transition-transform duration-500">
                  <span class="inline-block bg-redolence-blue text-white text-xs font-bold px-4 py-2 rounded-full shadow-lg border border-white/20">
                    POPULAR
                  </span>
                </div>
                <h3 class="card-title text-2xl font-bold mb-4 transition-all duration-500 drop-shadow-lg">Injectable</h3>
                <h4 class="card-subtitle text-base mb-4 transition-all duration-500 opacity-90 drop-shadow-md">Botox & Fillers</h4>
                <div class="card-number text-4xl font-bold transition-all duration-500 drop-shadow-xl">
                  <span class="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent">50+</span>
                </div>
                <p class="text-sm opacity-80 mt-3 drop-shadow-sm font-medium">Treatment Options</p>
                <div class="card-mobile-text hidden">Botox & Fillers</div>
              </div>
            </div>
          </div>

          <!-- Card 3 - Wellness Treatments -->
          <div class="hero-hover-card bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 relative transition-all duration-500 ease-out cursor-pointer" data-card="3">
            <div class="relative h-full overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1506003094589-53954a26283f?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                alt="Wellness Treatments"
                class="w-full h-full object-cover transition-transform duration-700 ease-out"
                loading="eager"
                decoding="async"
                style="image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;"
              >
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>

              <!-- Card Content Overlay - High Quality -->
              <div class="absolute bottom-8 left-8 text-white card-content transition-all duration-500 z-10">
                <div class="mb-4 transform transition-transform duration-500">
                  <span class="inline-block bg-purple-500 text-white text-xs font-bold px-4 py-2 rounded-full shadow-lg border border-white/20">
                    WELLNESS
                  </span>
                </div>
                <h3 class="card-title text-2xl font-bold mb-4 transition-all duration-500 drop-shadow-lg">Wellness</h3>
                <h4 class="card-subtitle text-base mb-4 transition-all duration-500 opacity-90 drop-shadow-md">Holistic Care</h4>
                <div class="card-number text-4xl font-bold transition-all duration-500 drop-shadow-xl">
                  <span class="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent">30+</span>
                </div>
                <p class="text-sm opacity-80 mt-3 drop-shadow-sm font-medium">Therapeutic Services</p>
                <div class="card-mobile-text hidden">Wellness Care</div>
              </div>
            </div>
          </div>

        </div>
      </div>

    </div>
  </div>

  <!-- Enhanced Parallax & Animation Script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check if device supports smooth scrolling and isn't mobile
      const isMobile = window.innerWidth <= 768;
      const supportsParallax = !isMobile && 'requestAnimationFrame' in window;

      if (supportsParallax) {
        // Smooth parallax effect with performance optimization
        let scrollY = 0;
        let ticking = false;

        function updateScrollY() {
          scrollY = window.pageYOffset;
        }

        function parallaxEffect() {
          const parallaxElements = document.querySelectorAll('.parallax-element');
          const parallaxBg = document.querySelector('.parallax-bg');

          // Background parallax with bounds checking
          if (parallaxBg && scrollY < window.innerHeight * 2) {
            const speed = parseFloat(parallaxBg.dataset.speed) || 0.5;
            const yPos = scrollY * speed;
            parallaxBg.style.transform = `translate3d(0, ${yPos}px, 0)`;
          }

          // Element parallax with intersection observer optimization
          parallaxElements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const isVisible = rect.bottom >= 0 && rect.top <= window.innerHeight;

            if (isVisible) {
              const speed = parseFloat(element.dataset.speed) || 0.3;
              const yPos = -(scrollY * speed);
              element.style.transform = `translate3d(0, ${yPos}px, 0)`;
            }
          });

          ticking = false;
        }

        function requestParallaxUpdate() {
          if (!ticking) {
            requestAnimationFrame(parallaxEffect);
            ticking = true;
          }
        }

        // Optimized scroll listener
        window.addEventListener('scroll', () => {
          updateScrollY();
          requestParallaxUpdate();
        }, { passive: true });

        // Initial call
        updateScrollY();
        parallaxEffect();
      }

      // Enhanced dynamic hover card system with auto-slideshow
      const hoverCards = document.querySelectorAll('.hero-hover-card');
      const cardsContainer = document.getElementById('hero-cards-container');
      let currentActiveCard = 0;
      let cardAutoSlideInterval;
      let isUserHovering = false;

      // Auto-expand cards function
      function autoExpandCard(index) {
        if (isUserHovering) return;

        hoverCards.forEach((card, i) => {
          if (i === index) {
            card.style.flex = '2.5';
            card.style.transform = 'translateY(-12px) scale(1.02)';
            card.style.boxShadow = '0 30px 60px rgba(73, 167, 92, 0.2), 0 0 0 1px rgba(73, 167, 92, 0.1)';
            card.style.zIndex = '10';
          } else {
            card.style.flex = '0.8';
            card.style.transform = 'translateY(0) scale(0.95)';
            card.style.opacity = '0.7';
            card.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
          }
        });
      }

      // Reset all cards to equal size
      function resetCards() {
        if (isUserHovering) return;

        hoverCards.forEach(card => {
          card.style.flex = '1';
          card.style.transform = 'translateY(0) scale(1)';
          card.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1)';
          card.style.opacity = '1';
          card.style.zIndex = '1';
        });
      }

      // Start auto-slideshow for cards
      function startCardSlideshow() {
        cardAutoSlideInterval = setInterval(() => {
          if (!isUserHovering) {
            currentActiveCard = (currentActiveCard + 1) % hoverCards.length;
            autoExpandCard(currentActiveCard);

            // Reset after 2 seconds, then move to next
            setTimeout(() => {
              if (!isUserHovering) {
                resetCards();
              }
            }, 8000);
          }
        }, 12000);
      }

      // Stop auto-slideshow
      function stopCardSlideshow() {
        clearInterval(cardAutoSlideInterval);
      }

      hoverCards.forEach((card, index) => {
        // Enhanced hover effects with auto-slideshow control
        card.addEventListener('mouseenter', function(e) {
          isUserHovering = true;
          stopCardSlideshow();

          // Manual hover effect
          hoverCards.forEach((otherCard, otherIndex) => {
            if (otherIndex !== index) {
              setTimeout(() => {
                otherCard.style.flex = '0.8';
                otherCard.style.transform = 'translateY(0) scale(0.95)';
                otherCard.style.opacity = '0.7';
                otherCard.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
              }, otherIndex * 50);
            }
          });

          // Expand hovered card
          this.style.flex = '2.5';
          this.style.transform = 'translateY(-12px) scale(1.02)';
          this.style.boxShadow = '0 30px 60px rgba(73, 167, 92, 0.2), 0 0 0 1px rgba(73, 167, 92, 0.1)';
          this.style.zIndex = '10';
        });

        card.addEventListener('mouseleave', function(e) {
          isUserHovering = false;

          // Reset all cards smoothly
          hoverCards.forEach((otherCard, otherIndex) => {
            setTimeout(() => {
              otherCard.style.flex = '1';
              otherCard.style.transform = 'translateY(0) scale(1)';
              otherCard.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1)';
              otherCard.style.opacity = '1';
              otherCard.style.zIndex = '1';
            }, otherIndex * 30);
          });

          // Restart auto-slideshow after a delay
          setTimeout(() => {
            if (!isUserHovering) {
              startCardSlideshow();
            }
          }, 1000);
        });

        // Add click ripple effect with card-specific colors
        card.addEventListener('click', function(e) {
          const colors = [
            'rgba(73, 167, 92, 0.3)',   // Green for medical
            'rgba(59, 130, 246, 0.3)',  // Blue for injectable
            'rgba(168, 85, 247, 0.3)'   // Purple for wellness
          ];

          const ripple = document.createElement('div');
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = e.clientX - rect.left - size / 2;
          const y = e.clientY - rect.top - size / 2;

          ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: ${colors[index] || colors[0]};
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.8s ease-out;
            pointer-events: none;
            z-index: 1;
          `;

          this.style.position = 'relative';
          this.appendChild(ripple);

          setTimeout(() => {
            ripple.remove();
          }, 800);

          // Navigate to relevant service page
          const servicePages = [
            '<?= getBasePath() ?>/services?category=medical-aesthetics',
            '<?= getBasePath() ?>/services?category=injectables',
            '<?= getBasePath() ?>/services?category=wellness'
          ];

          setTimeout(() => {
            window.location.href = servicePages[index] || servicePages[0];
          }, 200);
        });

        // Add subtle floating animation on idle
        setTimeout(() => {
          card.style.animation = `float${index + 1} 6s ease-in-out infinite`;
        }, index * 200);
      });

      // Initialize card auto-slideshow
      if (hoverCards.length > 0) {
        // Start after initial page load
        setTimeout(() => {
          startCardSlideshow();
        }, 2000);
      }

      // CTA Slideshow functionality
      const ctaSlides = document.querySelectorAll('.cta-slide');
      const ctaIndicators = document.querySelectorAll('.cta-indicator');
      let currentSlide = 0;
      let slideInterval;

      function showSlide(index) {
        // Hide all slides and ensure only the active one is interactive
        ctaSlides.forEach((slide, i) => {
          const isActive = i === index;
          slide.style.opacity = isActive ? '1' : '0';
          slide.style.transform = isActive ? 'translateX(0)' : 'translateX(20px)';
          // Prevent hidden slides from capturing clicks
          slide.style.pointerEvents = isActive ? 'auto' : 'none';
          // Bring active slide above others
          slide.style.zIndex = isActive ? '1' : '0';
          slide.setAttribute('aria-hidden', isActive ? 'false' : 'true');
        });

        // Update indicators
        ctaIndicators.forEach((indicator, i) => {
          if (i === index) {
            indicator.classList.remove('bg-gray-300');
            indicator.classList.add('bg-redolence-green');
          } else {
            indicator.classList.remove('bg-redolence-green');
            indicator.classList.add('bg-gray-300');
          }
        });

        currentSlide = index;
      }

      function nextSlide() {
        const next = (currentSlide + 1) % ctaSlides.length;
        showSlide(next);
      }

      function startSlideshow() {
        slideInterval = setInterval(nextSlide, 4000);
      }

      function stopSlideshow() {
        clearInterval(slideInterval);
      }

      // Initialize slideshow
      if (ctaSlides.length > 0) {
        showSlide(0);
        startSlideshow();

        // Add click handlers to indicators
        ctaIndicators.forEach((indicator, index) => {
          indicator.addEventListener('click', () => {
            stopSlideshow();
            showSlide(index);
            setTimeout(startSlideshow, 5000); // Restart after 5 seconds
          });
        });

        // Pause on hover
        const ctaContainer = document.querySelector('.cta-slideshow');
        if (ctaContainer) {
          ctaContainer.addEventListener('mouseenter', stopSlideshow);
          ctaContainer.addEventListener('mouseleave', startSlideshow);
        }
      }
    });

    // Enhanced CSS for modern components
    const style = document.createElement('style');
    style.textContent = `
      @keyframes ripple {
        to {
          transform: scale(2);
          opacity: 0;
        }
      }

      /* REMOVED CONFLICTING STYLES - Using unified styles below */

      /* Text Clamp for Descriptions */
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      /* REMOVED CONFLICTING HOVER STYLES - Using unified styles below */

      /* CLEAN GRID SYSTEM */
      .treatment-cards-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 2rem;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
      }

      @media (min-width: 768px) {
        .treatment-cards-container {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .treatment-cards-container {
          grid-template-columns: repeat(3, 1fr);
        }
      }

      /* Simple Card Design */
      .treatment-card {
        background: white;
        border-radius: 1.5rem;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid #f3f4f6;
        display: flex;
        flex-direction: column;
        min-height: 400px;
      }

      .treatment-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        border-color: rgba(73,167,92,0.3);
      }

      .treatment-card img {
        width: 100%;
        height: 16rem;
        object-fit: cover;
      }

      .treatment-card .description-content {
        max-height: 0;
        overflow: hidden;
        opacity: 0;
        transition: all 0.4s ease;
      }

      .treatment-card:hover .description-content {
        max-height: 300px;
        opacity: 1;
      }

      /* REMOVED CONFLICTING BUTTON STYLES - Using unified styles below */

      /* Wishlist Heart Animation */
      .wishlist-btn.active {
        background-color: rgb(239 68 68);
        border-color: rgb(239 68 68);
        color: white;
        transform: scale(1.1);
      }

      .wishlist-btn.active svg {
        fill: currentColor;
      }

      /* Loading Skeleton Animation */
      .image-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
      }

      /* Fade In Animation for Images */
      .lazy-image {
        opacity: 0;
        transition: opacity 0.5s ease;
      }

      .lazy-image.loaded {
        opacity: 1;
      }

      /* REMOVED CONFLICTING RESPONSIVE STYLES - Using unified grid system */
    `;
    document.head.appendChild(style);
  </script>

  <style>
    /* Dynamic Hover Cards System */
    #hero-cards-container {
      position: relative;
    }

    /* Ensure hero heading remains readable on image backgrounds */
    .hero-section h1 {
      color: #ffffff;
    }

    .hero-hover-card {
      flex: 1;
      min-width: 200px;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: transform, flex;
      position: relative;
      overflow: hidden;
      z-index: 1;
    }

    /* Default state - all cards equal size */
    .hero-hover-card {
      flex: 1;
      transform: translateY(0) scale(1);
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    /* Hover state - expand the hovered card */
    .hero-hover-card:hover {
      flex: 2.5;
      transform: translateY(-12px) scale(1.02);
      box-shadow: 0 30px 60px rgba(0,0,0,0.25);
      z-index: 10;
    }

    /* Shrink non-hovered cards when one is hovered */
    #hero-cards-container:hover .hero-hover-card:not(:hover) {
      flex: 0.8;
      transform: translateY(0) scale(0.95);
      opacity: 0.7;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    /* Optimized image scaling effects for quality preservation */
    .hero-hover-card img {
      transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1), filter 0.3s ease;
      transform-origin: center center;
    }

    .hero-hover-card:hover img {
      transform: scale3d(1.03, 1.03, 1) translateZ(0);
    }

    #hero-cards-container:hover .hero-hover-card:not(:hover) img {
      transform: scale3d(1.01, 1.01, 1) translateZ(0);
    }

    /* Enhanced text content scaling for better hierarchy */
    .card-content {
      transform: scale(1);
      opacity: 1;
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
      padding: 1.5rem;
      border-radius: 1rem;
      background: rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.1);
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .hero-hover-card:hover .card-title {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      text-shadow: 0 4px 8px rgba(0,0,0,0.7);
      font-weight: 800;
    }

    .hero-hover-card:hover .card-subtitle {
      font-size: 1.25rem;
      margin-bottom: 1rem;
      text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    }

    .hero-hover-card:hover .card-number span {
      font-size: 4rem;
      text-shadow: 0 6px 12px rgba(0,0,0,0.4);
      font-weight: 900;
    }

    #hero-cards-container:hover .hero-hover-card:not(:hover) .card-content {
      transform: scale(0.9);
      opacity: 0.8;
    }

    /* Premium image quality enhancements */
    .hero-hover-card img {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      image-rendering: pixelated;
      image-rendering: high-quality;
      filter: contrast(1.05) saturate(1.05) brightness(1.02);
      transform-origin: center center;
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
      perspective: 1000px;
      will-change: transform, filter;
    }

    .hero-hover-card:hover img {
      filter: contrast(1.08) saturate(1.08) brightness(1.05);
      transform: scale3d(1.03, 1.03, 1) translateZ(0);
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }

    /* Prevent quality loss during transitions */
    .hero-hover-card {
      transform-style: preserve-3d;
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
      contain: layout style paint;
    }

    /* Additional quality preservation */
    .hero-hover-card img {
      -webkit-transform: translateZ(0);
      -moz-transform: translateZ(0);
      -ms-transform: translateZ(0);
      -o-transform: translateZ(0);
      transform: translateZ(0);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Parallax optimization with hardware acceleration */
    .parallax-element, .parallax-bg {
      will-change: transform;
      transform-style: preserve-3d;
      backface-visibility: hidden;
    }

    /* Enhanced search input styling */
    .hero-section input[type="text"] {
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
    }

    .hero-section input[type="text"]:focus {
      background: rgba(255, 255, 255, 1);
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    /* Button hover effects */
    .hero-section button {
      position: relative;
      overflow: hidden;
    }

    .hero-section button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .hero-section button:hover::before {
      left: 100%;
    }

    /* Subtle floating animations for idle state */
    @keyframes float1 {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-10px) rotate(1deg); }
    }

    @keyframes float2 {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-15px) rotate(-1deg); }
    }

    @keyframes float3 {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-8px) rotate(0.5deg); }
    }

    /* Enhanced border effects on hover */
    .hero-hover-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(73, 167, 92, 0.1), rgba(59, 130, 246, 0.1));
      opacity: 0;
      transition: opacity 0.4s ease;
      z-index: 1;
      pointer-events: none;
    }

    .hero-hover-card:hover::before {
      opacity: 1;
    }

    /* Responsive adjustments */
    @media (max-width: 1024px) {
      /* Disable hover effects on tablet/mobile for better touch experience */
      #hero-cards-container:hover .hero-hover-card:not(:hover) {
        flex: 1;
        transform: none;
        opacity: 1;
      }

      .hero-hover-card:hover {
        flex: 1.2;
        transform: translateY(-5px) scale(1.01);
      }

      .hero-hover-card {
        min-width: 150px;
      }
    }

    @media (max-width: 768px) {
      /* Disable parallax on mobile for better performance */
      .parallax-element, .parallax-bg {
        transform: none !important;
      }

      /* Mobile hero section layout */
      .hero-section {
        min-height: 100vh;
        padding: 1rem 0;
      }

      .hero-section .grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        min-height: auto;
      }

      .hero-section .col-span-12 {
        order: 1;
      }

      .hero-section .lg\\:col-span-4 {
        order: 1;
        text-align: center;
        padding: 2rem 1rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .hero-section .lg\\:col-span-8 {
        order: 2;
        padding: 0 1rem;
      }

      /* Mobile cards container */
      .hero-section .h-\[600px\] {
        height: auto;
        flex-direction: column;
        gap: 1.5rem;
        padding: 1rem 0;
      }

      .hero-hover-card {
        min-height: 200px;
        height: 200px;
        flex: none !important;
        width: 100%;
        max-width: 350px;
        margin: 0 auto;
      }

      /* Disable hover effects on mobile */
      #hero-cards-container:hover .hero-hover-card:not(:hover) {
        flex: none;
        transform: none;
        opacity: 1;
      }

      .hero-hover-card:hover {
        flex: none;
        transform: translateY(-3px) scale(1.01);
      }

      /* Mobile text adjustments - Centered Hero */
      .hero-section h1 {
        font-size: 3.5rem;
        line-height: 1.1;
        margin-bottom: 2rem;
        text-align: center;
        max-width: 100%;
      }

      .hero-section h1 > div {
        display: block;
        margin: 0 auto;
      }

      /* Mobile card overlay - Minimal size with meaningful text */
      .card-content {
        bottom: 0.5rem;
        left: 0.5rem;
        right: 0.5rem;
        padding: 0.5rem 0.75rem;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(6px);
        border-radius: 0.5rem;
        border: 1px solid rgba(255, 255, 255, 0.15);
        max-height: 45px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .card-content .mb-4 {
        display: none !important; /* Hide badge on mobile */
      }

      .card-content > div:first-child {
        display: none !important; /* Hide badge container */
      }

      .card-title {
        display: none !important; /* Hide desktop title */
      }

      .card-subtitle {
        display: none !important; /* Hide subtitle */
      }

      .card-number {
        display: none !important; /* Hide number */
      }

      .card-content p {
        display: none !important; /* Hide description */
      }

      /* Show mobile-specific text */
      .card-mobile-text {
        display: block !important;
        font-size: 0.9rem !important;
        font-weight: 700 !important;
        color: white !important;
        text-align: center !important;
        line-height: 1.2 !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.5) !important;
      }

      /* Mobile CTA section - Completely redesigned */
      .hero-section .bg-white\/95 {
        margin: 1rem 0.5rem;
        padding: 0.75rem;
        max-width: none;
        border-radius: 1rem;
      }

      .cta-slideshow {
        height: 50px !important;
        min-height: 50px !important;
        margin-bottom: 0.5rem;
        overflow: hidden;
      }

      .cta-slide {
        flex-direction: row !important;
        text-align: left !important;
        gap: 0.5rem !important;
        padding: 0.5rem !important;
        position: absolute !important;
        align-items: center !important;
        justify-content: space-between !important;
        height: 50px;
        width: 100%;
        top: 0;
        left: 0;
        transition: all 0.5s ease !important;
      }

      .cta-slide div:first-child {
        flex: 1;
        min-width: 0;
      }

      .cta-slide h4 {
        font-size: 0.9rem !important;
        margin-bottom: 0 !important;
        line-height: 1.2 !important;
        font-weight: 600 !important;
      }

      .cta-slide p {
        font-size: 0.7rem !important;
        margin-bottom: 0 !important;
        opacity: 0.7 !important;
        line-height: 1.1 !important;
      }

      .cta-slide a {
        padding: 0.5rem 1rem !important;
        font-size: 0.8rem !important;
        width: auto !important;
        text-align: center !important;
        margin-bottom: 0 !important;
        white-space: nowrap !important;
        flex-shrink: 0 !important;
      }

      /* Compact indicators */
      .hero-section .flex.justify-center {
        margin-top: 0.5rem;
      }

      .hero-section .cta-indicator {
        width: 6px !important;
        height: 6px !important;
      }
    }

    /* Loading animation for images */
    .hero-main-card img, .hero-side-card img {
      opacity: 0;
      animation: fadeInImage 0.8s ease-out forwards;
    }

    @keyframes fadeInImage {
      from {
        opacity: 0;
        transform: scale(1.05);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }

    /* Subtle animations for text elements */
    .hero-section h1 > div {
      opacity: 0;
      transform: translateY(30px);
      animation: slideInUp 0.8s ease-out forwards;
    }

    .hero-section h1 > div:nth-child(1) { animation-delay: 0.1s; }
    .hero-section h1 > div:nth-child(2) { animation-delay: 0.2s; }
    .hero-section h1 > div:nth-child(3) { animation-delay: 0.3s; }

    @keyframes slideInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Card content positioning */
    .hero-main-card .absolute,
    .hero-side-card .absolute {
      z-index: 2;
    }
  </style>
</section>









<!-- Modern Advanced Medical Treatments (New UI/UX) -->
<section id="advanced-medical" class="py-24 relative overflow-hidden bg-gradient-to-br from-gray-50 via-white to-redolence-green/5">
  <!-- Decorative background -->
  <div class="pointer-events-none absolute inset-0 opacity-[0.35]">
    <div class="absolute -top-24 -right-24 w-96 h-96 bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 blur-3xl rounded-full"></div>
    <div class="absolute -bottom-20 -left-20 w-[28rem] h-[28rem] bg-gradient-to-tr from-purple-500/10 to-redolence-blue/10 blur-3xl rounded-full"></div>
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.04) 1px, transparent 0); background-size: 22px 22px;"></div>
  </div>

  <div class="max-w-7xl mx-auto px-6 relative z-10">
    <!-- Header -->
    <div class="text-center mb-12 md:mb-16">
      <div class="inline-flex items-center px-6 py-2 rounded-full text-sm font-semibold bg-white/70 backdrop-blur border border-gray-200 text-redolence-green shadow-sm">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
        Advanced Medical Treatments
      </div>
      <h2 class="mt-6 text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight text-gray-900">
        Transform Your Beauty with <span class="bg-gradient-to-r from-redolence-green via-redolence-blue to-purple-600 bg-clip-text text-transparent">Science</span>
      </h2>
      <p class="mt-4 md:mt-6 text-base md:text-xl text-gray-600 max-w-3xl mx-auto">Experience cutting-edge medical aesthetic treatments that combine advanced technology with personalized care for natural, lasting results.</p>
    </div>

    <?php
      $serviceCategories = [
        [
          'name' => 'Injectable Treatments',
          'icon' => '💉',
          'description' => 'Botox, dermal fillers, and advanced injectables',
          'color' => 'redolence-green',
          'treatments' => ['Botox', 'Dermal Fillers', 'Lip Enhancement']
        ],
        [
          'name' => 'Laser Treatments',
          'icon' => '⚡',
          'description' => 'Advanced laser therapy for skin rejuvenation',
          'color' => 'redolence-blue',
          'treatments' => ['Laser Resurfacing', 'Hair Removal', 'Pigmentation']
        ],
        [
          'name' => 'Skin Rejuvenation',
          'icon' => '✨',
          'description' => 'Medical-grade facial treatments and peels',
          'color' => 'redolence-green',
          'treatments' => ['Chemical Peels', 'Microneedling', 'HydraFacial']
        ],
      ];
    ?>

    <!-- Filter pills 
    <div class="mb-10 md:mb-14">
      <div class="flex gap-3 md:gap-4 overflow-x-auto no-scrollbar justify-start sm:justify-center px-1 -mx-6 sm:mx-0 sm:px-0 snap-x snap-mandatory" role="tablist" aria-label="Treatment Categories">
        <button type="button" data-filter="all" aria-pressed="true" class="pill active inline-flex items-center rounded-full px-4 md:px-5 py-2 text-sm md:text-base font-semibold bg-gray-900 text-white shadow whitespace-nowrap flex-shrink-0 snap-start">
          <span class="mr-2">✨</span> All
        </button>
        <?php foreach ($serviceCategories as $i => $category):
          $slug = strtolower(preg_replace('/[^a-z0-9]+/i','-', $category['name']));
        ?>
          <button type="button" data-filter="<?= $slug ?>" aria-pressed="false" class="pill inline-flex items-center rounded-full px-4 md:px-5 py-2 text-sm md:text-base font-semibold bg-white text-gray-800 border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition whitespace-nowrap flex-shrink-0 snap-start">
            <span class="mr-2 text-lg"><?= $category['icon'] ?></span> <?= htmlspecialchars($category['name']) ?>
          </button>
        <?php endforeach; ?>
      </div>
    </div> -->

    <!-- Cards grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 sm:gap-6 md:gap-8">
      <?php foreach ($serviceCategories as $index => $category):
        $slug = strtolower(preg_replace('/[^a-z0-9]+/i','-', $category['name']));
        $colorClass = $category['color'];
      ?>
        <article data-category="<?= $slug ?>" class="group relative rounded-2xl overflow-hidden bg-white/70 backdrop-blur border border-gray-200 shadow-sm hover:shadow-xl transition-all focus-within:shadow-xl">
          <!-- gradient ring -->
          <div class="pointer-events-none absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity" aria-hidden="true" style="background: radial-gradient(120px 120px at 85% -10%, rgba(59,130,246,0.15), transparent 70%), radial-gradient(140px 140px at -10% 90%, rgba(73,167,92,0.18), transparent 70%);"></div>

          <div class="relative z-10 p-6 md:p-8 h-full flex flex-col">
            <!-- header -->
            <div class="flex items-center mb-6">
              <div class="relative mr-4">
                <div class="w-14 h-14 rounded-2xl grid place-items-center bg-gradient-to-br from-<?= $colorClass ?>/20 to-<?= $colorClass ?>/10 ring-1 ring-<?= $colorClass ?>/30">
                  <span class="text-2xl md:text-3xl select-none"><?= $category['icon'] ?></span>
                </div>
              </div>
              <div>
                <h3 class="text-xl md:text-2xl font-bold text-gray-900 leading-tight group-hover:text-<?= $colorClass ?> transition-colors">
                  <?= htmlspecialchars($category['name']) ?>
                </h3>
                <p class="mt-1 text-sm font-semibold text-<?= $colorClass ?> uppercase tracking-wider">Premium Care</p>
              </div>
            </div>

            <p class="text-gray-600 leading-relaxed mb-5">
              <?= htmlspecialchars($category['description']) ?>
            </p>

            <!-- tags -->
            <ul class="flex flex-wrap gap-2 mb-6">
              <?php foreach ($category['treatments'] as $t): ?>
                <li class="px-3 py-1 rounded-full text-xs font-semibold bg-<?= $colorClass ?>/10 text-<?= $colorClass ?> border border-<?= $colorClass ?>/20">
                  <?= htmlspecialchars($t) ?>
                </li>
              <?php endforeach; ?>
            </ul>

            <!-- actions -->
            <div class="mt-auto grid grid-cols-2 gap-3">
              <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center justify-center rounded-xl px-4 py-3 text-sm md:text-base font-semibold text-white bg-<?= $colorClass ?> hover:scale-[1.02] active:scale-[0.99] transition-transform shadow">
                Book
              </a>
              <a href="<?= getBasePath() ?>/services?category=<?= urlencode($category['name']) ?>" class="inline-flex items-center justify-center rounded-xl px-4 py-3 text-sm md:text-base font-semibold text-<?= $colorClass ?> bg-white border border-<?= $colorClass ?>/40 hover:bg-<?= $colorClass ?>/5 transition">
                Explore →
              </a>
            </div>
          </div>
        </article>
      <?php endforeach; ?>
    </div>

    <!-- CTA panel -->
    <div class="mt-14 md:mt-20">
      <div class="relative overflow-hidden rounded-3xl border border-gray-200 bg-white/70 backdrop-blur shadow-xl">
        <div class="absolute inset-0 opacity-60" aria-hidden="true" style="background: radial-gradient(400px 200px at 100% 0%, rgba(73,167,92,0.08), transparent 70%), radial-gradient(300px 300px at -10% 120%, rgba(59,130,246,0.08), transparent 70%);"></div>
        <div class="relative z-10 px-6 py-10 md:px-12 md:py-14 grid md:grid-cols-[1fr_auto] gap-8 items-center">
          <div>
            <h3 class="text-2xl md:text-3xl font-extrabold text-gray-900">Ready to Begin Your Transformation?</h3>
            <p class="mt-2 md:mt-3 text-gray-600 md:text-lg">Schedule a personalized consultation with our medical aesthetics experts to create your custom treatment plan.</p>
          </div>
          <div class="flex flex-col sm:flex-row gap-3 justify-start md:justify-end">
            <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center justify-center rounded-xl px-6 py-3 text-base font-semibold text-white bg-redolence-green hover:scale-[1.02] active:scale-[0.99] transition-transform shadow">
              Book Consultation
            </a>
            <a href="<?= getBasePath() ?>/services" class="inline-flex items-center justify-center rounded-xl px-6 py-3 text-base font-semibold text-redolence-blue bg-white border-2 border-redolence-blue hover:bg-blue-50 transition">
              View All Treatments
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    /* pills */
    #advanced-medical .pill.active { background: #111827; color: #fff; }
    #advanced-medical .no-scrollbar { scrollbar-width: none; }
    #advanced-medical .no-scrollbar::-webkit-scrollbar { display: none; }
    #advanced-medical [role="tablist"] { -webkit-overflow-scrolling: touch; scroll-snap-type: x mandatory; padding-bottom: .25rem; }
    #advanced-medical .pill { white-space: nowrap; flex-shrink: 0; }

    @media (max-width: 640px) {
      #advanced-medical .grid { gap: 1rem !important; }
      #advanced-medical [data-category] .p-6 { padding: 0.875rem !important; }
      #advanced-medical .pill { font-size: .9rem; padding: .5rem .875rem; }
    }

    /* reveal animation */
    #advanced-medical [data-category] { opacity: 0; transform: translateY(16px); transition: opacity .5s ease, transform .6s cubic-bezier(.2,.8,.2,1); }
    #advanced-medical [data-category].reveal-in { opacity: 1; transform: translateY(0); }
    #advanced-medical [data-category].hide { display: none !important; }
  </style>
  <script>
    (function(){
      const root = document.getElementById('advanced-medical');
      if(!root) return;
      const pills = root.querySelectorAll('[data-filter]');
      const cards = root.querySelectorAll('[data-category]');

      // reveal on intersect
      const io = new IntersectionObserver(entries => {
        entries.forEach(e => { if(e.isIntersecting) e.target.classList.add('reveal-in'); });
      }, { rootMargin: '0px 0px -10% 0px', threshold: 0.1 });
      cards.forEach(c => io.observe(c));

      function setActive(target) {
        pills.forEach(p => p.classList.remove('active'));
        pills.forEach(p => p.setAttribute('aria-pressed','false'));
        target.classList.add('active');
        target.setAttribute('aria-pressed','true');
      }

      function applyFilter(value){
        cards.forEach(card => {
          const show = (value === 'all') || card.getAttribute('data-category') === value;
          card.classList.toggle('hide', !show);
        });
      }

      pills.forEach(pill => {
        pill.addEventListener('click', () => {
          setActive(pill);
          applyFilter(pill.getAttribute('data-filter'));
        });
        pill.addEventListener('keydown', (e) => {
          if(e.key === 'Enter' || e.key === ' '){ e.preventDefault(); pill.click(); }
        });
      });
    })();
  </script>
</section>

<!-- Medical Aesthetics Treatments Section -->
<section class="py-24 bg-gradient-to-br from-gray-50 to-white" hidden>
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-20">
            <div class="inline-flex items-center bg-redolence-green/10 text-redolence-green px-6 py-3 rounded-full text-sm font-bold mb-6">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                Advanced Medical Treatments
            </div>
            <h2 class="text-5xl font-bold text-gray-900 mb-6">
                Transform Your Beauty with
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Science
                </span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Experience cutting-edge medical aesthetic treatments that combine advanced technology with personalized care for natural, lasting results.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <?php
            $serviceCategories = [
                [
                    'name' => 'Injectable Treatments',
                    'icon' => '💉',
                    'description' => 'Botox, dermal fillers, and advanced injectables',
                    'color' => 'redolence-green',
                    'treatments' => ['Botox', 'Dermal Fillers', 'Lip Enhancement']
                ],
                [
                    'name' => 'Laser Treatments',
                    'icon' => '⚡',
                    'description' => 'Advanced laser therapy for skin rejuvenation',
                    'color' => 'redolence-blue',
                    'treatments' => ['Laser Resurfacing', 'Hair Removal', 'Pigmentation']
                ],
                [
                    'name' => 'Skin Rejuvenation',
                    'icon' => '✨',
                    'description' => 'Medical-grade facial treatments and peels',
                    'color' => 'redolence-green',
                    'treatments' => ['Chemical Peels', 'Microneedling', 'HydraFacial']
                ],
               
            ];

            foreach ($serviceCategories as $index => $category):
                $isEven = $index % 2 == 0;
                $colorClass = $category['color'];
            ?>
                <div class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-<?= $colorClass ?>/30 hover:-translate-y-2">
                    <!-- Background Gradient -->
                    <div class="absolute inset-0 bg-gradient-to-br from-<?= $colorClass ?>/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    <!-- Content -->
                    <div class="relative p-8">
                        <!-- Icon -->
                        <div class="w-16 h-16 bg-gradient-to-br from-<?= $colorClass ?>/20 to-<?= $colorClass ?>/10 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                            <span class="text-3xl"><?= $category['icon'] ?></span>
                        </div>

                        <!-- Title -->
                        <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-<?= $colorClass ?> transition-colors duration-300">
                            <?= $category['name'] ?>
                        </h3>

                        <!-- Description -->
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            <?= $category['description'] ?>
                        </p>

                        <!-- Treatment List -->
                        <div class="mb-6">
                            <div class="flex flex-wrap gap-2">
                                <?php foreach ($category['treatments'] as $treatment): ?>
                                    <span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full group-hover:bg-<?= $colorClass ?>/10 group-hover:text-<?= $colorClass ?> transition-colors duration-300">
                                        <?= $treatment ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- CTA Button -->
                        <a href="<?= getBasePath() ?>/services" class="inline-flex items-center justify-center w-full bg-<?= $colorClass ?> hover:bg-<?= $colorClass === 'redolence-green' ? 'green-dark' : 'blue-dark' ?> text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                            Book Treatment
                        </a>
                    </div>

                    <!-- Decorative Element -->
                    <div class="absolute top-4 right-4 w-20 h-20 bg-<?= $colorClass ?>/5 rounded-full blur-xl group-hover:bg-<?= $colorClass ?>/10 transition-colors duration-500"></div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center">
            <div class="bg-white rounded-3xl shadow-xl p-12 border border-gray-100">
                <div class="max-w-3xl mx-auto">
                    <h3 class="text-3xl font-bold text-gray-900 mb-4">
                        Ready to Begin Your Transformation?
                    </h3>
                    <p class="text-xl text-gray-600 mb-8">
                        Schedule a personalized consultation with our medical aesthetics experts to create your custom treatment plan.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center justify-center bg-redolence-green hover:bg-green-dark text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            Book Consultation
                        </a>
                        <a href="<?= getBasePath() ?>/services" class="inline-flex items-center justify-center bg-white hover:bg-gray-50 text-redolence-blue border-2 border-redolence-blue px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                            View All Treatments
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modern Signature Treatments Section -->
<section class="py-24 bg-gradient-to-br from-gray-50 via-white to-redolence-green/5 relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute top-0 left-0 w-full h-full opacity-30">
        <div class="absolute top-20 left-20 w-40 h-40 bg-redolence-green/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-20 right-20 w-60 h-60 bg-redolence-blue/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 3s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 6s;"></div>
    </div>

    <div class="max-w-7xl mx-auto px-6 relative z-10">
        <!-- Enhanced Header -->
        <div class="text-center mb-20">
            <div class="inline-block bg-redolence-green/10 text-redolence-green px-8 py-4 rounded-full text-sm font-bold mb-8 border border-redolence-green/20 shadow-lg backdrop-blur-sm">
                <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                Signature Treatments
            </div>

            <h2 class="text-4xl lg:text-6xl font-bold text-gray-900 mb-8 leading-tight">
                Premium
                <span class="bg-gradient-to-r from-redolence-green via-redolence-blue to-purple-600 bg-clip-text text-transparent">
                    Medical Aesthetics
                </span>
            </h2>

            <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
                Discover our most sought-after treatments, meticulously crafted to enhance your natural beauty with cutting-edge technology and expert care.
            </p>

            <!-- Stats Bar -->
            <div class="flex justify-center items-center space-x-8 text-sm text-gray-500">
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    FDA Approved
                </div>
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    500+ Happy Clients
                </div>
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    Expert Team
                </div>
            </div>
        </div>

        <!-- Modern Treatment Cards Grid - Fixed Layout -->
        <div class="flex flex-wrap justify-center gap-4">
            <?php foreach ($featuredServices as $index => $service): ?>
                <div class="treatment-card group w-full max-w-xs bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 flex flex-col cursor-pointer relative" onclick="openServiceModal('<?= $service['id'] ?>')">

                    <!-- Treatment Badges -->
                    <?php if (isset($service['featured'], $service['popular'], $service['new_treatment']) && ($service['featured'] || $service['popular'] || $service['new_treatment'])): ?>
                        <div class="absolute top-4 left-4 z-10 flex flex-wrap gap-2">
                            <?php if ($service['featured']): ?>
                                <span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg">
                                    ⭐ Featured
                                </span>
                            <?php endif; ?>
                            <?php if ($service['popular']): ?>
                                <span class="bg-gradient-to-r from-red-400 to-red-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg">
                                    🔥 Popular
                                </span>
                            <?php endif; ?>
                            <?php if ($service['new_treatment']): ?>
                                <span class="bg-gradient-to-r from-green-400 to-green-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg">
                                    ✨ New
                                </span>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Enhanced Image Container -->
                    <div class="relative h-64 overflow-hidden">
                        <?php if ($service['image']): ?>
                            <?php
                            // Handle both uploaded files and external URLs
                            $imageSrc = $service['image'];
                            if (!filter_var($imageSrc, FILTER_VALIDATE_URL)) {
                                // If not a URL, treat as uploaded file and prepend uploads path
                                $imageSrc = getBasePath() . '/uploads/' . ltrim($imageSrc, '/');
                            }
                            ?>
                             <!-- High-Quality Image -->
                             <img src="<?= htmlspecialchars($imageSrc) ?>"
                                  alt="<?= htmlspecialchars($service['name']) ?>"
                                  class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                                  loading="lazy"
                                  decoding="async"
                                  width="320"
                                  height="256"
                                  style="backface-visibility: hidden; transform: translateZ(0);"
                            />
                        <?php else: ?>
                            <!-- Fallback Design -->
                            <div class="w-full h-full bg-gradient-to-br from-redolence-green/20 to-redolence-blue/20 flex items-center justify-center">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto backdrop-blur-sm">
                                        <svg class="w-8 h-8 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    </div>
                                    <div class="text-gray-700 font-medium">Medical Treatment</div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Gradient Overlays -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                        <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/10 to-redolence-blue/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                        <!-- Hover Overlay Content -->
                        <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                            <div class="text-center text-white">
                                <div class="bg-white/20 backdrop-blur-md rounded-2xl p-4 border border-white/30">
                                    <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                    </svg>
                                    <div class="text-sm font-semibold">View Details</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Simple, Clean Card Content -->
                    <div class="p-6 flex-grow">
                        <!-- Top Content -->
                        <div>
                            <!-- Treatment Name -->
                            <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-redolence-green transition-colors duration-300">
                                <?= html_entity_decode($service['name'], ENT_QUOTES, 'UTF-8') ?> <!-- Decode HTML entities -->
                            </h3>

                            <!-- Technology Used -->
                            <?php if (!empty($service['technology_used'])): ?>
                                <div class="mb-3">
                                    <div class="inline-flex items-center bg-redolence-blue/10 text-redolence-blue px-3 py-1 rounded-full text-xs font-semibold">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                                        </svg>
                                        <?= htmlspecialchars($service['technology_used']) ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Price -->
                            <div class="mb-4">
                                <?php if (shouldShowPricing()): ?>
                                    <?php if ($service['price']): ?>
                                        <div class="text-2xl font-bold text-redolence-green">
                                            <?= formatCurrency($service['price'], null, true) ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-lg font-semibold text-redolence-blue">
                                            TSH (To be discussed)
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="text-lg font-semibold text-redolence-green">
                                        Contact for pricing
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Description - Display HTML properly -->
                            <div class="description-content">
                                <?php if (!empty($service['description'])): ?>
                                    <div class="text-gray-600 leading-relaxed text-sm mb-3 treatment-description">
                                        <?php
                                        // Extract plain text for preview (limit to 100 characters)
                                        // First decode HTML entities, then strip tags
                                        $decodedDescription = html_entity_decode($service['description'], ENT_QUOTES, 'UTF-8');
                                        $plainText = strip_tags($decodedDescription);
                                        $shortText = strlen($plainText) > 100 ? substr($plainText, 0, 100) . '...' : $plainText;
                                        echo htmlspecialchars($shortText);
                                        ?>
                                    </div>
                                <?php endif; ?>

                                <!-- Treatment Details -->
                                <div class="space-y-2 mb-3">
                                    <?php if ($service['duration']): ?>
                                        <div class="flex items-center text-gray-500 text-sm">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                            </svg>
                                            <?= $service['duration'] ?> minutes
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($service['session_frequency'])): ?>
                                        <div class="flex items-center text-gray-500 text-sm">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 7h12v9a1 1 0 01-1 1H5a1 1 0 01-1-1V7z"/>
                                            </svg>
                                            <?= htmlspecialchars($service['session_frequency']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="flex items-center mb-4">
                                    <?php for ($i = 0; $i < 5; $i++): ?>
                                        <svg class="w-4 h-4 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    <?php endfor; ?>
                                    <?php
                                    $rating = number_format(rand(47, 50) / 10, 1);
                                    $reviewCount = rand(150, 350);
                                    ?>
                                    <span class="ml-2 text-sm text-gray-600"><?= $rating ?> (<?= $reviewCount ?> reviews)</span>
                                </div>
                            </div>
                        </div>

                        <!-- Book Button - Always at bottom -->
                        <div class="mt-auto pt-4">
                            <button onclick="bookViaWhatsApp('<?= htmlspecialchars($service['name'], ENT_QUOTES) ?>')"
                                    class="w-full bg-redolence-green hover:bg-green-600 text-white py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 text-center shadow-lg hover:shadow-xl">
                                Book Now
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Advanced Treatment Showcase Section -->
<section class="py-24 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-20">
            <div class="inline-block bg-redolence-blue/20 text-redolence-blue px-6 py-3 rounded-full text-sm font-semibold mb-6 backdrop-blur-sm border border-redolence-blue/30">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
                Advanced Procedures
            </div>
            <h2 class="text-5xl font-bold text-white mb-6">
                Cutting-Edge
                <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">
                    Medical Aesthetics
                </span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Discover our advanced treatment protocols that combine medical expertise with aesthetic artistry for transformative results.
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Injectable Treatments -->
            <div class="relative group">
                <div class="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-8 hover:border-redolence-green/50 transition-all duration-300 hover:scale-105 shadow-lg">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-redolence-green/20 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-8 h-8 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 2L3 7v11a1 1 0 001 1h3v-8a1 1 0 011-1h4a1 1 0 011 1v8h3a1 1 0 001-1V7l-7-5z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Injectable Treatments</h3>
                            <p class="text-redolence-green font-semibold">BOTOX & FILLERS</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Professional Botox and dermal filler treatments for natural-looking results. Reduce wrinkles and enhance facial features with precision.
                    </p>
                    <div class="flex items-center justify-between">
                        <a href="<?= getBasePath() ?>/services?category=Injectables" class="text-redolence-green hover:text-green-600 font-semibold transition-colors">
                            View Treatments →
                        </a>
                        <div class="flex items-center text-gray-500">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span class="font-bold text-gray-900">500+</span>
                            <span class="ml-1">Procedures</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Laser Treatments -->
            <div class="relative group">
                <div class="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-8 hover:border-redolence-blue/50 transition-all duration-300 hover:scale-105 shadow-lg">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-redolence-blue/20 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-8 h-8 text-redolence-blue" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900">Laser Treatments</h3>
                            <p class="text-redolence-blue font-semibold">ADVANCED TECHNOLOGY</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        State-of-the-art laser treatments for skin resurfacing, hair removal, and pigmentation correction with minimal downtime.
                    </p>
                    <div class="flex items-center justify-between">
                        <a href="<?= getBasePath() ?>/services?category=Laser" class="text-redolence-blue hover:text-blue-600 font-semibold transition-colors">
                            Explore Laser →
                        </a>
                        <div class="flex items-center text-gray-500">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                            <span class="font-bold text-gray-900">95%</span>
                            <span class="ml-1">Success Rate</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us Section - Modern Redesign -->
<section class="py-24 bg-gray-50">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
            <h2 class="text-base font-semibold text-redolence-green tracking-wider uppercase">Our Promise</h2>
            <p class="mt-2 text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">
                Why Choose Redolence?
            </p>
            <p class="mt-5 max-w-2xl mx-auto text-lg text-gray-500">
                We blend artistry with medical science to deliver unparalleled aesthetic results.
            </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Card 1: Expert Team -->
            <div class="bg-white p-8 rounded-2xl shadow-sm border border-gray-200/80 hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                <div class="flex items-center justify-center h-12 w-12 rounded-full bg-redolence-green/10 mb-6">
                    <svg class="h-6 w-6 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/></svg>
                </div>
                <h3 class="text-lg font-bold text-gray-900">Expert Team</h3>
                <p class="mt-2 text-sm text-gray-600">Our board-certified specialists provide top-tier care with precision and artistry.</p>
            </div>

            <!-- Card 2: Advanced Technology -->
            <div class="bg-white p-8 rounded-2xl shadow-sm border border-gray-200/80 hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                <div class="flex items-center justify-center h-12 w-12 rounded-full bg-redolence-green/10 mb-6">
                    <svg class="h-6 w-6 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c.251.023.501.05.75.082m.75.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082m-1.5 0a48.108 48.108 0 00-3 0m3 0a48.108 48.108 0 013 0m-3 0v6.345c0 .65.203 1.27.57 1.751L12 18.25M12 18.25a2.25 2.25 0 01-2.25-2.25V7.5M12 18.25a2.25 2.25 0 002.25-2.25V7.5" /></svg>
                </div>
                <h3 class="text-lg font-bold text-gray-900">Advanced Technology</h3>
                <p class="mt-2 text-sm text-gray-600">We utilize the latest FDA-approved technologies for safe and effective treatments.</p>
            </div>

            <!-- Card 3: Personalized Care -->
            <div class="bg-white p-8 rounded-2xl shadow-sm border border-gray-200/80 hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                <div class="flex items-center justify-center h-12 w-12 rounded-full bg-redolence-green/10 mb-6">
                    <svg class="h-6 w-6 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-3.355a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z"/></svg>
                </div>
                <h3 class="text-lg font-bold text-gray-900">Personalized Care</h3>
                <p class="mt-2 text-sm text-gray-600">Every treatment plan is customized to your unique goals and physiology.</p>
            </div>

            <!-- Card 4: Proven Results -->
            <div class="bg-white p-8 rounded-2xl shadow-sm border border-gray-200/80 hover:shadow-lg hover:-translate-y-1 transition-all duration-300">
                <div class="flex items-center justify-center h-12 w-12 rounded-full bg-redolence-green/10 mb-6">
                    <svg class="h-6 w-6 text-redolence-green" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                </div>
                <h3 class="text-lg font-bold text-gray-900">Proven Results</h3>
                <p class="mt-2 text-sm text-gray-600">Our focus is on delivering natural-looking, beautiful results that you'll love.</p>
            </div>
        </div>
    </div>
</section>

<!-- Modern Patient Success Stories Section -->
<section class="py-24 bg-gradient-to-br from-white via-gray-50 to-redolence-blue/5 relative">
    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-20 h-20 bg-redolence-green/10 rounded-full blur-xl animate-pulse"></div>
    <div class="absolute bottom-20 right-10 w-32 h-32 bg-redolence-blue/10 rounded-full blur-2xl animate-pulse" style="animation-delay: 2s;"></div>

    <div class="max-w-7xl mx-auto px-6 relative">
        <!-- Header -->
        <div class="text-center mb-16">
            <div class="inline-flex items-center bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 text-redolence-green px-6 py-3 rounded-full text-sm font-bold mb-8 border border-redolence-green/20">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                Success Stories
            </div>

            <h2 class="text-4xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Transforming Lives,
                <br>
                <span class="bg-gradient-to-r from-redolence-green via-redolence-blue to-purple-600 bg-clip-text text-transparent">
                    One Patient at a Time
                </span>
            </h2>

            <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Real stories from real patients who trusted us with their aesthetic journey and achieved remarkable results.
            </p>
        </div>

        <!-- Success Stories Slideshow -->
        <div class="relative max-w-6xl mx-auto">
            <div class="testimonials-slider overflow-hidden">
                <div class="testimonials-track flex transition-transform duration-500 ease-in-out">
                    <?php
                    $testimonials = [
                        [
                            'name' => 'Sarah Mitchell',
                            'role' => 'Business Executive',
                            'content' => 'The Botox treatment was exceptional! Dr. Ahmed and the team made me feel comfortable throughout. The results look natural and I feel more confident than ever.',
                            'rating' => 5,
                            'service' => 'Botox Treatment',
                            'image' => '/testimonials/sarah.jpg',
                            'treatment' => 'Anti-Aging'
                        ],
                        [
                            'name' => 'Dr. Amina Rashid',
                            'role' => 'Physician',
                            'content' => 'The laser skin resurfacing treatment exceeded my expectations. The medical team is highly professional and the results are remarkable. My skin looks years younger!',
                            'rating' => 5,
                            'service' => 'Laser Resurfacing',
                            'image' => '/testimonials/amina.jpg',
                            'treatment' => 'Skin Rejuvenation'
                        ],
                        [
                            'name' => 'Maria Santos',
                            'role' => 'Marketing Director',
                            'content' => 'The dermal filler treatment was life-changing! The consultation was thorough, the procedure was comfortable, and the results look completely natural.',
                            'rating' => 5,
                            'service' => 'Dermal Fillers',
                            'image' => '/testimonials/maria.jpg',
                            'treatment' => 'Facial Enhancement'
                        ],
                        [
                            'name' => 'Layla Ahmed',
                            'role' => 'Fashion Designer',
                            'content' => 'The HydraFacial gave me the glowing, radiant skin I\'ve always wanted. The medical-grade treatment exceeded my expectations. Redolence is simply the best!',
                            'rating' => 5,
                            'service' => 'HydraFacial',
                            'image' => '/testimonials/layla.jpg',
                            'treatment' => 'Facial Treatment'
                        ],
                        [
                            'name' => 'Nadia Omar',
                            'role' => 'Wellness Consultant',
                            'content' => 'The CoolSculpting treatment helped me achieve the body contour I wanted without surgery. Professional service, amazing results. Thank you Redolence team!',
                            'rating' => 5,
                            'service' => 'CoolSculpting',
                            'image' => '/testimonials/nadia.jpg',
                            'treatment' => 'Body Contouring'
                        ]
                    ];

                    foreach ($testimonials as $index => $testimonial): ?>
                        <div class="testimonial-slide flex-shrink-0 w-full md:w-1/2 lg:w-1/3 px-2 md:px-4">
                            <div class="bg-white border border-gray-200 rounded-xl md:rounded-2xl p-4 md:p-6 lg:p-8 h-full hover:border-redolence-green/50 transition-all duration-300 hover:shadow-2xl relative overflow-hidden group">
                                <!-- Background Gradient -->
                                <div class="absolute inset-0 bg-gradient-to-br from-redolence-green/5 to-redolence-blue/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                
                                <!-- Content -->
                                <div class="relative z-10">
                                    <!-- Rating & Category -->
                                    <div class="mb-4 md:mb-6">
                                        <div class="flex items-center justify-between mb-2 md:mb-3">
                                            <div class="flex space-x-1">
                                                <?php for ($i = 0; $i < 5; $i++): ?>
                                                    <i class="fas fa-star text-redolence-green text-sm md:text-lg"></i>
                                                <?php endfor; ?>
                                            </div>
                                            <span class="bg-redolence-green/10 text-redolence-green px-2 md:px-3 py-1 rounded-full text-xs font-semibold">
                                                <?= $testimonial['treatment'] ?>
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Quote -->
                                    <blockquote class="text-gray-700 mb-4 md:mb-8 leading-relaxed text-sm md:text-base lg:text-lg font-medium relative">
                                        <div class="absolute -top-1 md:-top-2 -left-1 md:-left-2 text-2xl md:text-4xl text-redolence-green/20 font-serif">"</div>
                                        <?= $testimonial['content'] ?>
                                        <div class="absolute -bottom-2 md:-bottom-4 -right-1 md:-right-2 text-2xl md:text-4xl text-redolence-green/20 font-serif">"</div>
                                    </blockquote>

                                    <!-- Client Info -->
                                    <div class="flex items-center space-x-3 md:space-x-4">
                                        <div class="w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-redolence-green to-redolence-blue rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                                            <span class="text-white font-bold text-sm md:text-base lg:text-lg">
                                                <?= strtoupper(substr($testimonial['name'], 0, 1)) . strtoupper(substr(explode(' ', $testimonial['name'])[1] ?? '', 0, 1)) ?>
                                            </span>
                                        </div>
                                        <div class="min-w-0 flex-1">
                                            <div class="text-gray-900 font-bold text-sm md:text-base lg:text-lg truncate"><?= $testimonial['name'] ?></div>
                                            <div class="text-gray-600 text-xs md:text-sm truncate"><?= $testimonial['role'] ?></div>
                                            <div class="text-redolence-green text-xs md:text-sm font-semibold truncate"><?= $testimonial['service'] ?></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Decorative Elements - Hidden on mobile for cleaner look -->
                                <div class="hidden md:block absolute top-4 right-4 w-16 md:w-24 h-16 md:h-24 bg-redolence-green/10 rounded-full blur-2xl group-hover:bg-redolence-green/20 transition-colors duration-300"></div>
                                <div class="hidden md:block absolute bottom-4 left-4 w-12 md:w-16 h-12 md:h-16 bg-redolence-blue/10 rounded-full blur-xl group-hover:bg-redolence-blue/20 transition-colors duration-300"></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Navigation Arrows - Smaller on mobile -->
            <button class="testimonials-prev absolute left-2 md:left-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-gray-50 border border-gray-200 rounded-full w-10 h-10 md:w-12 md:h-12 flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 z-10 hover:border-redolence-green/50">
                <i class="fas fa-chevron-left text-redolence-green text-sm md:text-base"></i>
            </button>
            <button class="testimonials-next absolute right-2 md:right-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-gray-50 border border-gray-200 rounded-full w-10 h-10 md:w-12 md:h-12 flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 z-10 hover:border-redolence-green/50">
                <i class="fas fa-chevron-right text-redolence-green text-sm md:text-base"></i>
            </button>

            <!-- Dots Indicator - More prominent on mobile -->
            <div class="flex justify-center mt-6 md:mt-8 space-x-2 md:space-x-3">
                <?php foreach ($testimonials as $index => $testimonial): ?>
                    <button class="testimonial-dot w-2.5 h-2.5 md:w-3 md:h-3 rounded-full bg-gray-300 hover:bg-redolence-green transition-colors duration-300 <?= $index === 0 ? 'bg-redolence-green' : '' ?>" data-slide="<?= $index ?>"></button>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Modern Stats Section -->
        <div class="mt-20">
          <!-- Redesigned Trusted Excellence -->
          <section id="trusted-excellence" class="relative overflow-hidden rounded-3xl border border-gray-200 bg-white/80 backdrop-blur px-6 py-10 md:px-12 md:py-14 shadow-2xl">
            <div class="absolute inset-0 pointer-events-none opacity-60" aria-hidden="true" style="background: radial-gradient(400px 200px at 100% 0%, rgba(73,167,92,0.06), transparent 70%), radial-gradient(300px 300px at -10% 120%, rgba(59,130,246,0.06), transparent 70%);"></div>
            <div class="relative z-10 max-w-5xl mx-auto">
              <div class="text-center mb-8 md:mb-10">
                <div class="inline-flex items-center px-5 py-2 rounded-full text-xs md:text-sm font-bold bg-gradient-to-r from-redolence-green/10 to-redolence-blue/10 text-redolence-green border border-redolence-green/20">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>
                  Trusted Excellence
                </div>
                <h3 class="mt-5 text-2xl md:text-3xl lg:text-4xl font-extrabold text-gray-900">
                  Why patients trust Redolence
                </h3>
                <p class="mt-3 md:mt-4 text-gray-600 md:text-lg max-w-2xl mx-auto">
                  Clinical-grade care, proven outcomes, and a compassionate team focused on your confidence and wellbeing.
                </p>
              </div>
              <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 md:gap-6">
                <div class="group rounded-2xl border border-gray-200 bg-white p-4 md:p-5 text-center shadow-sm hover:shadow-md transition-all">
                  <div class="mx-auto mb-2 w-10 h-10 rounded-xl bg-redolence-green/10 text-redolence-green grid place-items-center group-hover:bg-redolence-green/15">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M10 2a8 8 0 100 16 8 8 0 000-16zM9 6h2v5H9V6zm0 6h2v2H9v-2z"/></svg>
                  </div>
                  <div class="text-2xl md:text-3xl font-extrabold text-gray-900">10+</div>
                  <div class="text-xs md:text-sm text-gray-600">Years Experience</div>
                </div>
                <div class="group rounded-2xl border border-gray-200 bg-white p-4 md:p-5 text-center shadow-sm hover:shadow-md transition-all">
                  <div class="mx-auto mb-2 w-10 h-10 rounded-xl bg-redolence-blue/10 text-redolence-blue grid place-items-center group-hover:bg-redolence-blue/15">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M4 3h12a1 1 0 011 1v4a5 5 0 11-10 0V5H4a1 1 0 010-2z"/></svg>
                  </div>
                  <div class="text-2xl md:text-3xl font-extrabold text-gray-900">15k+</div>
                  <div class="text-xs md:text-sm text-gray-600">Procedures Done</div>
                </div>
                <div class="group rounded-2xl border border-gray-200 bg-white p-4 md:p-5 text-center shadow-sm hover:shadow-md transition-all">
                  <div class="mx-auto mb-2 w-10 h-10 rounded-xl bg-purple-500/10 text-purple-600 grid place-items-center group-hover:bg-purple-500/15">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292A1 1 0 0012 7h3.5a1 1 0 01.6 1.8l-2.8 2.2a1 1 0 00-.35 1.1l1.1 3.3a1 1 0 01-1.54 1.1l-2.78-2.02a1 1 0 00-1.16 0l-2.78 2.02a1 1 0 01-1.54-1.1l1.1-3.3a1 1 0 00-.35-1.1L3.9 8.8A1 1 0 014.5 7H8a1 1 0 00.929-.781l1.07-3.292z"/></svg>
                  </div>
                  <div class="text-2xl md:text-3xl font-extrabold text-gray-900">4.9/5</div>
                  <div class="text-xs md:text-sm text-gray-600">Average Rating</div>
                </div>
                <div class="group rounded-2xl border border-gray-200 bg-white p-4 md:p-5 text-center shadow-sm hover:shadow-md transition-all">
                  <div class="mx-auto mb-2 w-10 h-10 rounded-xl bg-amber-500/10 text-amber-600 grid place-items-center group-hover:bg-amber-500/15">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M10 2l2.39 4.84L18 7.27l-4 3.9.95 5.53L10 14.77l-4.95 1.93L6 11.17 2 7.27l5.61-.43L10 2z"/></svg>
                  </div>
                  <div class="text-2xl md:text-3xl font-extrabold text-gray-900">Certified</div>
                  <div class="text-xs md:text-sm text-gray-600">Expert Practitioners</div>
                </div>
                <div class="group rounded-2xl border border-gray-200 bg-white p-4 md:p-5 text-center shadow-sm hover:shadow-md transition-all">
                  <div class="mx-auto mb-2 w-10 h-10 rounded-xl bg-emerald-500/10 text-emerald-600 grid place-items-center group-hover:bg-emerald-500/15">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0L3.293 11.7a1 1 0 011.414-1.414L8 13.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/></svg>
                  </div>
                  <div class="text-2xl md:text-3xl font-extrabold text-gray-900">FDA</div>
                  <div class="text-xs md:text-sm text-gray-600">Approved Tech</div>
                </div>
              </div>
              <div class="mt-8 md:mt-10 flex flex-col sm:flex-row items-center justify-center gap-3">
                <a href="<?= getBasePath() ?>/about#credentials" class="inline-flex items-center justify-center rounded-xl px-5 py-3 text-sm md:text-base font-semibold text-white bg-redolence-green hover:scale-[1.02] active:scale-[0.99] transition-transform shadow">
                  View Certifications
                </a>
                <a href="<?= getBasePath() ?>/testimonials" class="inline-flex items-center justify-center rounded-xl px-5 py-3 text-sm md:text-base font-semibold text-redolence-blue bg-white border-2 border-redolence-blue hover:bg-blue-50 transition">
                  Read Reviews
                </a>
              </div>
            </div>
          </section>
            <div class="relative bg-gradient-to-br from-white via-gray-50 to-redolence-green/5 rounded-3xl p-8 md:p-12 max-w-5xl mx-auto overflow-hidden border border-gray-100 shadow-2xl hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="stats-grid" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
                                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#49a75c" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#stats-grid)" />
                    </svg>
                </div>

                <!-- Floating Elements -->
                <div class="absolute top-6 right-6 w-32 h-32 bg-redolence-green/10 rounded-full blur-3xl"></div>
                <div class="absolute bottom-6 left-6 w-24 h-24 bg-redolence-blue/10 rounded-full blur-2xl"></div>

                <div class="relative z-10">
                    <div class="text-center mb-12">
                        <div class="inline-flex items-center bg-redolence-green/10 text-redolence-green px-6 py-3 rounded-full text-sm font-bold mb-6">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                            Trusted Excellence
                        </div>
                        <h3 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                            Proven Results That Speak for Themselves
                        </h3>
                        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                            Join the growing community of satisfied clients who trust Redolence for their medical aesthetic journey.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <!-- Average Rating -->
                        <div class="text-center group">
                            <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-gray-100">
                                <div class="w-20 h-20 bg-gradient-to-br from-redolence-green to-green-dark rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-star text-white text-2xl"></i>
                                </div>
                                <div class="text-4xl md:text-5xl font-bold text-gray-900 mb-3">4.9</div>
                                <div class="flex justify-center space-x-1 mb-3">
                                    <?php for ($i = 0; $i < 5; $i++): ?>
                                        <i class="fas fa-star text-redolence-green text-lg"></i>
                                    <?php endfor; ?>
                                </div>
                                <div class="text-gray-600 font-semibold">Average Rating</div>
                                <div class="text-gray-500 text-sm mt-1">Based on 500+ Reviews</div>
                            </div>
                        </div>

                        <!-- Happy Clients -->
                        <div class="text-center group">
                            <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-gray-100">
                                <div class="w-20 h-20 bg-gradient-to-br from-redolence-blue to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-users text-white text-2xl"></i>
                                </div>
                                <div class="text-4xl md:text-5xl font-bold text-gray-900 mb-3">500+</div>
                                <div class="text-gray-600 font-semibold">Happy Clients</div>
                                <div class="text-gray-500 text-sm mt-1">Transformed Lives</div>
                            </div>
                        </div>

                        <!-- Satisfaction Rate -->
                        <div class="text-center group">
                            <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-gray-100">
                                <div class="w-20 h-20 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-heart text-white text-2xl"></i>
                                </div>
                                <div class="text-4xl md:text-5xl font-bold text-gray-900 mb-3">98%</div>
                                <div class="text-gray-600 font-semibold">Satisfaction Rate</div>
                                <div class="text-gray-500 text-sm mt-1">Client Retention</div>
                            </div>
                        </div>
                    </div>

                    <!-- Bottom CTA -->
                    <div class="text-center mt-12 pt-8 border-t border-gray-200">
                        <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8">
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-shield-alt text-redolence-green mr-2"></i>
                                <span class="font-semibold">Medical Grade Treatments</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-award text-redolence-green mr-2"></i>
                                <span class="font-semibold">Licensed Professionals</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-clock text-redolence-green mr-2"></i>
                                <span class="font-semibold">5+ Years Experience</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<!-- Modern Newsletter Section -->
<section class="py-24 bg-gradient-to-br from-gray-50 via-white to-redolence-green/5 relative overflow-hidden" hidden>
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(73,167,92,0.3) 1px, transparent 0); background-size: 40px 40px;"></div>
    </div>

    <div class="max-w-6xl mx-auto px-6 relative">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
            <!-- Left Content -->
            <div class="space-y-8">
                <div class="space-y-6">
                    <div class="inline-flex items-center bg-redolence-green/10 text-redolence-green px-4 py-2 rounded-full text-sm font-bold">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                        </svg>
                        Newsletter
                    </div>

                    <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                        Stay <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">Informed</span> & Beautiful
                    </h2>

                    <p class="text-xl text-gray-600 leading-relaxed">
                        Get exclusive access to medical aesthetic insights, treatment innovations, and special offers delivered to your inbox.
                    </p>
                </div>

                <!-- Benefits Grid -->
                <div class="grid grid-cols-2 gap-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-redolence-green/10 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="text-gray-700 font-medium">Expert Tips</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-redolence-green/10 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="text-gray-700 font-medium">Special Offers</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-redolence-green/10 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="text-gray-700 font-medium">New Treatments</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-redolence-green/10 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-redolence-green" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="text-gray-700 font-medium">No Spam</span>
                    </div>
                </div>
            </div>

            <!-- Right Content - Form -->
            <div class="bg-white rounded-3xl shadow-2xl p-8 border border-gray-100">
                <form class="space-y-6" action="<?= getBasePath() ?>/api/newsletter.php" method="POST">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-2">Email Address</label>
                            <input
                                type="email"
                                name="email"
                                placeholder="<EMAIL>"
                                class="w-full px-4 py-4 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-redolence-green/20 focus:border-redolence-green transition-all"
                                required
                            >
                        </div>

                        <button
                            type="submit"
                            class="w-full px-8 py-4 bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-redolence-green text-white font-semibold rounded-xl transition-all duration-300 hover:scale-105"
                        >
                            Subscribe Now
                        </button>
                    </div>

                    <p class="text-gray-500 text-sm text-center">
                        Join 5,000+ subscribers • Unsubscribe anytime
                    </p>
                </form>

                <!-- Trust Indicators -->
                <div class="grid grid-cols-3 gap-4 mt-8 pt-6 border-t border-gray-100">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-redolence-green">5K+</div>
                        <div class="text-gray-500 text-xs">Subscribers</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-redolence-green">Weekly</div>
                        <div class="text-gray-500 text-xs">Updates</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-redolence-green">100%</div>
                        <div class="text-gray-500 text-xs">Private</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modern Blog Section -->
<section class="py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100">
    <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-20">
            <div class="inline-block bg-redolence-green/10 text-redolence-green px-6 py-3 rounded-full text-sm font-bold mb-6">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 5a1 1 0 011-1h1a1 1 0 011 1v4a1 1 0 01-1 1H4a1 1 0 01-1-1V5zM6 5a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zM6 9a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zM6 13a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1z"/>
                </svg>
                Medical Aesthetics Blog
            </div>
            <h2 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                Latest <span class="bg-gradient-to-r from-redolence-green to-redolence-blue bg-clip-text text-transparent">Expert Insights</span>
            </h2>
            <p class="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
                Discover the latest medical aesthetic trends, treatment insights, and expert advice from our certified practitioners.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($blogPosts as $index => $post): 
                // Calculate estimated read time based on content length
                $wordCount = str_word_count(strip_tags($post['full_content']));
                $readTime = max(1, ceil($wordCount / 200)) . ' min read'; // Average reading speed 200 words/min
                
                // Use summary if available, otherwise truncate full content
                $excerpt = !empty($post['summary']) ? $post['summary'] : substr(strip_tags($post['full_content']), 0, 120) . '...';
                
                // Format the publish date
                $publishDate = $post['publish_date'] ? date('Y-m-d', strtotime($post['publish_date'])) : date('Y-m-d', strtotime($post['created_at']));
                
                // Determine if this is a featured post (first one)
                $isFeatured = $index === 0;
            ?>
                <article class="group relative">
                    <div class="bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-redolence-green/30 hover:-translate-y-2 h-full flex flex-col">
                        <!-- Image Container -->
                        <div class="relative h-56 overflow-hidden bg-gradient-to-br from-redolence-green/10 to-redolence-blue/10">
                            <?php if (!empty($post['image_url'])): ?>
                                <div class="image-skeleton w-full h-full absolute inset-0"></div>
                                <img src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>" 
                                     alt="<?= htmlspecialchars($post['title']) ?>" 
                                     class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 lazy-image" 
                                     loading="lazy" 
                                     decoding="async" 
                                     onload="this.classList.add('loaded', 'fade-in-loaded'); this.previousElementSibling.style.display='none';" 
                                     onerror="this.previousElementSibling.style.display='block';">
                            <?php else: ?>
                                <div class="w-full h-full flex items-center justify-center">
                                    <div class="text-center">
                                        <div class="w-20 h-20 bg-gradient-to-br from-redolence-green to-redolence-blue rounded-full flex items-center justify-center mx-auto mb-4">
                                            <i class="fas fa-microscope text-white text-2xl"></i>
                                        </div>
                                        <div class="text-gray-700 font-medium">Medical Article</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Overlay Gradient -->
                            <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            
                            <!-- Featured Badge -->
                            <?php if ($isFeatured): ?>
                                <div class="absolute top-4 left-4">
                                    <div class="bg-gradient-to-r from-redolence-green to-green-dark text-white px-3 py-1 rounded-full text-xs font-bold flex items-center">
                                        <i class="fas fa-star mr-1"></i>
                                        Featured
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Category Badge -->
                            <div class="absolute top-4 right-4">
                                <div class="bg-white/90 backdrop-blur-sm text-redolence-green px-3 py-1 rounded-full text-xs font-semibold">
                                    Medical Insights
                                </div>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="p-8 flex-1 flex flex-col">
                            <!-- Meta Info -->
                            <div class="flex items-center justify-between text-gray-500 text-sm mb-4">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-redolence-green/10 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-calendar text-redolence-green text-xs"></i>
                                    </div>
                                    <?= date('M j, Y', strtotime($publishDate)) ?>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock text-redolence-green mr-2"></i>
                                    <?= $readTime ?>
                                </div>
                            </div>

                            <!-- Title -->
                            <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-redolence-green transition-colors duration-300 leading-tight line-clamp-2">
                                <?= htmlspecialchars($post['title']) ?>
                            </h3>

                            <!-- Excerpt -->
                            <p class="text-gray-600 mb-6 leading-relaxed line-clamp-3 flex-1">
                                <?= htmlspecialchars($excerpt) ?>
                            </p>

                            <!-- CTA -->
                            <div class="flex items-center justify-between">
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>" 
                                   class="inline-flex items-center bg-redolence-green hover:bg-green-dark text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                                    Read Article
                                    <i class="fas fa-arrow-right ml-2 text-sm group-hover:translate-x-1 transition-transform duration-300"></i>
                                </a>
                                
                                <!-- Social Share -->
                                <div class="flex items-center space-x-2">
                                    <button class="w-10 h-10 bg-gray-100 hover:bg-redolence-green/10 text-gray-600 hover:text-redolence-green rounded-full flex items-center justify-center transition-colors duration-300">
                                        <i class="fas fa-share-alt text-sm"></i>
                                    </button>
                                    <button class="w-10 h-10 bg-gray-100 hover:bg-redolence-green/10 text-gray-600 hover:text-redolence-green rounded-full flex items-center justify-center transition-colors duration-300">
                                        <i class="fas fa-bookmark text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Decorative Elements -->
                        <div class="absolute bottom-4 left-4 w-16 h-16 bg-redolence-green/5 rounded-full blur-xl group-hover:bg-redolence-green/10 transition-colors duration-500"></div>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-12">
            <a href="blog.php" class="inline-flex items-center bg-redolence-green hover:bg-green-dark text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                View All Articles
                <i class="fas fa-arrow-right ml-2"></i>
            </a>
        </div>
    </div>
</section>




<script>
// Enhanced Lazy Loading with Intersection Observer
function initializeLazyLoading() {
    // Check if Intersection Observer is supported
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;

                    // Add loading state
                    img.style.filter = 'blur(5px)';

                    // Create a new image to preload
                    const imageLoader = new Image();
                    imageLoader.onload = function() {
                        // Image loaded successfully
                        img.style.filter = 'none';
                        img.classList.add('loaded', 'fade-in-loaded');

                        // Hide skeleton
                        const skeleton = img.previousElementSibling;
                        if (skeleton && skeleton.classList.contains('image-skeleton')) {
                            skeleton.style.opacity = '0';
                            setTimeout(() => skeleton.style.display = 'none', 300);
                        }
                    };

                    imageLoader.onerror = function() {
                        // Image failed to load
                        const skeleton = img.previousElementSibling;
                        if (skeleton && skeleton.classList.contains('image-skeleton')) {
                            skeleton.style.display = 'block';
                        }
                        img.style.display = 'none';
                    };

                    // Start loading the image
                    imageLoader.src = img.src;

                    // Stop observing this image
                    observer.unobserve(img);
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });

        // Observe all lazy images
        document.querySelectorAll('.lazy-image').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Preload critical images (above the fold)
function preloadCriticalImages() {
    const criticalImages = document.querySelectorAll('.lazy-image[loading="eager"]');
    criticalImages.forEach(img => {
        if (img.complete) {
            img.classList.add('loaded');
        } else {
            img.addEventListener('load', () => {
                img.classList.add('loaded', 'fade-in-loaded');
            });
        }
    });
}

// Hero carousel functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize lazy loading
    initializeLazyLoading();
    preloadCriticalImages();
    const sliderContainer = document.querySelector('.testimonials-slider');
    if (!sliderContainer) return;

    const track = sliderContainer.querySelector('.testimonials-track');
    const slides = Array.from(track.children);
    const nextButton = document.querySelector('.testimonials-next');
    const prevButton = document.querySelector('.testimonials-prev');
    const slideCount = slides.length;
    if (slideCount === 0) return;

    let currentIndex = 0;

    function getSlidesToShow() {
        if (window.innerWidth >= 1024) return 3;
        if (window.innerWidth >= 768) return 2;
        return 1;
    }

    function updateSlider() {
        const slidesToShow = getSlidesToShow();
        const slideWidth = sliderContainer.clientWidth / slidesToShow;
        track.style.transform = `translateX(-${currentIndex * slideWidth}px)`;

        // Update button visibility
        prevButton.style.display = currentIndex === 0 ? 'none' : 'flex';
        nextButton.style.display = currentIndex >= slideCount - slidesToShow ? 'none' : 'flex';
    }

    function moveNext() {
        const slidesToShow = getSlidesToShow();
        if (currentIndex < slideCount - slidesToShow) {
            currentIndex++;
            updateSlider();
        }
    }

    function movePrev() {
        if (currentIndex > 0) {
            currentIndex--;
            updateSlider();
        }
    }

    nextButton.addEventListener('click', moveNext);
    prevButton.addEventListener('click', movePrev);

    // Touch functionality
    let touchStartX = 0;
    let touchEndX = 0;

    track.addEventListener('touchstart', (e) => {
        touchStartX = e.touches[0].clientX;
    }, { passive: true });

    track.addEventListener('touchmove', (e) => {
        touchEndX = e.touches[0].clientX;
    }, { passive: true });

    track.addEventListener('touchend', () => {
        const deltaX = touchEndX - touchStartX;
        if (Math.abs(deltaX) > 50) { // Swipe threshold
            if (deltaX < 0) {
                moveNext();
            } else {
                movePrev();
            }
        }
        // Reset values
        touchStartX = 0;
        touchEndX = 0;
    }, { passive: true });

    window.addEventListener('resize', () => {
        // Recalculate index to avoid empty space on resize
        const slidesToShow = getSlidesToShow();
        if (currentIndex > slideCount - slidesToShow) {
            currentIndex = Math.max(0, slideCount - slidesToShow);
        }
        updateSlider();
    });

    // Initial setup
    updateSlider();
});

// CSRF Token
const csrfToken = '<?= $_SESSION['csrf_token'] ?? '' ?>';

// Initialize wishlist states
function initializeWishlistStates() {
    const wishlistBtns = document.querySelectorAll('.wishlist-btn');

    wishlistBtns.forEach(btn => {
        const itemType = btn.dataset.itemType;
        const itemId = btn.dataset.itemId;

        // Check if item is in wishlist
        fetch(`<?= getBasePath() ?>/api/wishlist.php?action=check&item_type=${itemType}&item_id=${itemId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.inWishlist) {
                    updateHeartIcon(btn, true);
                }
            })
            .catch(error => console.error('Error checking wishlist:', error));
    });
}

// Toggle wishlist item
function toggleWishlist(itemType, itemId, button) {
    // Prevent event bubbling
    event.stopPropagation();

    // Add loading state
    const icon = button.querySelector('i');
    const originalClass = icon.className;
    icon.className = 'fas fa-spinner fa-spin text-white';

    fetch('<?= getBasePath() ?>/api/wishlist.php?action=toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `item_type=${itemType}&item_id=${itemId}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateHeartIcon(button, data.inWishlist);
            showToast(data.message, 'success');
            updateWishlistBadge(data.count);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        // Restore original icon if there was an error
        if (icon.className.includes('fa-spinner')) {
            icon.className = originalClass;
        }
    });
}

// Update heart icon appearance
function updateHeartIcon(button, inWishlist) {
    const icon = button.querySelector('i');

    if (inWishlist) {
        icon.className = 'fas fa-heart text-lg';
        button.classList.remove('border-gray-600', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
        button.classList.add('border-red-400', 'text-red-400', 'bg-red-50', 'animate-pulse');
        setTimeout(() => button.classList.remove('animate-pulse'), 600);
    } else {
        icon.className = 'far fa-heart text-lg';
        button.classList.remove('border-red-400', 'text-red-400', 'bg-red-50');
        button.classList.add('border-gray-600', 'text-gray-300', 'hover:border-red-400', 'hover:text-red-400');
    }
}

// Update wishlist badge count
function updateWishlistBadge(count) {
    const badge = document.getElementById('wishlist-badge');
    if (badge) {
        if (count > 0) {
            badge.textContent = count;
            badge.style.display = 'inline-block';
        } else {
            badge.style.display = 'none';
        }
    }
}

// Show toast notification with smooth animations
function showToast(message, type = 'success') {
    // Remove any existing toast first
    const existingToast = document.getElementById('wishlist-toast');
    if (existingToast) {
        existingToast.style.transform = 'translateX(100%)';
        existingToast.style.opacity = '0';
        setTimeout(() => existingToast.remove(), 200);
    }

    // Create new toast
    const toast = document.createElement('div');
    toast.id = 'wishlist-toast';

    // Set base styles for smooth animation
    toast.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        background-color: #1e293b;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateX(100%) scale(0.95);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        max-width: 20rem;
        backdrop-filter: blur(8px);
    `;

    // Set content and style based on type
    if (type === 'success') {
        toast.style.borderLeft = '4px solid #10b981';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-check-circle" style="color: #10b981; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    } else {
        toast.style.borderLeft = '4px solid #ef4444';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-exclamation-circle" style="color: #ef4444; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    }

    // Add to DOM
    document.body.appendChild(toast);

    // Trigger smooth slide-in animation
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0) scale(1)';
            toast.style.opacity = '1';
        });
    });

    // Hide toast after 2 seconds with smooth slide-out
    setTimeout(() => {
        toast.style.transform = 'translateX(100%) scale(0.95)';
        toast.style.opacity = '0';

        // Remove from DOM after animation completes
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.remove();
            }
        }, 400);
    }, 2000);
}

// Back to Top Button Functionality
function initializeBackToTop() {
    // Create back to top button
    const backToTopButton = document.createElement('button');
    backToTopButton.className = 'back-to-top';
    backToTopButton.setAttribute('aria-label', 'Back to top');
    backToTopButton.innerHTML = `
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    `;

    // Add to DOM
    document.body.appendChild(backToTopButton);

    // Scroll to top functionality
    backToTopButton.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Show/hide button based on scroll position
    let isVisible = false;
    const toggleVisibility = () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const shouldShow = scrollTop > 300;

        if (shouldShow && !isVisible) {
            backToTopButton.classList.add('show');
            isVisible = true;
        } else if (!shouldShow && isVisible) {
            backToTopButton.classList.remove('show');
            isVisible = false;
        }
    };

    // Throttled scroll event listener for better performance
    let ticking = false;
    const handleScroll = () => {
        if (!ticking) {
            requestAnimationFrame(() => {
                toggleVisibility();
                ticking = false;
            });
            ticking = true;
        }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    // Initial check
    toggleVisibility();
}

// Header Auto-Hide/Show Functionality - REMOVED to keep header always visible

// Ensure header stays fixed and visible
function ensureHeaderFixed() {
    const header = document.querySelector('header');
    if (header) {
        // Remove any transform styles that might hide the header
        header.style.transform = 'none';
        header.style.transition = 'none';
        // Ensure header classes are correct
        header.classList.remove('header-hidden', 'header-visible');
    }
}

// Initialize features when DOM is loaded
// Hero Cards Animation - Skillex Style
function initializeHeroCards() {
    const cards = document.querySelectorAll('.hero-card');
    let currentIndex = 0;

    // Enhanced card rotation with stacking effect
    function rotateCards() {
        cards.forEach((card, index) => {
            const isActive = index === currentIndex;
            const nextIndex = (currentIndex + 1) % cards.length;
            const prevIndex = (currentIndex - 1 + cards.length) % cards.length;

            // Reset all cards
            card.style.zIndex = '1';
            card.style.transform = 'translateY(0px) rotateY(0deg) scale(1)';

            if (isActive) {
                // Active card - front and center
                card.style.zIndex = '4';
                card.style.transform = 'translateY(-10px) rotateY(0deg) scale(1.05)';
            } else if (index === nextIndex) {
                // Next card - slightly behind and to the side
                card.style.zIndex = '3';
                card.style.transform = 'translateY(5px) rotateY(-5deg) scale(0.98)';
            } else if (index === prevIndex) {
                // Previous card - further back
                card.style.zIndex = '2';
                card.style.transform = 'translateY(10px) rotateY(5deg) scale(0.95)';
            }
        });

        currentIndex = (currentIndex + 1) % cards.length;
    }

    // Start the rotation
    if (cards.length > 0) {
        setInterval(rotateCards, 3000); // Rotate every 3 seconds

        // Add hover effects
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-15px) scale(1.08) rotateY(0deg)';
                card.style.zIndex = '10';
                card.style.boxShadow = '0 30px 60px rgba(0,0,0,0.2)';
            });

            card.addEventListener('mouseleave', () => {
                // Reset to current state
                setTimeout(() => {
                    if (!card.matches(':hover')) {
                        card.style.boxShadow = '0 20px 40px rgba(0,0,0,0.1)';
                    }
                }, 100);
            });
        });
    }
}

// Parallax effect for hero section
function initializeHeroParallax() {
    const heroSection = document.querySelector('.hero-cards-container');
    if (!heroSection) return;

    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;

        if (scrolled < window.innerHeight) {
            heroSection.style.transform = `translateY(${rate}px)`;
        }
    });
}

// Testimonials Slideshow Functionality
function initializeTestimonialsSlider() {
    const track = document.querySelector('.testimonials-track');
    const slides = document.querySelectorAll('.testimonial-slide');
    const prevBtn = document.querySelector('.testimonials-prev');
    const nextBtn = document.querySelector('.testimonials-next');
    const dots = document.querySelectorAll('.testimonial-dot');
    
    if (!track || !slides.length) return;
    
    let currentIndex = 0;
    let slidesToShow = getSlidesToShow();
    let maxIndex = Math.max(0, slides.length - slidesToShow);
    
    function getSlidesToShow() {
        return window.innerWidth >= 1024 ? 3 : window.innerWidth >= 768 ? 2 : 1;
    }
    
    function updateSlider() {
        const translateX = -(currentIndex * (100 / slidesToShow));
        track.style.transform = `translateX(${translateX}%)`;
        
        // Update dots
        dots.forEach((dot, index) => {
            dot.classList.toggle('bg-redolence-green', index === currentIndex);
            dot.classList.toggle('bg-gray-300', index !== currentIndex);
        });
        
        // Update button states
        if (prevBtn) prevBtn.style.opacity = currentIndex === 0 ? '0.5' : '1';
        if (nextBtn) nextBtn.style.opacity = currentIndex === maxIndex ? '0.5' : '1';
    }
    
    function nextSlide() {
        if (currentIndex < maxIndex) {
            currentIndex++;
        } else {
            currentIndex = 0; // Loop back to start
        }
        updateSlider();
    }
    
    function prevSlide() {
        if (currentIndex > 0) {
            currentIndex--;
        } else {
            currentIndex = maxIndex; // Loop to end
        }
        updateSlider();
    }
    
    function goToSlide(index) {
        if (index >= 0 && index <= maxIndex) {
            currentIndex = index;
            updateSlider();
        }
    }
    
    function recalculateSlider() {
        slidesToShow = getSlidesToShow();
        maxIndex = Math.max(0, slides.length - slidesToShow);
        if (currentIndex > maxIndex) {
            currentIndex = maxIndex;
        }
        updateSlider();
    }
    
    // Event listeners
    nextBtn?.addEventListener('click', nextSlide);
    prevBtn?.addEventListener('click', prevSlide);
    
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => goToSlide(index));
    });
    
    // Auto-play functionality - slower on mobile for better UX
    const autoPlayDelay = window.innerWidth <= 768 ? 6000 : 5000;
    let autoPlayInterval = setInterval(nextSlide, autoPlayDelay);
    
    // Pause on hover (desktop only)
    const slider = document.querySelector('.testimonials-slider');
    if (window.innerWidth > 768) {
        slider?.addEventListener('mouseenter', () => clearInterval(autoPlayInterval));
        slider?.addEventListener('mouseleave', () => {
            clearInterval(autoPlayInterval);
            autoPlayInterval = setInterval(nextSlide, autoPlayDelay);
        });
    }
    
    // Enhanced touch/swipe support for mobile
    let startX = 0;
    let endX = 0;
    let startY = 0;
    let endY = 0;
    let isScrolling = false;
    
    track.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        isScrolling = false;
    }, { passive: true });
    
    track.addEventListener('touchmove', (e) => {
        if (!startX || !startY) return;
        
        const currentX = e.touches[0].clientX;
        const currentY = e.touches[0].clientY;
        const diffX = Math.abs(startX - currentX);
        const diffY = Math.abs(startY - currentY);
        
        // Determine if user is scrolling vertically
        if (diffY > diffX) {
            isScrolling = true;
        }
    }, { passive: true });
    
    track.addEventListener('touchend', (e) => {
        if (isScrolling) return; // Don't interfere with vertical scrolling
        
        endX = e.changedTouches[0].clientX;
        const diff = startX - endX;
        
        // Reduced swipe sensitivity for mobile
        if (Math.abs(diff) > 30) {
            if (diff > 0) {
                nextSlide();
            } else {
                prevSlide();
            }
        }
        
        // Reset values
        startX = 0;
        startY = 0;
        isScrolling = false;
    }, { passive: true });
    
    // Handle window resize with debouncing
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(recalculateSlider, 150);
    });
    
    // Initial setup
    updateSlider();
}

document.addEventListener('DOMContentLoaded', () => {
    initializeBackToTop();
    ensureHeaderFixed();
    initializeHeroCards();
    initializeHeroParallax();
    initializeTestimonialsSlider();
    // Header auto-hide functionality removed - header will remain always visible
});
</script>

<!-- Booking CTA Section -->
<section class="py-16 bg-gradient-to-r from-redolence-green to-redolence-blue relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="medical-dots" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
                    <circle cx="30" cy="30" r="2" fill="white"/>
                </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#medical-dots)" />
        </svg>
    </div>

    <div class="max-w-4xl mx-auto px-6 text-center relative z-10">
        <div class="mb-8">
            <div class="inline-flex items-center bg-white/20 text-white px-4 py-2 rounded-full text-sm font-semibold mb-6">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                </svg>
                Ready to Transform?
            </div>
            <h2 class="text-3xl md:text-5xl font-bold text-white mb-4">
                Start Your Medical Aesthetic Journey Today
            </h2>
            <p class="text-xl text-white/90 max-w-2xl mx-auto mb-8">
                Book your personalized consultation and discover the advanced treatments that will help you achieve your aesthetic goals.
            </p>
        </div>

        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <a href="<?= getBasePath() ?>/contact" class="inline-flex items-center justify-center bg-white hover:bg-gray-100 text-redolence-green px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                </svg>
                Book Free Consultation
            </a>
            <a href="tel:+1234567890" class="inline-flex items-center justify-center bg-transparent hover:bg-white/10 text-white border-2 border-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 hover:scale-105">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                </svg>
                Call Now
            </a>
        </div>

        <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 text-white/80 text-sm">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4a1 1 0 00.707-1.707z" clip-rule="evenodd"/>
                </svg>
                Free Consultation
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4a1 1 0 00.707-1.707z" clip-rule="evenodd"/>
                </svg>
                No Commitment
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4a1 1 0 00.707-1.707z" clip-rule="evenodd"/>
                </svg>
                Expert Advice
            </div>
        </div>
    </div>
</section>

<!-- Service Details Modal -->
<div id="serviceDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center rounded-t-2xl">
            <h2 id="modalServiceName" class="text-2xl font-bold text-gray-900"></h2>
            <button onclick="closeServiceModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <!-- Treatment Badges -->
            <div id="modalBadges" class="flex flex-wrap gap-2 mb-4"></div>

            <!-- Service Image -->
            <div id="modalImageContainer" class="mb-6"></div>

            <!-- Technology Used -->
            <div id="modalTechnology" class="mb-4"></div>

            <!-- Price and Duration -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="text-sm text-gray-500 mb-1">Price</div>
                    <div id="modalPrice" class="text-2xl font-bold text-redolence-green"></div>
                </div>
                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="text-sm text-gray-500 mb-1">Duration</div>
                    <div id="modalDuration" class="text-xl font-semibold text-gray-900"></div>
                </div>
            </div>

            <!-- Session Frequency -->
            <div id="modalFrequency" class="mb-6"></div>

            <!-- Full Description -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Treatment Details</h3>
                <div id="modalDescription" class="prose prose-redolence max-w-none"></div>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-4 pt-4 border-t border-gray-200">
                <button onclick="bookViaWhatsApp(document.getElementById('modalServiceName').textContent)" class="flex-1 bg-gradient-to-r from-redolence-green to-green-600 hover:from-green-600 hover:to-green-700 text-white text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                    Book Appointment
                </button>
                <button onclick="closeServiceModal()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-6 rounded-xl font-semibold transition-all duration-300">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function openServiceModal(serviceId) {
    // Update URL with service anchor
    if (window.history && window.history.pushState) {
        const newUrl = window.location.pathname + window.location.search + '#service-' + serviceId;
        window.history.pushState({serviceId: serviceId}, '', newUrl);
    }

    // Fetch service details
    fetch(`<?= getBasePath() ?>/api/services/get.php?id=${serviceId}`)
        .then(response => response.json())
        .then(service => {
            if (service.error) {
                alert('Error loading service details');
                return;
            }

            // Populate modal content
            document.getElementById('modalServiceName').textContent = service.name;

            // Treatment badges
            const badgesContainer = document.getElementById('modalBadges');
            badgesContainer.innerHTML = '';
            if (service.featured) {
                badgesContainer.innerHTML += '<span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-3 py-1 rounded-full text-sm font-bold">⭐ Featured</span>';
            }
            if (service.popular) {
                badgesContainer.innerHTML += '<span class="bg-gradient-to-r from-red-400 to-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">🔥 Popular</span>';
            }
            if (service.new_treatment) {
                badgesContainer.innerHTML += '<span class="bg-gradient-to-r from-green-400 to-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">✨ New</span>';
            }

            // Service image
            const imageContainer = document.getElementById('modalImageContainer');
            if (service.image) {
                const imageSrc = service.image.startsWith('http') ? service.image : `<?= getBasePath() ?>/uploads/${service.image}`;
                imageContainer.innerHTML = `<img src="${imageSrc}" alt="${service.name}" class="w-full h-64 object-cover rounded-xl">`;
            } else {
                imageContainer.innerHTML = '';
            }

            // Technology
            const technologyContainer = document.getElementById('modalTechnology');
            if (service.technology_used) {
                technologyContainer.innerHTML = `
                    <div class="inline-flex items-center bg-redolence-blue/10 text-redolence-blue px-4 py-2 rounded-lg">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                        </svg>
                        ${service.technology_used}
                    </div>
                `;
            } else {
                technologyContainer.innerHTML = '';
            }

            // Price
            const priceContainer = document.getElementById('modalPrice');
            if (service.price) {
                priceContainer.textContent = new Intl.NumberFormat('en-TZ', {
                    style: 'currency',
                    currency: 'TZS',
                    minimumFractionDigits: 0
                }).format(service.price);
            } else {
                priceContainer.innerHTML = '<span class="text-redolence-blue">TSH (To be discussed)</span>';
            }

            // Duration
            const durationContainer = document.getElementById('modalDuration');
            if (service.duration) {
                durationContainer.textContent = `${service.duration} minutes`;
            } else {
                durationContainer.innerHTML = '<span class="text-gray-500">Variable duration</span>';
            }

            // Session frequency
            const frequencyContainer = document.getElementById('modalFrequency');
            if (service.session_frequency) {
                frequencyContainer.innerHTML = `
                    <div class="bg-blue-50 rounded-xl p-4">
                        <div class="text-sm text-blue-600 font-semibold mb-1">Recommended Frequency</div>
                        <div class="text-blue-900">${service.session_frequency}</div>
                    </div>
                `;
            } else {
                frequencyContainer.innerHTML = '';
            }

            // Description (display HTML properly)
            const descriptionContainer = document.getElementById('modalDescription');
            if (service.description) {
                descriptionContainer.innerHTML = service.description;
            } else {
                descriptionContainer.innerHTML = '<p class="text-gray-500">No detailed description available.</p>';
            }

            // Show modal
            document.getElementById('serviceDetailsModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading service details');
        });
}

function closeServiceModal() {
    document.getElementById('serviceDetailsModal').classList.add('hidden');

    // Remove service anchor from URL
    if (window.history && window.history.pushState) {
        const newUrl = window.location.pathname + window.location.search;
        window.history.pushState({}, '', newUrl);
    }
}

// Close modal when clicking outside
document.getElementById('serviceDetailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeServiceModal();
    }
});

// Handle direct service links from FAQ page
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a service anchor in the URL
    const hash = window.location.hash;
    if (hash && hash.startsWith('#service-')) {
        const serviceId = hash.replace('#service-', '');
        if (serviceId) {
            // Small delay to ensure page is fully loaded
            setTimeout(() => {
                openServiceModal(serviceId);
            }, 500);
        }
    }
});

// WhatsApp booking function
function bookViaWhatsApp(serviceName) {
    // WhatsApp number without the plus sign and spaces
    const whatsappNumber = '255787574355'; // Tanzanian number format
    
    // Properly encode the service name to handle special characters like &, +, etc.
    const encodedServiceName = encodeURIComponent(serviceName);
    
    // Create the message template with proper encoding
    const message = `Hello, I'd like to book the service: ${encodedServiceName}.%0A%0APlease provide me with the available dates and details.`;
    
    // Create WhatsApp URL
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    
    // Open WhatsApp in a new tab/window
    window.open(whatsappUrl, '_blank');
}
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
