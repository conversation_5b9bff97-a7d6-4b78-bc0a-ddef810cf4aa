<?php
/**
 * Service Variation Management Functions
 * Flix Salonce - PHP Version
 */

/**
 * Create a new service variation
 */
function createServiceVariation($serviceId, $data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name']) || empty($data['price']) || empty($data['duration'])) {
            return ['success' => false, 'error' => 'Name, price, and duration are required'];
        }
        
        // Validate service exists
        $service = $database->fetch("SELECT id FROM services WHERE id = ?", [$serviceId]);
        if (!$service) {
            return ['success' => false, 'error' => 'Service not found'];
        }
        
        $variationId = generateUUID();
        
        // Get next sort order
        $maxSort = $database->fetch(
            "SELECT COALESCE(MAX(sort_order), 0) as max_sort FROM service_variations WHERE service_id = ?", 
            [$serviceId]
        );
        $sortOrder = ($maxSort['max_sort'] ?? 0) + 1;
        
        $database->query(
            "INSERT INTO service_variations (id, service_id, name, description, price, duration, is_active, sort_order, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $variationId,
                $serviceId,
                cleanText($data['name']),
                cleanText($data['description'] ?? ''),
                floatval($data['price']),
                intval($data['duration']),
                isset($data['is_active']) ? 1 : 0,
                $sortOrder
            ]
        );
        
        return ['success' => true, 'id' => $variationId];
        
    } catch (Exception $e) {
        error_log("Service variation creation error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create service variation'];
    }
}

/**
 * Update an existing service variation
 */
function updateServiceVariation($variationId, $data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name']) || empty($data['price']) || empty($data['duration'])) {
            return ['success' => false, 'error' => 'Name, price, and duration are required'];
        }
        
        // Check if variation exists
        $variation = $database->fetch("SELECT * FROM service_variations WHERE id = ?", [$variationId]);
        if (!$variation) {
            return ['success' => false, 'error' => 'Service variation not found'];
        }
        
        $database->query(
            "UPDATE service_variations SET name = ?, description = ?, price = ?, duration = ?, is_active = ?, updated_at = NOW()
             WHERE id = ?",
            [
                cleanText($data['name']),
                cleanText($data['description'] ?? ''),
                floatval($data['price']),
                intval($data['duration']),
                isset($data['is_active']) ? 1 : 0,
                $variationId
            ]
        );
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Service variation update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update service variation'];
    }
}

/**
 * Delete a service variation
 */
function deleteServiceVariation($variationId) {
    global $database;
    
    try {
        // Check if variation exists
        $variation = $database->fetch("SELECT * FROM service_variations WHERE id = ?", [$variationId]);
        if (!$variation) {
            return ['success' => false, 'error' => 'Service variation not found'];
        }
        
        // Check if variation is used in any bookings
        $bookingCount = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings WHERE service_variation_id = ?", 
            [$variationId]
        )['count'];
        
        if ($bookingCount > 0) {
            return [
                'success' => false, 
                'error' => 'Cannot delete variation that has been used in bookings',
                'details' => "This variation is referenced in $bookingCount booking(s). Please deactivate it instead."
            ];
        }
        
        $database->query("DELETE FROM service_variations WHERE id = ?", [$variationId]);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Service variation deletion error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to delete service variation'];
    }
}

/**
 * Get service variation by ID
 */
function getServiceVariationById($variationId) {
    global $database;
    
    return $database->fetch("SELECT * FROM service_variations WHERE id = ?", [$variationId]);
}

/**
 * Get all variations for a service
 */
function getServiceVariations($serviceId, $activeOnly = false) {
    global $database;
    
    $whereClause = "WHERE service_id = ?";
    $params = [$serviceId];
    
    if ($activeOnly) {
        $whereClause .= " AND is_active = 1";
    }
    
    return $database->fetchAll(
        "SELECT * FROM service_variations $whereClause ORDER BY sort_order ASC, name ASC",
        $params
    );
}

/**
 * Get services with their variations
 */
function getServicesWithVariations($activeOnly = false) {
    global $database;
    
    $serviceWhere = $activeOnly ? "WHERE s.is_active = 1" : "";
    $variationWhere = $activeOnly ? "AND sv.is_active = 1" : "";
    
    $services = $database->fetchAll(
        "SELECT s.*, 
                COUNT(sv.id) as variation_count,
                GROUP_CONCAT(
                    CONCAT(sv.id, ':', sv.name, ':', sv.price, ':', sv.duration, ':', sv.is_active) 
                    ORDER BY sv.sort_order ASC, sv.name ASC 
                    SEPARATOR '|'
                ) as variations_data
         FROM services s 
         LEFT JOIN service_variations sv ON s.id = sv.service_id $variationWhere
         $serviceWhere
         GROUP BY s.id 
         ORDER BY s.name ASC"
    );
    
    // Parse variations data
    foreach ($services as &$service) {
        $service['variations'] = [];
        if ($service['variations_data']) {
            $variationsData = explode('|', $service['variations_data']);
            foreach ($variationsData as $varData) {
                $parts = explode(':', $varData);
                if (count($parts) >= 5) {
                    $service['variations'][] = [
                        'id' => $parts[0],
                        'name' => $parts[1],
                        'price' => floatval($parts[2]),
                        'duration' => intval($parts[3]),
                        'is_active' => (bool)$parts[4]
                    ];
                }
            }
        }
        unset($service['variations_data']);
    }
    
    return $services;
}

/**
 * Update variation sort order
 */
function updateVariationSortOrder($variationId, $newOrder) {
    global $database;
    
    try {
        $database->query(
            "UPDATE service_variations SET sort_order = ?, updated_at = NOW() WHERE id = ?",
            [intval($newOrder), $variationId]
        );
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Variation sort order update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update sort order'];
    }
}

/**
 * Toggle variation active status
 */
function toggleServiceVariationStatus($variationId) {
    global $database;
    
    try {
        $variation = $database->fetch("SELECT is_active FROM service_variations WHERE id = ?", [$variationId]);
        if (!$variation) {
            return ['success' => false, 'error' => 'Service variation not found'];
        }
        
        $newStatus = $variation['is_active'] ? 0 : 1;
        
        $database->query(
            "UPDATE service_variations SET is_active = ?, updated_at = NOW() WHERE id = ?",
            [$newStatus, $variationId]
        );
        
        return ['success' => true, 'new_status' => $newStatus];
        
    } catch (Exception $e) {
        error_log("Variation status toggle error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to toggle variation status'];
    }
}

/**
 * Get variation statistics
 */
function getVariationStats($serviceId = null) {
    global $database;
    
    $stats = [];
    
    $whereClause = $serviceId ? "WHERE service_id = ?" : "";
    $params = $serviceId ? [$serviceId] : [];
    
    // Total variations
    $stats['total'] = $database->fetch(
        "SELECT COUNT(*) as count FROM service_variations $whereClause", 
        $params
    )['count'];
    
    // Active variations
    $activeWhere = $serviceId ? "WHERE service_id = ? AND is_active = 1" : "WHERE is_active = 1";
    $activeParams = $serviceId ? [$serviceId] : [];
    
    $stats['active'] = $database->fetch(
        "SELECT COUNT(*) as count FROM service_variations $activeWhere", 
        $activeParams
    )['count'];
    
    // Most booked variations
    $bookingWhere = $serviceId ? "WHERE sv.service_id = ?" : "";
    $bookingParams = $serviceId ? [$serviceId] : [];
    
    $stats['most_booked'] = $database->fetchAll(
        "SELECT sv.name, s.name as service_name, COUNT(b.id) as booking_count 
         FROM service_variations sv 
         LEFT JOIN services s ON sv.service_id = s.id
         LEFT JOIN bookings b ON sv.id = b.service_variation_id 
         $bookingWhere
         GROUP BY sv.id, sv.name, s.name 
         ORDER BY booking_count DESC 
         LIMIT 5",
        $bookingParams
    );
    
    return $stats;
}
